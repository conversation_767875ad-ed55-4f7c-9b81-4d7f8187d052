2025-08-05 21:45:21,273 - INFO - 🎯 rml201801a 使用初始第2轮门槛: 60.0000
2025-08-05 21:45:21,273 - INFO - 🚀 开始分别优化模式
2025-08-05 21:45:21,273 - INFO - 目标数据集: ['rml201801a']
2025-08-05 21:45:21,273 - INFO - 每个数据集试验次数: 20
2025-08-05 21:45:21,273 - INFO - 总试验次数: 1 × 20 = 20
2025-08-05 21:45:21,273 - INFO - 结果保存目录: optimization_results_wnn_mrnn_hyperopt_separate
2025-08-05 21:45:21,273 - INFO - 
================================================================================
2025-08-05 21:45:21,273 - INFO - 📊 进度: 1/1 - 正在优化数据集: rml201801a
2025-08-05 21:45:21,273 - INFO - ================================================================================
2025-08-05 21:45:21,273 - INFO - 
🎯 开始优化数据集: rml201801a
2025-08-05 21:45:21,273 - INFO - 试验次数: 20
2025-08-05 21:45:21,764 - INFO - 🚀 阶段1: 进行前20轮试验
2025-08-05 21:45:21,797 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-05 21:45:21,797 - INFO -    - dropout: 0.29100315496856294 (数据集特定 + 通用)
2025-08-05 21:45:21,797 - INFO -    - lambda_lifting: 0.3510096866438333
2025-08-05 21:45:21,809 - INFO - 📄 临时配置文件创建: /tmp/tmp64m7_x12.yaml
2025-08-05 21:45:21,809 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.29100315496856294, 'batch_size': 256}
2025-08-05 21:45:21,809 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.29100315496856294
2025-08-05 21:45:21,809 - INFO - ✅ 最终训练参数: lambda_lifting=0.3510096866438333, batch_size=128
2025-08-05 21:45:21,809 - INFO - rml201801a Trial 0: 参数 {'dropout': 0.29100315496856294, 'lambda_lifting': 0.3510096866438333}
2025-08-05 21:45:21,993 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 0.0GB, 可用: 23.6GB
2025-08-05 21:45:21,993 - INFO - ====================================================================================================
2025-08-05 21:45:21,993 - INFO - 🚀 开始训练 - rml201801a Trial 0
2025-08-05 21:45:21,993 - INFO - ====================================================================================================
2025-08-05 21:45:21,993 - INFO - 📋 本次训练参数:
2025-08-05 21:45:21,993 - INFO -   dropout: 0.2910
  lambda_lifting: 0.3510
2025-08-05 21:45:21,993 - INFO - 
🏆 rml201801a 当前TOP参数: 暂无历史记录
2025-08-05 21:45:21,994 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-05 21:45:21,994 - INFO - ====================================================================================================
2025-08-05 21:45:21,994 - INFO - 🎯 开始训练...
2025-08-05 21:45:21,994 - INFO - ====================================================================================================
2025-08-05 21:45:21,994 - INFO - 🎯 开始训练 rml201801a Trial 0 (第1次试验)
2025-08-05 21:45:21,994 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-05 21:45:22,057 - INFO - 开始训练，配置文件: /tmp/tmp64m7_x12.yaml
2025-08-05 21:45:22,057 - INFO - 数据集类型: rml201801a
2025-08-05 21:45:22,057 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250805_214522
2025-08-05 21:45:22,072 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_214522/configs/config_backup.yaml
2025-08-05 21:45:22,072 - INFO - 使用设备: cuda
2025-08-05 21:45:22,072 - INFO - 加载数据...
2025-08-05 21:49:38,373 - INFO - 创建模型...
2025-08-05 21:49:38,903 - INFO - 可训练参数数量: 867,608
2025-08-05 21:49:38,903 - INFO - 总参数数量: 867,608
2025-08-05 21:49:38,903 - INFO - 可训练参数比例: 100.00%
2025-08-05 21:49:38,904 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-05 21:49:38,904 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 1, 'trial_number': 0}
2025-08-05 21:49:38,904 - INFO - 📊 最少训练轮数: 2
2025-08-05 21:49:38,904 - INFO - Epoch 1/400
2025-08-05 22:07:48,911 - INFO - Train Loss: 1.6926, Train Acc: 42.94%
2025-08-05 22:08:46,927 - INFO - Val Loss: 1.4328, Val Acc: 50.47%, Macro-F1: 49.93%, Kappa: 0.4832
2025-08-05 22:08:46,928 - INFO - Epoch 1 训练时间: 1148.02 秒
2025-08-05 22:08:46,988 - INFO - 保存最佳模型，验证准确率: 50.47%, Macro-F1: 49.93%, Kappa: 0.4832
2025-08-05 22:08:46,989 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_214522/models/best_model.pth
2025-08-05 22:08:46,989 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_214522/models/model_epoch_1_acc_50.47.pth
2025-08-05 22:08:46,989 - INFO - Epoch 2/400
2025-08-05 22:26:53,552 - INFO - Train Loss: 1.4133, Train Acc: 52.27%
2025-08-05 22:27:50,740 - INFO - Val Loss: 1.3862, Val Acc: 53.37%, Macro-F1: 54.87%, Kappa: 0.5134
2025-08-05 22:27:50,740 - INFO - Epoch 2 训练时间: 1143.75 秒
2025-08-05 22:27:50,740 - INFO - 🔍 第2轮门槛检查: 准确率 53.3716%, 门槛 60.0000
2025-08-05 22:27:50,740 - INFO - ⚠️ 第2轮准确率 53.3716% 未达到门槛 60.0000，提前结束训练
2025-08-05 22:27:50,740 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_214522/results/training_history.json
2025-08-05 22:27:50,741 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_214522/results/training_summary.json
2025-08-05 22:27:50,741 - INFO - 训练完成！
2025-08-05 22:27:50,741 - INFO - 最佳验证准确率: 50.47%
2025-08-05 22:27:50,741 - INFO - 最佳Macro-F1: 49.93%
2025-08-05 22:27:50,741 - INFO - 最佳Kappa: 0.4832
2025-08-05 22:27:50,741 - INFO - 总训练时间: 2291.77 秒 (38.2 分钟)
2025-08-05 22:27:50,741 - INFO - 平均每轮训练时间: 1145.89 秒
2025-08-05 22:27:50,741 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250805_214522
2025-08-05 22:27:50,741 - INFO - 🔍 第2轮准确率记录: 0.5337
2025-08-05 22:27:53,046 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-05 22:27:53,047 - INFO -    - 当前门槛: 60.0000
2025-08-05 22:27:53,047 - INFO -    - 第2轮准确率: 53.3716
2025-08-05 22:27:53,047 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-05 22:27:53,047 - INFO - 训练完成 rml201801a Trial 0, 结果: {'best_val_acc': 0.5046904315196998, 'best_val_f1': 0.4993416014804006, 'best_val_kappa': 0.4831552328901215, 'total_epochs': 2, 'total_training_time': 2291.774444103241, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5337163852407755}
2025-08-05 22:27:53,047 - INFO - rml201801a Trial 0 早停训练，使用第2轮准确率: 0.5337
2025-08-05 22:27:53,047 - INFO - 🎉 rml201801a 发现新的最佳结果! 试验 0, 准确率: 0.5337
2025-08-05 22:27:53,047 - INFO - rml201801a 最佳参数: {'dropout': 0.29100315496856294, 'lambda_lifting': 0.3510096866438333}
2025-08-05 22:27:53,371 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-05 22:27:53,371 - INFO -    - dropout: 0.6192028344071885 (数据集特定 + 通用)
2025-08-05 22:27:53,371 - INFO -    - lambda_lifting: 0.4013116754754876
2025-08-05 22:27:53,382 - INFO - 📄 临时配置文件创建: /tmp/tmpndti2_k3.yaml
2025-08-05 22:27:53,382 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.6192028344071885, 'batch_size': 256}
2025-08-05 22:27:53,382 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.6192028344071885
2025-08-05 22:27:53,382 - INFO - ✅ 最终训练参数: lambda_lifting=0.4013116754754876, batch_size=128
2025-08-05 22:27:53,382 - INFO - rml201801a Trial 1: 参数 {'dropout': 0.6192028344071885, 'lambda_lifting': 0.4013116754754876}
2025-08-05 22:27:53,499 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-05 22:27:53,499 - INFO - ====================================================================================================
2025-08-05 22:27:53,499 - INFO - 🚀 开始训练 - rml201801a Trial 1
2025-08-05 22:27:53,499 - INFO - ====================================================================================================
2025-08-05 22:27:53,499 - INFO - 📋 本次训练参数:
2025-08-05 22:27:53,499 - INFO -   dropout: 0.6192
  lambda_lifting: 0.4013
2025-08-05 22:27:53,499 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-05 22:27:53,499 - INFO -   #1 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-05 22:27:53,499 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-05 22:27:53,499 - INFO - ====================================================================================================
2025-08-05 22:27:53,499 - INFO - 🎯 开始训练...
2025-08-05 22:27:53,500 - INFO - ====================================================================================================
2025-08-05 22:27:53,500 - INFO - 🎯 开始训练 rml201801a Trial 1 (第2次试验)
2025-08-05 22:27:53,500 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-05 22:27:53,548 - INFO - 开始训练，配置文件: /tmp/tmpndti2_k3.yaml
2025-08-05 22:27:53,548 - INFO - 数据集类型: rml201801a
2025-08-05 22:27:53,548 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250805_222753
2025-08-05 22:27:53,559 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_222753/configs/config_backup.yaml
2025-08-05 22:27:53,559 - INFO - 使用设备: cuda
2025-08-05 22:27:53,559 - INFO - 加载数据...
2025-08-05 22:33:38,479 - INFO - 创建模型...
2025-08-05 22:33:38,520 - INFO - 可训练参数数量: 867,608
2025-08-05 22:33:38,520 - INFO - 总参数数量: 867,608
2025-08-05 22:33:38,520 - INFO - 可训练参数比例: 100.00%
2025-08-05 22:33:38,521 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-05 22:33:38,521 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 2, 'trial_number': 1}
2025-08-05 22:33:38,521 - INFO - 📊 最少训练轮数: 2
2025-08-05 22:33:38,521 - INFO - Epoch 1/400
2025-08-05 22:51:47,719 - INFO - Train Loss: 1.8021, Train Acc: 39.13%
2025-08-05 22:52:45,498 - INFO - Val Loss: 1.7491, Val Acc: 41.36%, Macro-F1: 40.03%, Kappa: 0.3881
2025-08-05 22:52:45,498 - INFO - Epoch 1 训练时间: 1146.98 秒
2025-08-05 22:52:45,559 - INFO - 保存最佳模型，验证准确率: 41.36%, Macro-F1: 40.03%, Kappa: 0.3881
2025-08-05 22:52:45,559 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_222753/models/best_model.pth
2025-08-05 22:52:45,559 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_222753/models/model_epoch_1_acc_41.36.pth
2025-08-05 22:52:45,559 - INFO - Epoch 2/400
2025-08-05 23:10:50,931 - INFO - Train Loss: 1.5195, Train Acc: 48.16%
2025-08-05 23:11:48,027 - INFO - Val Loss: 1.4969, Val Acc: 48.10%, Macro-F1: 49.90%, Kappa: 0.4584
2025-08-05 23:11:48,028 - INFO - Epoch 2 训练时间: 1142.47 秒
2025-08-05 23:11:48,028 - INFO - 🔍 第2轮门槛检查: 准确率 48.0986%, 门槛 60.0000
2025-08-05 23:11:48,028 - INFO - ⚠️ 第2轮准确率 48.0986% 未达到门槛 60.0000，提前结束训练
2025-08-05 23:11:48,028 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_222753/results/training_history.json
2025-08-05 23:11:48,028 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_222753/results/training_summary.json
2025-08-05 23:11:48,028 - INFO - 训练完成！
2025-08-05 23:11:48,028 - INFO - 最佳验证准确率: 41.36%
2025-08-05 23:11:48,028 - INFO - 最佳Macro-F1: 40.03%
2025-08-05 23:11:48,028 - INFO - 最佳Kappa: 0.3881
2025-08-05 23:11:48,028 - INFO - 总训练时间: 2289.45 秒 (38.2 分钟)
2025-08-05 23:11:48,028 - INFO - 平均每轮训练时间: 1144.72 秒
2025-08-05 23:11:48,029 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250805_222753
2025-08-05 23:11:48,029 - INFO - 🔍 第2轮准确率记录: 0.4810
2025-08-05 23:11:50,098 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-05 23:11:50,098 - INFO -    - 当前门槛: 60.0000
2025-08-05 23:11:50,099 - INFO -    - 第2轮准确率: 48.0986
2025-08-05 23:11:50,099 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-05 23:11:50,099 - INFO - 训练完成 rml201801a Trial 1, 结果: {'best_val_acc': 0.4135605586825099, 'best_val_f1': 0.4003107947966909, 'best_val_kappa': 0.38806319166870595, 'total_epochs': 2, 'total_training_time': 2289.4453353881836, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.4809855117781947}
2025-08-05 23:11:50,099 - INFO - rml201801a Trial 1 早停训练，使用第2轮准确率: 0.4810
2025-08-05 23:11:50,099 - INFO - 
📊 rml201801a 当前TOP 2 参数组合排名:
2025-08-05 23:11:50,099 - INFO - --------------------------------------------------------------------------------
2025-08-05 23:11:50,100 - INFO - #1 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-05 23:11:50,100 - INFO - #2 | 试验1 | 准确率:0.4810 | dropout=0.6192028344071885, lambda_lifting=0.4013116754754876
2025-08-05 23:11:50,100 - INFO - --------------------------------------------------------------------------------
2025-08-05 23:11:50,664 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-05 23:11:50,664 - INFO -    - dropout: 0.33975852577567 (数据集特定 + 通用)
2025-08-05 23:11:50,664 - INFO -    - lambda_lifting: 0.4449883445254538
2025-08-05 23:11:50,679 - INFO - 📄 临时配置文件创建: /tmp/tmp7soss6ga.yaml
2025-08-05 23:11:50,679 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.33975852577567, 'batch_size': 256}
2025-08-05 23:11:50,679 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.33975852577567
2025-08-05 23:11:50,679 - INFO - ✅ 最终训练参数: lambda_lifting=0.4449883445254538, batch_size=128
2025-08-05 23:11:50,679 - INFO - rml201801a Trial 2: 参数 {'dropout': 0.33975852577567, 'lambda_lifting': 0.4449883445254538}
2025-08-05 23:11:50,810 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-05 23:11:50,810 - INFO - ====================================================================================================
2025-08-05 23:11:50,811 - INFO - 🚀 开始训练 - rml201801a Trial 2
2025-08-05 23:11:50,811 - INFO - ====================================================================================================
2025-08-05 23:11:50,811 - INFO - 📋 本次训练参数:
2025-08-05 23:11:50,811 - INFO -   dropout: 0.3398
  lambda_lifting: 0.4450
2025-08-05 23:11:50,811 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-05 23:11:50,811 - INFO -   #1 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-05 23:11:50,811 - INFO -   #2 | Trial1 | 准确率:0.4810 | dropout=0.6192, lambda_lifting=0.4013
2025-08-05 23:11:50,811 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-05 23:11:50,811 - INFO - ====================================================================================================
2025-08-05 23:11:50,811 - INFO - 🎯 开始训练...
2025-08-05 23:11:50,811 - INFO - ====================================================================================================
2025-08-05 23:11:50,811 - INFO - 🎯 开始训练 rml201801a Trial 2 (第3次试验)
2025-08-05 23:11:50,811 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-05 23:11:50,878 - INFO - 开始训练，配置文件: /tmp/tmp7soss6ga.yaml
2025-08-05 23:11:50,878 - INFO - 数据集类型: rml201801a
2025-08-05 23:11:50,878 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250805_231150
2025-08-05 23:11:50,893 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_231150/configs/config_backup.yaml
2025-08-05 23:11:50,893 - INFO - 使用设备: cuda
2025-08-05 23:11:50,894 - INFO - 加载数据...
2025-08-05 23:17:46,003 - INFO - 创建模型...
2025-08-05 23:17:46,051 - INFO - 可训练参数数量: 867,608
2025-08-05 23:17:46,051 - INFO - 总参数数量: 867,608
2025-08-05 23:17:46,051 - INFO - 可训练参数比例: 100.00%
2025-08-05 23:17:46,053 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-05 23:17:46,053 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 3, 'trial_number': 2}
2025-08-05 23:17:46,053 - INFO - 📊 最少训练轮数: 2
2025-08-05 23:17:46,053 - INFO - Epoch 1/400
2025-08-05 23:35:56,958 - INFO - Train Loss: 1.7095, Train Acc: 42.83%
2025-08-05 23:36:55,174 - INFO - Val Loss: 1.4680, Val Acc: 48.92%, Macro-F1: 47.35%, Kappa: 0.4670
2025-08-05 23:36:55,175 - INFO - Epoch 1 训练时间: 1149.12 秒
2025-08-05 23:36:55,237 - INFO - 保存最佳模型，验证准确率: 48.92%, Macro-F1: 47.35%, Kappa: 0.4670
2025-08-05 23:36:55,237 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_231150/models/best_model.pth
2025-08-05 23:36:55,237 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_231150/models/model_epoch_1_acc_48.92.pth
2025-08-05 23:36:55,237 - INFO - Epoch 2/400
2025-08-05 23:55:02,481 - INFO - Train Loss: 1.4298, Train Acc: 51.97%
2025-08-05 23:55:59,682 - INFO - Val Loss: 1.4385, Val Acc: 51.95%, Macro-F1: 52.96%, Kappa: 0.4986
2025-08-05 23:55:59,683 - INFO - Epoch 2 训练时间: 1144.45 秒
2025-08-05 23:55:59,683 - INFO - 🔍 第2轮门槛检查: 准确率 51.9457%, 门槛 60.0000
2025-08-05 23:55:59,683 - INFO - ⚠️ 第2轮准确率 51.9457% 未达到门槛 60.0000，提前结束训练
2025-08-05 23:55:59,683 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_231150/results/training_history.json
2025-08-05 23:55:59,683 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_231150/results/training_summary.json
2025-08-05 23:55:59,683 - INFO - 训练完成！
2025-08-05 23:55:59,683 - INFO - 最佳验证准确率: 48.92%
2025-08-05 23:55:59,684 - INFO - 最佳Macro-F1: 47.35%
2025-08-05 23:55:59,684 - INFO - 最佳Kappa: 0.4670
2025-08-05 23:55:59,684 - INFO - 总训练时间: 2293.57 秒 (38.2 分钟)
2025-08-05 23:55:59,684 - INFO - 平均每轮训练时间: 1146.78 秒
2025-08-05 23:55:59,684 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250805_231150
2025-08-05 23:55:59,684 - INFO - 🔍 第2轮准确率记录: 0.5195
2025-08-05 23:56:02,656 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-05 23:56:02,657 - INFO -    - 当前门槛: 60.0000
2025-08-05 23:56:02,657 - INFO -    - 第2轮准确率: 51.9457
2025-08-05 23:56:02,657 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-05 23:56:02,657 - INFO - 训练完成 rml201801a Trial 2, 结果: {'best_val_acc': 0.48918334375651445, 'best_val_f1': 0.47345148097554185, 'best_val_kappa': 0.46697392391984116, 'total_epochs': 2, 'total_training_time': 2293.5682106018066, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5194574734208881}
2025-08-05 23:56:02,657 - INFO - rml201801a Trial 2 早停训练，使用第2轮准确率: 0.5195
2025-08-05 23:56:02,658 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-05 23:56:02,658 - INFO - --------------------------------------------------------------------------------
2025-08-05 23:56:02,658 - INFO - #1 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-05 23:56:02,658 - INFO - #2 | 试验2 | 准确率:0.5195 | dropout=0.33975852577567, lambda_lifting=0.4449883445254538
2025-08-05 23:56:02,658 - INFO - #3 | 试验1 | 准确率:0.4810 | dropout=0.6192028344071885, lambda_lifting=0.4013116754754876
2025-08-05 23:56:02,658 - INFO - --------------------------------------------------------------------------------
2025-08-05 23:56:03,021 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-05 23:56:03,021 - INFO -    - dropout: 0.22965918483841627 (数据集特定 + 通用)
2025-08-05 23:56:03,021 - INFO -    - lambda_lifting: 0.5553878352193506
2025-08-05 23:56:03,032 - INFO - 📄 临时配置文件创建: /tmp/tmpkics_rni.yaml
2025-08-05 23:56:03,032 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.22965918483841627, 'batch_size': 256}
2025-08-05 23:56:03,032 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.22965918483841627
2025-08-05 23:56:03,032 - INFO - ✅ 最终训练参数: lambda_lifting=0.5553878352193506, batch_size=128
2025-08-05 23:56:03,032 - INFO - rml201801a Trial 3: 参数 {'dropout': 0.22965918483841627, 'lambda_lifting': 0.5553878352193506}
2025-08-05 23:56:03,127 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-05 23:56:03,127 - INFO - ====================================================================================================
2025-08-05 23:56:03,127 - INFO - 🚀 开始训练 - rml201801a Trial 3
2025-08-05 23:56:03,127 - INFO - ====================================================================================================
2025-08-05 23:56:03,127 - INFO - 📋 本次训练参数:
2025-08-05 23:56:03,127 - INFO -   dropout: 0.2297
  lambda_lifting: 0.5554
2025-08-05 23:56:03,127 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-05 23:56:03,127 - INFO -   #1 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-05 23:56:03,127 - INFO -   #2 | Trial2 | 准确率:0.5195 | dropout=0.3398, lambda_lifting=0.4450
2025-08-05 23:56:03,127 - INFO -   #3 | Trial1 | 准确率:0.4810 | dropout=0.6192, lambda_lifting=0.4013
2025-08-05 23:56:03,127 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-05 23:56:03,127 - INFO - ====================================================================================================
2025-08-05 23:56:03,127 - INFO - 🎯 开始训练...
2025-08-05 23:56:03,127 - INFO - ====================================================================================================
2025-08-05 23:56:03,127 - INFO - 🎯 开始训练 rml201801a Trial 3 (第4次试验)
2025-08-05 23:56:03,128 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-05 23:56:03,175 - INFO - 开始训练，配置文件: /tmp/tmpkics_rni.yaml
2025-08-05 23:56:03,175 - INFO - 数据集类型: rml201801a
2025-08-05 23:56:03,175 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250805_235603
2025-08-05 23:56:03,186 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_235603/configs/config_backup.yaml
2025-08-05 23:56:03,186 - INFO - 使用设备: cuda
2025-08-05 23:56:03,186 - INFO - 加载数据...
2025-08-05 23:59:58,895 - INFO - 创建模型...
2025-08-05 23:59:58,937 - INFO - 可训练参数数量: 867,608
2025-08-05 23:59:58,937 - INFO - 总参数数量: 867,608
2025-08-05 23:59:58,937 - INFO - 可训练参数比例: 100.00%
2025-08-05 23:59:58,938 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-05 23:59:58,939 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 4, 'trial_number': 3}
2025-08-05 23:59:58,939 - INFO - 📊 最少训练轮数: 2
2025-08-05 23:59:58,939 - INFO - Epoch 1/400
2025-08-06 00:18:08,893 - INFO - Train Loss: 1.7041, Train Acc: 43.20%
2025-08-06 00:19:06,812 - INFO - Val Loss: 1.4182, Val Acc: 51.18%, Macro-F1: 50.49%, Kappa: 0.4906
2025-08-06 00:19:06,813 - INFO - Epoch 1 训练时间: 1147.87 秒
2025-08-06 00:19:06,874 - INFO - 保存最佳模型，验证准确率: 51.18%, Macro-F1: 50.49%, Kappa: 0.4906
2025-08-06 00:19:06,874 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_235603/models/best_model.pth
2025-08-06 00:19:06,874 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_235603/models/model_epoch_1_acc_51.18.pth
2025-08-06 00:19:06,874 - INFO - Epoch 2/400
2025-08-06 00:37:13,771 - INFO - Train Loss: 1.4118, Train Acc: 52.53%
2025-08-06 00:38:10,971 - INFO - Val Loss: 1.4024, Val Acc: 52.03%, Macro-F1: 53.07%, Kappa: 0.4994
2025-08-06 00:38:10,972 - INFO - Epoch 2 训练时间: 1144.10 秒
2025-08-06 00:38:10,972 - INFO - 🔍 第2轮门槛检查: 准确率 52.0270%, 门槛 60.0000
2025-08-06 00:38:10,972 - INFO - ⚠️ 第2轮准确率 52.0270% 未达到门槛 60.0000，提前结束训练
2025-08-06 00:38:10,972 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_235603/results/training_history.json
2025-08-06 00:38:10,972 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250805_235603/results/training_summary.json
2025-08-06 00:38:10,972 - INFO - 训练完成！
2025-08-06 00:38:10,972 - INFO - 最佳验证准确率: 51.18%
2025-08-06 00:38:10,973 - INFO - 最佳Macro-F1: 50.49%
2025-08-06 00:38:10,973 - INFO - 最佳Kappa: 0.4906
2025-08-06 00:38:10,973 - INFO - 总训练时间: 2291.97 秒 (38.2 分钟)
2025-08-06 00:38:10,973 - INFO - 平均每轮训练时间: 1145.99 秒
2025-08-06 00:38:10,973 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250805_235603
2025-08-06 00:38:10,973 - INFO - 🔍 第2轮准确率记录: 0.5203
2025-08-06 00:38:13,362 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 00:38:13,363 - INFO -    - 当前门槛: 60.0000
2025-08-06 00:38:13,363 - INFO -    - 第2轮准确率: 52.0270
2025-08-06 00:38:13,363 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 00:38:13,363 - INFO - 训练完成 rml201801a Trial 3, 结果: {'best_val_acc': 0.5118094642484886, 'best_val_f1': 0.5048786225304307, 'best_val_kappa': 0.4905837887810317, 'total_epochs': 2, 'total_training_time': 2291.971033811569, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5202704815509693}
2025-08-06 00:38:13,363 - INFO - rml201801a Trial 3 早停训练，使用第2轮准确率: 0.5203
2025-08-06 00:38:13,364 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 00:38:13,364 - INFO - --------------------------------------------------------------------------------
2025-08-06 00:38:13,364 - INFO - #1 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 00:38:13,364 - INFO - #2 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 00:38:13,364 - INFO - #3 | 试验2 | 准确率:0.5195 | dropout=0.33975852577567, lambda_lifting=0.4449883445254538
2025-08-06 00:38:13,364 - INFO - --------------------------------------------------------------------------------
2025-08-06 00:38:13,720 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 00:38:13,720 - INFO -    - dropout: 0.17041612078998825 (数据集特定 + 通用)
2025-08-06 00:38:13,720 - INFO -    - lambda_lifting: 0.10770851894149434
2025-08-06 00:38:13,731 - INFO - 📄 临时配置文件创建: /tmp/tmpv5l3rwox.yaml
2025-08-06 00:38:13,731 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.17041612078998825, 'batch_size': 256}
2025-08-06 00:38:13,731 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.17041612078998825
2025-08-06 00:38:13,731 - INFO - ✅ 最终训练参数: lambda_lifting=0.10770851894149434, batch_size=128
2025-08-06 00:38:13,731 - INFO - rml201801a Trial 4: 参数 {'dropout': 0.17041612078998825, 'lambda_lifting': 0.10770851894149434}
2025-08-06 00:38:13,893 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 00:38:13,893 - INFO - ====================================================================================================
2025-08-06 00:38:13,893 - INFO - 🚀 开始训练 - rml201801a Trial 4
2025-08-06 00:38:13,893 - INFO - ====================================================================================================
2025-08-06 00:38:13,893 - INFO - 📋 本次训练参数:
2025-08-06 00:38:13,893 - INFO -   dropout: 0.1704
  lambda_lifting: 0.1077
2025-08-06 00:38:13,893 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 00:38:13,893 - INFO -   #1 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 00:38:13,893 - INFO -   #2 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 00:38:13,893 - INFO -   #3 | Trial2 | 准确率:0.5195 | dropout=0.3398, lambda_lifting=0.4450
2025-08-06 00:38:13,893 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 00:38:13,893 - INFO - ====================================================================================================
2025-08-06 00:38:13,894 - INFO - 🎯 开始训练...
2025-08-06 00:38:13,894 - INFO - ====================================================================================================
2025-08-06 00:38:13,894 - INFO - 🎯 开始训练 rml201801a Trial 4 (第5次试验)
2025-08-06 00:38:13,894 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 00:38:13,941 - INFO - 开始训练，配置文件: /tmp/tmpv5l3rwox.yaml
2025-08-06 00:38:13,942 - INFO - 数据集类型: rml201801a
2025-08-06 00:38:13,942 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_003813
2025-08-06 00:38:13,952 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_003813/configs/config_backup.yaml
2025-08-06 00:38:13,953 - INFO - 使用设备: cuda
2025-08-06 00:38:13,953 - INFO - 加载数据...
2025-08-06 00:45:56,836 - INFO - 创建模型...
2025-08-06 00:45:57,033 - INFO - 可训练参数数量: 867,608
2025-08-06 00:45:57,033 - INFO - 总参数数量: 867,608
2025-08-06 00:45:57,033 - INFO - 可训练参数比例: 100.00%
2025-08-06 00:45:57,034 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 00:45:57,034 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 5, 'trial_number': 4}
2025-08-06 00:45:57,034 - INFO - 📊 最少训练轮数: 2
2025-08-06 00:45:57,035 - INFO - Epoch 1/400
2025-08-06 01:04:07,420 - INFO - Train Loss: 1.6164, Train Acc: 45.14%
2025-08-06 01:05:05,665 - INFO - Val Loss: 1.4288, Val Acc: 50.74%, Macro-F1: 50.59%, Kappa: 0.4860
2025-08-06 01:05:05,666 - INFO - Epoch 1 训练时间: 1148.63 秒
2025-08-06 01:05:05,728 - INFO - 保存最佳模型，验证准确率: 50.74%, Macro-F1: 50.59%, Kappa: 0.4860
2025-08-06 01:05:05,729 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_003813/models/best_model.pth
2025-08-06 01:05:05,729 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_003813/models/model_epoch_1_acc_50.74.pth
2025-08-06 01:05:05,729 - INFO - Epoch 2/400
2025-08-06 01:23:12,619 - INFO - Train Loss: 1.3596, Train Acc: 54.49%
2025-08-06 01:24:09,870 - INFO - Val Loss: 1.3121, Val Acc: 56.14%, Macro-F1: 57.12%, Kappa: 0.5424
2025-08-06 01:24:09,871 - INFO - Epoch 2 训练时间: 1144.14 秒
2025-08-06 01:24:09,871 - INFO - 🔍 第2轮门槛检查: 准确率 56.1421%, 门槛 60.0000
2025-08-06 01:24:09,871 - INFO - ⚠️ 第2轮准确率 56.1421% 未达到门槛 60.0000，提前结束训练
2025-08-06 01:24:09,871 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_003813/results/training_history.json
2025-08-06 01:24:09,871 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_003813/results/training_summary.json
2025-08-06 01:24:09,871 - INFO - 训练完成！
2025-08-06 01:24:09,871 - INFO - 最佳验证准确率: 50.74%
2025-08-06 01:24:09,871 - INFO - 最佳Macro-F1: 50.59%
2025-08-06 01:24:09,871 - INFO - 最佳Kappa: 0.4860
2025-08-06 01:24:09,871 - INFO - 总训练时间: 2292.77 秒 (38.2 分钟)
2025-08-06 01:24:09,872 - INFO - 平均每轮训练时间: 1146.39 秒
2025-08-06 01:24:09,872 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_003813
2025-08-06 01:24:09,872 - INFO - 🔍 第2轮准确率记录: 0.5614
2025-08-06 01:24:12,755 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 01:24:12,755 - INFO -    - 当前门槛: 60.0000
2025-08-06 01:24:12,755 - INFO -    - 第2轮准确率: 56.1421
2025-08-06 01:24:12,755 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 01:24:12,756 - INFO - 训练完成 rml201801a Trial 4, 结果: {'best_val_acc': 0.5073874296435271, 'best_val_f1': 0.5059082118938868, 'best_val_kappa': 0.4859694918019414, 'total_epochs': 2, 'total_training_time': 2292.7732117176056, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5614212007504691}
2025-08-06 01:24:12,756 - INFO - rml201801a Trial 4 早停训练，使用第2轮准确率: 0.5614
2025-08-06 01:24:12,757 - INFO - 🎉 rml201801a 发现新的最佳结果! 试验 4, 准确率: 0.5614
2025-08-06 01:24:12,757 - INFO - rml201801a 最佳参数: {'dropout': 0.17041612078998825, 'lambda_lifting': 0.10770851894149434}
2025-08-06 01:24:12,757 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 01:24:12,757 - INFO - --------------------------------------------------------------------------------
2025-08-06 01:24:12,758 - INFO - #1 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 01:24:12,758 - INFO - #2 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 01:24:12,758 - INFO - #3 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 01:24:12,758 - INFO - --------------------------------------------------------------------------------
2025-08-06 01:24:13,075 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 01:24:13,076 - INFO -    - dropout: 0.4524924390885482 (数据集特定 + 通用)
2025-08-06 01:24:13,076 - INFO -    - lambda_lifting: 0.22565302006075094
2025-08-06 01:24:13,086 - INFO - 📄 临时配置文件创建: /tmp/tmp7sxrbv5_.yaml
2025-08-06 01:24:13,086 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.4524924390885482, 'batch_size': 256}
2025-08-06 01:24:13,086 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.4524924390885482
2025-08-06 01:24:13,086 - INFO - ✅ 最终训练参数: lambda_lifting=0.22565302006075094, batch_size=128
2025-08-06 01:24:13,086 - INFO - rml201801a Trial 5: 参数 {'dropout': 0.4524924390885482, 'lambda_lifting': 0.22565302006075094}
2025-08-06 01:24:13,188 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 01:24:13,188 - INFO - ====================================================================================================
2025-08-06 01:24:13,188 - INFO - 🚀 开始训练 - rml201801a Trial 5
2025-08-06 01:24:13,188 - INFO - ====================================================================================================
2025-08-06 01:24:13,188 - INFO - 📋 本次训练参数:
2025-08-06 01:24:13,188 - INFO -   dropout: 0.4525
  lambda_lifting: 0.2257
2025-08-06 01:24:13,188 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 01:24:13,188 - INFO -   #1 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 01:24:13,188 - INFO -   #2 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 01:24:13,188 - INFO -   #3 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 01:24:13,188 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 01:24:13,188 - INFO - ====================================================================================================
2025-08-06 01:24:13,188 - INFO - 🎯 开始训练...
2025-08-06 01:24:13,188 - INFO - ====================================================================================================
2025-08-06 01:24:13,188 - INFO - 🎯 开始训练 rml201801a Trial 5 (第6次试验)
2025-08-06 01:24:13,188 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 01:24:13,236 - INFO - 开始训练，配置文件: /tmp/tmp7sxrbv5_.yaml
2025-08-06 01:24:13,236 - INFO - 数据集类型: rml201801a
2025-08-06 01:24:13,236 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_012413
2025-08-06 01:24:13,247 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_012413/configs/config_backup.yaml
2025-08-06 01:24:13,247 - INFO - 使用设备: cuda
2025-08-06 01:24:13,247 - INFO - 加载数据...
2025-08-06 01:26:46,488 - INFO - 创建模型...
2025-08-06 01:26:46,535 - INFO - 可训练参数数量: 867,608
2025-08-06 01:26:46,536 - INFO - 总参数数量: 867,608
2025-08-06 01:26:46,536 - INFO - 可训练参数比例: 100.00%
2025-08-06 01:26:46,537 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 01:26:46,537 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 6, 'trial_number': 5}
2025-08-06 01:26:46,537 - INFO - 📊 最少训练轮数: 2
2025-08-06 01:26:46,537 - INFO - Epoch 1/400
2025-08-06 01:44:57,025 - INFO - Train Loss: 1.7051, Train Acc: 42.49%
2025-08-06 01:45:55,269 - INFO - Val Loss: 1.5352, Val Acc: 46.95%, Macro-F1: 46.65%, Kappa: 0.4464
2025-08-06 01:45:55,269 - INFO - Epoch 1 训练时间: 1148.73 秒
2025-08-06 01:45:55,330 - INFO - 保存最佳模型，验证准确率: 46.95%, Macro-F1: 46.65%, Kappa: 0.4464
2025-08-06 01:45:55,331 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_012413/models/best_model.pth
2025-08-06 01:45:55,331 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_012413/models/model_epoch_1_acc_46.95.pth
2025-08-06 01:45:55,331 - INFO - Epoch 2/400
2025-08-06 02:04:02,137 - INFO - Train Loss: 1.4411, Train Acc: 51.18%
2025-08-06 02:04:59,411 - INFO - Val Loss: 1.4372, Val Acc: 50.89%, Macro-F1: 51.73%, Kappa: 0.4875
2025-08-06 02:04:59,411 - INFO - Epoch 2 训练时间: 1144.08 秒
2025-08-06 02:04:59,411 - INFO - 🔍 第2轮门槛检查: 准确率 50.8886%, 门槛 60.0000
2025-08-06 02:04:59,411 - INFO - ⚠️ 第2轮准确率 50.8886% 未达到门槛 60.0000，提前结束训练
2025-08-06 02:04:59,412 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_012413/results/training_history.json
2025-08-06 02:04:59,412 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_012413/results/training_summary.json
2025-08-06 02:04:59,412 - INFO - 训练完成！
2025-08-06 02:04:59,412 - INFO - 最佳验证准确率: 46.95%
2025-08-06 02:04:59,412 - INFO - 最佳Macro-F1: 46.65%
2025-08-06 02:04:59,412 - INFO - 最佳Kappa: 0.4464
2025-08-06 02:04:59,412 - INFO - 总训练时间: 2292.81 秒 (38.2 分钟)
2025-08-06 02:04:59,412 - INFO - 平均每轮训练时间: 1146.41 秒
2025-08-06 02:04:59,412 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_012413
2025-08-06 02:04:59,412 - INFO - 🔍 第2轮准确率记录: 0.5089
2025-08-06 02:05:02,422 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 02:05:02,422 - INFO -    - 当前门槛: 60.0000
2025-08-06 02:05:02,422 - INFO -    - 第2轮准确率: 50.8886
2025-08-06 02:05:02,422 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 02:05:02,422 - INFO - 训练完成 rml201801a Trial 5, 结果: {'best_val_acc': 0.46945747342088806, 'best_val_f1': 0.46654929531483985, 'best_val_kappa': 0.44639040704788324, 'total_epochs': 2, 'total_training_time': 2292.8128066062927, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5088857619345424}
2025-08-06 02:05:02,422 - INFO - rml201801a Trial 5 早停训练，使用第2轮准确率: 0.5089
2025-08-06 02:05:02,423 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 02:05:02,423 - INFO - --------------------------------------------------------------------------------
2025-08-06 02:05:02,423 - INFO - #1 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 02:05:02,424 - INFO - #2 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 02:05:02,424 - INFO - #3 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 02:05:02,424 - INFO - --------------------------------------------------------------------------------
2025-08-06 02:05:02,771 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 02:05:02,771 - INFO -    - dropout: 0.545344740309106 (数据集特定 + 通用)
2025-08-06 02:05:02,771 - INFO -    - lambda_lifting: 0.20034056983204745
2025-08-06 02:05:02,782 - INFO - 📄 临时配置文件创建: /tmp/tmpeq1k7fg6.yaml
2025-08-06 02:05:02,782 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.545344740309106, 'batch_size': 256}
2025-08-06 02:05:02,782 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.545344740309106
2025-08-06 02:05:02,782 - INFO - ✅ 最终训练参数: lambda_lifting=0.20034056983204745, batch_size=128
2025-08-06 02:05:02,782 - INFO - rml201801a Trial 6: 参数 {'dropout': 0.545344740309106, 'lambda_lifting': 0.20034056983204745}
2025-08-06 02:05:02,897 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 02:05:02,897 - INFO - ====================================================================================================
2025-08-06 02:05:02,897 - INFO - 🚀 开始训练 - rml201801a Trial 6
2025-08-06 02:05:02,897 - INFO - ====================================================================================================
2025-08-06 02:05:02,897 - INFO - 📋 本次训练参数:
2025-08-06 02:05:02,897 - INFO -   dropout: 0.5453
  lambda_lifting: 0.2003
2025-08-06 02:05:02,897 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 02:05:02,897 - INFO -   #1 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 02:05:02,897 - INFO -   #2 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 02:05:02,898 - INFO -   #3 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 02:05:02,898 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 02:05:02,898 - INFO - ====================================================================================================
2025-08-06 02:05:02,898 - INFO - 🎯 开始训练...
2025-08-06 02:05:02,898 - INFO - ====================================================================================================
2025-08-06 02:05:02,898 - INFO - 🎯 开始训练 rml201801a Trial 6 (第7次试验)
2025-08-06 02:05:02,898 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 02:05:02,945 - INFO - 开始训练，配置文件: /tmp/tmpeq1k7fg6.yaml
2025-08-06 02:05:02,945 - INFO - 数据集类型: rml201801a
2025-08-06 02:05:02,945 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_020502
2025-08-06 02:05:02,956 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_020502/configs/config_backup.yaml
2025-08-06 02:05:02,956 - INFO - 使用设备: cuda
2025-08-06 02:05:02,957 - INFO - 加载数据...
2025-08-06 02:07:14,479 - INFO - 创建模型...
2025-08-06 02:07:14,534 - INFO - 可训练参数数量: 867,608
2025-08-06 02:07:14,534 - INFO - 总参数数量: 867,608
2025-08-06 02:07:14,534 - INFO - 可训练参数比例: 100.00%
2025-08-06 02:07:14,535 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 02:07:14,535 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 7, 'trial_number': 6}
2025-08-06 02:07:14,535 - INFO - 📊 最少训练轮数: 2
2025-08-06 02:07:14,535 - INFO - Epoch 1/400
2025-08-06 02:25:24,497 - INFO - Train Loss: 1.7315, Train Acc: 41.52%
2025-08-06 02:26:23,185 - INFO - Val Loss: 1.5904, Val Acc: 45.18%, Macro-F1: 43.26%, Kappa: 0.4279
2025-08-06 02:26:23,185 - INFO - Epoch 1 训练时间: 1148.65 秒
2025-08-06 02:26:23,248 - INFO - 保存最佳模型，验证准确率: 45.18%, Macro-F1: 43.26%, Kappa: 0.4279
2025-08-06 02:26:23,248 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_020502/models/best_model.pth
2025-08-06 02:26:23,248 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_020502/models/model_epoch_1_acc_45.18.pth
2025-08-06 02:26:23,248 - INFO - Epoch 2/400
2025-08-06 02:44:30,061 - INFO - Train Loss: 1.4683, Train Acc: 50.08%
2025-08-06 02:45:27,446 - INFO - Val Loss: 1.4553, Val Acc: 49.48%, Macro-F1: 49.73%, Kappa: 0.4728
2025-08-06 02:45:27,446 - INFO - Epoch 2 训练时间: 1144.20 秒
2025-08-06 02:45:27,446 - INFO - 🔍 第2轮门槛检查: 准确率 49.4773%, 门槛 60.0000
2025-08-06 02:45:27,446 - INFO - ⚠️ 第2轮准确率 49.4773% 未达到门槛 60.0000，提前结束训练
2025-08-06 02:45:27,446 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_020502/results/training_history.json
2025-08-06 02:45:27,447 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_020502/results/training_summary.json
2025-08-06 02:45:27,447 - INFO - 训练完成！
2025-08-06 02:45:27,447 - INFO - 最佳验证准确率: 45.18%
2025-08-06 02:45:27,447 - INFO - 最佳Macro-F1: 43.26%
2025-08-06 02:45:27,447 - INFO - 最佳Kappa: 0.4279
2025-08-06 02:45:27,447 - INFO - 总训练时间: 2292.85 秒 (38.2 分钟)
2025-08-06 02:45:27,447 - INFO - 平均每轮训练时间: 1146.42 秒
2025-08-06 02:45:27,447 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_020502
2025-08-06 02:45:27,447 - INFO - 🔍 第2轮准确率记录: 0.4948
2025-08-06 02:45:30,600 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 02:45:30,601 - INFO -    - 当前门槛: 60.0000
2025-08-06 02:45:30,601 - INFO -    - 第2轮准确率: 49.4773
2025-08-06 02:45:30,601 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 02:45:30,601 - INFO - 训练完成 rml201801a Trial 6, 结果: {'best_val_acc': 0.45176151761517613, 'best_val_f1': 0.43259851126122967, 'best_val_kappa': 0.4279250618593142, 'total_epochs': 2, 'total_training_time': 2292.847448348999, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.4947727746508235}
2025-08-06 02:45:30,601 - INFO - rml201801a Trial 6 早停训练，使用第2轮准确率: 0.4948
2025-08-06 02:45:30,602 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 02:45:30,602 - INFO - --------------------------------------------------------------------------------
2025-08-06 02:45:30,602 - INFO - #1 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 02:45:30,602 - INFO - #2 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 02:45:30,602 - INFO - #3 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 02:45:30,602 - INFO - --------------------------------------------------------------------------------
2025-08-06 02:45:30,994 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 02:45:30,994 - INFO -    - dropout: 0.5900964442675751 (数据集特定 + 通用)
2025-08-06 02:45:30,994 - INFO -    - lambda_lifting: 0.35253770774999277
2025-08-06 02:45:31,004 - INFO - 📄 临时配置文件创建: /tmp/tmpb9moa0q2.yaml
2025-08-06 02:45:31,004 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.5900964442675751, 'batch_size': 256}
2025-08-06 02:45:31,004 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.5900964442675751
2025-08-06 02:45:31,004 - INFO - ✅ 最终训练参数: lambda_lifting=0.35253770774999277, batch_size=128
2025-08-06 02:45:31,004 - INFO - rml201801a Trial 7: 参数 {'dropout': 0.5900964442675751, 'lambda_lifting': 0.35253770774999277}
2025-08-06 02:45:31,162 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 02:45:31,162 - INFO - ====================================================================================================
2025-08-06 02:45:31,162 - INFO - 🚀 开始训练 - rml201801a Trial 7
2025-08-06 02:45:31,162 - INFO - ====================================================================================================
2025-08-06 02:45:31,162 - INFO - 📋 本次训练参数:
2025-08-06 02:45:31,162 - INFO -   dropout: 0.5901
  lambda_lifting: 0.3525
2025-08-06 02:45:31,162 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 02:45:31,162 - INFO -   #1 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 02:45:31,162 - INFO -   #2 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 02:45:31,162 - INFO -   #3 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 02:45:31,162 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 02:45:31,162 - INFO - ====================================================================================================
2025-08-06 02:45:31,162 - INFO - 🎯 开始训练...
2025-08-06 02:45:31,162 - INFO - ====================================================================================================
2025-08-06 02:45:31,162 - INFO - 🎯 开始训练 rml201801a Trial 7 (第8次试验)
2025-08-06 02:45:31,162 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 02:45:31,210 - INFO - 开始训练，配置文件: /tmp/tmpb9moa0q2.yaml
2025-08-06 02:45:31,210 - INFO - 数据集类型: rml201801a
2025-08-06 02:45:31,210 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_024531
2025-08-06 02:45:31,221 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_024531/configs/config_backup.yaml
2025-08-06 02:45:31,221 - INFO - 使用设备: cuda
2025-08-06 02:45:31,221 - INFO - 加载数据...
2025-08-06 02:50:05,990 - INFO - 创建模型...
2025-08-06 02:50:06,036 - INFO - 可训练参数数量: 867,608
2025-08-06 02:50:06,036 - INFO - 总参数数量: 867,608
2025-08-06 02:50:06,036 - INFO - 可训练参数比例: 100.00%
2025-08-06 02:50:06,038 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 02:50:06,038 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 8, 'trial_number': 7}
2025-08-06 02:50:06,038 - INFO - 📊 最少训练轮数: 2
2025-08-06 02:50:06,038 - INFO - Epoch 1/400
2025-08-06 03:08:16,158 - INFO - Train Loss: 1.7833, Train Acc: 39.70%
2025-08-06 03:09:14,420 - INFO - Val Loss: 1.6525, Val Acc: 42.54%, Macro-F1: 41.68%, Kappa: 0.4005
2025-08-06 03:09:14,421 - INFO - Epoch 1 训练时间: 1148.38 秒
2025-08-06 03:09:14,496 - INFO - 保存最佳模型，验证准确率: 42.54%, Macro-F1: 41.68%, Kappa: 0.4005
2025-08-06 03:09:14,496 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_024531/models/best_model.pth
2025-08-06 03:09:14,496 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_024531/models/model_epoch_1_acc_42.54.pth
2025-08-06 03:09:14,496 - INFO - Epoch 2/400
2025-08-06 03:27:20,672 - INFO - Train Loss: 1.4986, Train Acc: 49.07%
2025-08-06 03:28:17,920 - INFO - Val Loss: 1.5058, Val Acc: 48.14%, Macro-F1: 48.46%, Kappa: 0.4588
2025-08-06 03:28:17,920 - INFO - Epoch 2 训练时间: 1143.42 秒
2025-08-06 03:28:17,920 - INFO - 🔍 第2轮门槛检查: 准确率 48.1356%, 门槛 60.0000
2025-08-06 03:28:17,920 - INFO - ⚠️ 第2轮准确率 48.1356% 未达到门槛 60.0000，提前结束训练
2025-08-06 03:28:17,921 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_024531/results/training_history.json
2025-08-06 03:28:17,921 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_024531/results/training_summary.json
2025-08-06 03:28:17,921 - INFO - 训练完成！
2025-08-06 03:28:17,921 - INFO - 最佳验证准确率: 42.54%
2025-08-06 03:28:17,921 - INFO - 最佳Macro-F1: 41.68%
2025-08-06 03:28:17,921 - INFO - 最佳Kappa: 0.4005
2025-08-06 03:28:17,921 - INFO - 总训练时间: 2291.81 秒 (38.2 分钟)
2025-08-06 03:28:17,921 - INFO - 平均每轮训练时间: 1145.90 秒
2025-08-06 03:28:17,921 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_024531
2025-08-06 03:28:17,921 - INFO - 🔍 第2轮准确率记录: 0.4814
2025-08-06 03:28:20,585 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 03:28:20,585 - INFO -    - 当前门槛: 60.0000
2025-08-06 03:28:20,585 - INFO -    - 第2轮准确率: 48.1356
2025-08-06 03:28:20,586 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 03:28:20,586 - INFO - 训练完成 rml201801a Trial 7, 结果: {'best_val_acc': 0.4254377736085053, 'best_val_f1': 0.41684883737731254, 'best_val_kappa': 0.40045680724365773, 'total_epochs': 2, 'total_training_time': 2291.80627989769, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.4813555347091933}
2025-08-06 03:28:20,586 - INFO - rml201801a Trial 7 早停训练，使用第2轮准确率: 0.4814
2025-08-06 03:28:20,587 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 03:28:20,587 - INFO - --------------------------------------------------------------------------------
2025-08-06 03:28:20,587 - INFO - #1 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 03:28:20,587 - INFO - #2 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 03:28:20,587 - INFO - #3 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 03:28:20,587 - INFO - --------------------------------------------------------------------------------
2025-08-06 03:28:20,925 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 03:28:20,925 - INFO -    - dropout: 0.6055348952589817 (数据集特定 + 通用)
2025-08-06 03:28:20,925 - INFO -    - lambda_lifting: 0.27566253362281073
2025-08-06 03:28:20,935 - INFO - 📄 临时配置文件创建: /tmp/tmp_3s3po4g.yaml
2025-08-06 03:28:20,935 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.6055348952589817, 'batch_size': 256}
2025-08-06 03:28:20,935 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.6055348952589817
2025-08-06 03:28:20,936 - INFO - ✅ 最终训练参数: lambda_lifting=0.27566253362281073, batch_size=128
2025-08-06 03:28:20,936 - INFO - rml201801a Trial 8: 参数 {'dropout': 0.6055348952589817, 'lambda_lifting': 0.27566253362281073}
2025-08-06 03:28:21,040 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 03:28:21,040 - INFO - ====================================================================================================
2025-08-06 03:28:21,040 - INFO - 🚀 开始训练 - rml201801a Trial 8
2025-08-06 03:28:21,040 - INFO - ====================================================================================================
2025-08-06 03:28:21,040 - INFO - 📋 本次训练参数:
2025-08-06 03:28:21,040 - INFO -   dropout: 0.6055
  lambda_lifting: 0.2757
2025-08-06 03:28:21,040 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 03:28:21,040 - INFO -   #1 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 03:28:21,041 - INFO -   #2 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 03:28:21,041 - INFO -   #3 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 03:28:21,041 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 03:28:21,041 - INFO - ====================================================================================================
2025-08-06 03:28:21,041 - INFO - 🎯 开始训练...
2025-08-06 03:28:21,041 - INFO - ====================================================================================================
2025-08-06 03:28:21,041 - INFO - 🎯 开始训练 rml201801a Trial 8 (第9次试验)
2025-08-06 03:28:21,041 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 03:28:21,088 - INFO - 开始训练，配置文件: /tmp/tmp_3s3po4g.yaml
2025-08-06 03:28:21,088 - INFO - 数据集类型: rml201801a
2025-08-06 03:28:21,088 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_032821
2025-08-06 03:28:21,099 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_032821/configs/config_backup.yaml
2025-08-06 03:28:21,099 - INFO - 使用设备: cuda
2025-08-06 03:28:21,099 - INFO - 加载数据...
2025-08-06 03:33:06,486 - INFO - 创建模型...
2025-08-06 03:33:06,530 - INFO - 可训练参数数量: 867,608
2025-08-06 03:33:06,531 - INFO - 总参数数量: 867,608
2025-08-06 03:33:06,531 - INFO - 可训练参数比例: 100.00%
2025-08-06 03:33:06,532 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 03:33:06,532 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 9, 'trial_number': 8}
2025-08-06 03:33:06,532 - INFO - 📊 最少训练轮数: 2
2025-08-06 03:33:06,532 - INFO - Epoch 1/400
2025-08-06 03:51:16,403 - INFO - Train Loss: 1.7745, Train Acc: 40.25%
2025-08-06 03:52:14,444 - INFO - Val Loss: 1.6115, Val Acc: 44.37%, Macro-F1: 44.65%, Kappa: 0.4195
2025-08-06 03:52:14,445 - INFO - Epoch 1 训练时间: 1147.91 秒
2025-08-06 03:52:14,506 - INFO - 保存最佳模型，验证准确率: 44.37%, Macro-F1: 44.65%, Kappa: 0.4195
2025-08-06 03:52:14,506 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_032821/models/best_model.pth
2025-08-06 03:52:14,506 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_032821/models/model_epoch_1_acc_44.37.pth
2025-08-06 03:52:14,506 - INFO - Epoch 2/400
2025-08-06 04:10:21,316 - INFO - Train Loss: 1.4985, Train Acc: 49.16%
2025-08-06 04:11:18,426 - INFO - Val Loss: 1.6799, Val Acc: 44.64%, Macro-F1: 44.02%, Kappa: 0.4223
2025-08-06 04:11:18,427 - INFO - Epoch 2 训练时间: 1143.92 秒
2025-08-06 04:11:18,427 - INFO - 🔍 第2轮门槛检查: 准确率 44.6354%, 门槛 60.0000
2025-08-06 04:11:18,427 - INFO - ⚠️ 第2轮准确率 44.6354% 未达到门槛 60.0000，提前结束训练
2025-08-06 04:11:18,427 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_032821/results/training_history.json
2025-08-06 04:11:18,427 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_032821/results/training_summary.json
2025-08-06 04:11:18,427 - INFO - 训练完成！
2025-08-06 04:11:18,427 - INFO - 最佳验证准确率: 44.37%
2025-08-06 04:11:18,427 - INFO - 最佳Macro-F1: 44.65%
2025-08-06 04:11:18,427 - INFO - 最佳Kappa: 0.4195
2025-08-06 04:11:18,427 - INFO - 总训练时间: 2291.83 秒 (38.2 分钟)
2025-08-06 04:11:18,427 - INFO - 平均每轮训练时间: 1145.92 秒
2025-08-06 04:11:18,427 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_032821
2025-08-06 04:11:18,427 - INFO - 🔍 第2轮准确率记录: 0.4464
2025-08-06 04:11:21,155 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 04:11:21,155 - INFO -    - 当前门槛: 60.0000
2025-08-06 04:11:21,155 - INFO -    - 第2轮准确率: 44.6354
2025-08-06 04:11:21,155 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 04:11:21,155 - INFO - 训练完成 rml201801a Trial 8, 结果: {'best_val_acc': 0.4436522826766729, 'best_val_f1': 0.44654705321346944, 'best_val_kappa': 0.4194632514887022, 'total_epochs': 2, 'total_training_time': 2291.832747936249, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.4463544923910778}
2025-08-06 04:11:21,155 - INFO - rml201801a Trial 8 早停训练，使用第2轮准确率: 0.4464
2025-08-06 04:11:21,157 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 04:11:21,157 - INFO - --------------------------------------------------------------------------------
2025-08-06 04:11:21,157 - INFO - #1 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 04:11:21,157 - INFO - #2 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 04:11:21,157 - INFO - #3 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 04:11:21,157 - INFO - --------------------------------------------------------------------------------
2025-08-06 04:11:21,486 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 04:11:21,486 - INFO -    - dropout: 0.24588965130477117 (数据集特定 + 通用)
2025-08-06 04:11:21,486 - INFO -    - lambda_lifting: 0.38777172859103726
2025-08-06 04:11:21,496 - INFO - 📄 临时配置文件创建: /tmp/tmp2e5v1j35.yaml
2025-08-06 04:11:21,497 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.24588965130477117, 'batch_size': 256}
2025-08-06 04:11:21,497 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.24588965130477117
2025-08-06 04:11:21,497 - INFO - ✅ 最终训练参数: lambda_lifting=0.38777172859103726, batch_size=128
2025-08-06 04:11:21,497 - INFO - rml201801a Trial 9: 参数 {'dropout': 0.24588965130477117, 'lambda_lifting': 0.38777172859103726}
2025-08-06 04:11:21,604 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 04:11:21,604 - INFO - ====================================================================================================
2025-08-06 04:11:21,604 - INFO - 🚀 开始训练 - rml201801a Trial 9
2025-08-06 04:11:21,604 - INFO - ====================================================================================================
2025-08-06 04:11:21,604 - INFO - 📋 本次训练参数:
2025-08-06 04:11:21,604 - INFO -   dropout: 0.2459
  lambda_lifting: 0.3878
2025-08-06 04:11:21,604 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 04:11:21,604 - INFO -   #1 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 04:11:21,604 - INFO -   #2 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 04:11:21,604 - INFO -   #3 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 04:11:21,604 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 04:11:21,604 - INFO - ====================================================================================================
2025-08-06 04:11:21,605 - INFO - 🎯 开始训练...
2025-08-06 04:11:21,605 - INFO - ====================================================================================================
2025-08-06 04:11:21,605 - INFO - 🎯 开始训练 rml201801a Trial 9 (第10次试验)
2025-08-06 04:11:21,605 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 04:11:21,652 - INFO - 开始训练，配置文件: /tmp/tmp2e5v1j35.yaml
2025-08-06 04:11:21,652 - INFO - 数据集类型: rml201801a
2025-08-06 04:11:21,652 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_041121
2025-08-06 04:11:21,663 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_041121/configs/config_backup.yaml
2025-08-06 04:11:21,663 - INFO - 使用设备: cuda
2025-08-06 04:11:21,663 - INFO - 加载数据...
2025-08-06 04:15:29,793 - INFO - 创建模型...
2025-08-06 04:15:29,837 - INFO - 可训练参数数量: 867,608
2025-08-06 04:15:29,838 - INFO - 总参数数量: 867,608
2025-08-06 04:15:29,838 - INFO - 可训练参数比例: 100.00%
2025-08-06 04:15:29,839 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 04:15:29,839 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 10, 'trial_number': 9}
2025-08-06 04:15:29,839 - INFO - 📊 最少训练轮数: 2
2025-08-06 04:15:29,839 - INFO - Epoch 1/400
2025-08-06 04:33:39,397 - INFO - Train Loss: 1.6829, Train Acc: 43.68%
2025-08-06 04:34:37,495 - INFO - Val Loss: 1.4016, Val Acc: 51.79%, Macro-F1: 52.51%, Kappa: 0.4970
2025-08-06 04:34:37,496 - INFO - Epoch 1 训练时间: 1147.66 秒
2025-08-06 04:34:37,557 - INFO - 保存最佳模型，验证准确率: 51.79%, Macro-F1: 52.51%, Kappa: 0.4970
2025-08-06 04:34:37,557 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_041121/models/best_model.pth
2025-08-06 04:34:37,557 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_041121/models/model_epoch_1_acc_51.79.pth
2025-08-06 04:34:37,557 - INFO - Epoch 2/400
2025-08-06 04:52:43,695 - INFO - Train Loss: 1.4045, Train Acc: 53.02%
2025-08-06 04:53:41,001 - INFO - Val Loss: 1.4183, Val Acc: 51.48%, Macro-F1: 52.17%, Kappa: 0.4937
2025-08-06 04:53:41,001 - INFO - Epoch 2 训练时间: 1143.44 秒
2025-08-06 04:53:41,001 - INFO - 🔍 第2轮门槛检查: 准确率 51.4775%, 门槛 60.0000
2025-08-06 04:53:41,001 - INFO - ⚠️ 第2轮准确率 51.4775% 未达到门槛 60.0000，提前结束训练
2025-08-06 04:53:41,002 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_041121/results/training_history.json
2025-08-06 04:53:41,002 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_041121/results/training_summary.json
2025-08-06 04:53:41,002 - INFO - 训练完成！
2025-08-06 04:53:41,002 - INFO - 最佳验证准确率: 51.79%
2025-08-06 04:53:41,002 - INFO - 最佳Macro-F1: 52.51%
2025-08-06 04:53:41,002 - INFO - 最佳Kappa: 0.4970
2025-08-06 04:53:41,002 - INFO - 总训练时间: 2291.10 秒 (38.2 分钟)
2025-08-06 04:53:41,002 - INFO - 平均每轮训练时间: 1145.55 秒
2025-08-06 04:53:41,002 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_041121
2025-08-06 04:53:41,002 - INFO - 🔍 第2轮准确率记录: 0.5148
2025-08-06 04:53:43,867 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 04:53:43,868 - INFO -    - 当前门槛: 60.0000
2025-08-06 04:53:43,868 - INFO -    - 第2轮准确率: 51.4775
2025-08-06 04:53:43,868 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 04:53:43,868 - INFO - 训练完成 rml201801a Trial 9, 结果: {'best_val_acc': 0.517930477381697, 'best_val_f1': 0.5250588379838629, 'best_val_kappa': 0.4969709329200316, 'total_epochs': 2, 'total_training_time': 2291.10076713562, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5147748592870545}
2025-08-06 04:53:43,868 - INFO - rml201801a Trial 9 早停训练，使用第2轮准确率: 0.5148
2025-08-06 04:53:43,869 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 04:53:43,869 - INFO - --------------------------------------------------------------------------------
2025-08-06 04:53:43,869 - INFO - #1 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 04:53:43,869 - INFO - #2 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 04:53:43,869 - INFO - #3 | 试验3 | 准确率:0.5203 | dropout=0.22965918483841627, lambda_lifting=0.5553878352193506
2025-08-06 04:53:43,869 - INFO - --------------------------------------------------------------------------------
2025-08-06 04:53:44,186 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 04:53:44,186 - INFO -    - dropout: 0.14440589784927163 (数据集特定 + 通用)
2025-08-06 04:53:44,186 - INFO -    - lambda_lifting: 0.015854139739503323
2025-08-06 04:53:44,196 - INFO - 📄 临时配置文件创建: /tmp/tmpdi9v1ewq.yaml
2025-08-06 04:53:44,196 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.14440589784927163, 'batch_size': 256}
2025-08-06 04:53:44,197 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.14440589784927163
2025-08-06 04:53:44,197 - INFO - ✅ 最终训练参数: lambda_lifting=0.015854139739503323, batch_size=128
2025-08-06 04:53:44,197 - INFO - rml201801a Trial 10: 参数 {'dropout': 0.14440589784927163, 'lambda_lifting': 0.015854139739503323}
2025-08-06 04:53:44,289 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 04:53:44,289 - INFO - ====================================================================================================
2025-08-06 04:53:44,289 - INFO - 🚀 开始训练 - rml201801a Trial 10
2025-08-06 04:53:44,289 - INFO - ====================================================================================================
2025-08-06 04:53:44,289 - INFO - 📋 本次训练参数:
2025-08-06 04:53:44,289 - INFO -   dropout: 0.1444
  lambda_lifting: 0.0159
2025-08-06 04:53:44,289 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 04:53:44,289 - INFO -   #1 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 04:53:44,289 - INFO -   #2 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 04:53:44,289 - INFO -   #3 | Trial3 | 准确率:0.5203 | dropout=0.2297, lambda_lifting=0.5554
2025-08-06 04:53:44,289 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 04:53:44,289 - INFO - ====================================================================================================
2025-08-06 04:53:44,289 - INFO - 🎯 开始训练...
2025-08-06 04:53:44,289 - INFO - ====================================================================================================
2025-08-06 04:53:44,289 - INFO - 🎯 开始训练 rml201801a Trial 10 (第11次试验)
2025-08-06 04:53:44,289 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 04:53:44,337 - INFO - 开始训练，配置文件: /tmp/tmpdi9v1ewq.yaml
2025-08-06 04:53:44,337 - INFO - 数据集类型: rml201801a
2025-08-06 04:53:44,337 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_045344
2025-08-06 04:53:44,348 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_045344/configs/config_backup.yaml
2025-08-06 04:53:44,348 - INFO - 使用设备: cuda
2025-08-06 04:53:44,348 - INFO - 加载数据...
2025-08-06 04:55:58,542 - INFO - 创建模型...
2025-08-06 04:55:58,579 - INFO - 可训练参数数量: 867,608
2025-08-06 04:55:58,579 - INFO - 总参数数量: 867,608
2025-08-06 04:55:58,579 - INFO - 可训练参数比例: 100.00%
2025-08-06 04:55:58,580 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 04:55:58,580 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 11, 'trial_number': 10}
2025-08-06 04:55:58,580 - INFO - 📊 最少训练轮数: 2
2025-08-06 04:55:58,580 - INFO - Epoch 1/400
2025-08-06 05:14:07,039 - INFO - Train Loss: 1.5573, Train Acc: 46.38%
2025-08-06 05:15:04,795 - INFO - Val Loss: 1.3810, Val Acc: 52.79%, Macro-F1: 53.40%, Kappa: 0.5073
2025-08-06 05:15:04,795 - INFO - Epoch 1 训练时间: 1146.22 秒
2025-08-06 05:15:04,856 - INFO - 保存最佳模型，验证准确率: 52.79%, Macro-F1: 53.40%, Kappa: 0.5073
2025-08-06 05:15:04,857 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_045344/models/best_model.pth
2025-08-06 05:15:04,857 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_045344/models/model_epoch_1_acc_52.79.pth
2025-08-06 05:15:04,857 - INFO - Epoch 2/400
2025-08-06 05:33:11,012 - INFO - Train Loss: 1.3103, Train Acc: 55.68%
2025-08-06 05:34:08,199 - INFO - Val Loss: 1.2868, Val Acc: 56.59%, Macro-F1: 58.05%, Kappa: 0.5470
2025-08-06 05:34:08,200 - INFO - Epoch 2 训练时间: 1143.34 秒
2025-08-06 05:34:08,200 - INFO - 🔍 第2轮门槛检查: 准确率 56.5869%, 门槛 60.0000
2025-08-06 05:34:08,200 - INFO - ⚠️ 第2轮准确率 56.5869% 未达到门槛 60.0000，提前结束训练
2025-08-06 05:34:08,201 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_045344/results/training_history.json
2025-08-06 05:34:08,201 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_045344/results/training_summary.json
2025-08-06 05:34:08,201 - INFO - 训练完成！
2025-08-06 05:34:08,201 - INFO - 最佳验证准确率: 52.79%
2025-08-06 05:34:08,201 - INFO - 最佳Macro-F1: 53.40%
2025-08-06 05:34:08,201 - INFO - 最佳Kappa: 0.5073
2025-08-06 05:34:08,201 - INFO - 总训练时间: 2289.56 秒 (38.2 分钟)
2025-08-06 05:34:08,201 - INFO - 平均每轮训练时间: 1144.78 秒
2025-08-06 05:34:08,201 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_045344
2025-08-06 05:34:08,201 - INFO - 🔍 第2轮准确率记录: 0.5659
2025-08-06 05:34:10,438 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 05:34:10,438 - INFO -    - 当前门槛: 60.0000
2025-08-06 05:34:10,439 - INFO -    - 第2轮准确率: 56.5869
2025-08-06 05:34:10,439 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 05:34:10,439 - INFO - 训练完成 rml201801a Trial 10, 结果: {'best_val_acc': 0.5278559516364395, 'best_val_f1': 0.533979629108302, 'best_val_kappa': 0.507327949533676, 'total_epochs': 2, 'total_training_time': 2289.558680534363, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5658692933083177}
2025-08-06 05:34:10,439 - INFO - rml201801a Trial 10 早停训练，使用第2轮准确率: 0.5659
2025-08-06 05:34:10,439 - INFO - 🎉 rml201801a 发现新的最佳结果! 试验 10, 准确率: 0.5659
2025-08-06 05:34:10,439 - INFO - rml201801a 最佳参数: {'dropout': 0.14440589784927163, 'lambda_lifting': 0.015854139739503323}
2025-08-06 05:34:10,440 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 05:34:10,440 - INFO - --------------------------------------------------------------------------------
2025-08-06 05:34:10,441 - INFO - #1 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 05:34:10,441 - INFO - #2 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 05:34:10,441 - INFO - #3 | 试验0 | 准确率:0.5337 | dropout=0.29100315496856294, lambda_lifting=0.3510096866438333
2025-08-06 05:34:10,441 - INFO - --------------------------------------------------------------------------------
2025-08-06 05:34:10,907 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 05:34:10,907 - INFO -    - dropout: 0.11085351225993631 (数据集特定 + 通用)
2025-08-06 05:34:10,907 - INFO -    - lambda_lifting: 0.012639165448392015
2025-08-06 05:34:10,918 - INFO - 📄 临时配置文件创建: /tmp/tmp6qb9g39r.yaml
2025-08-06 05:34:10,918 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.11085351225993631, 'batch_size': 256}
2025-08-06 05:34:10,918 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.11085351225993631
2025-08-06 05:34:10,918 - INFO - ✅ 最终训练参数: lambda_lifting=0.012639165448392015, batch_size=128
2025-08-06 05:34:10,918 - INFO - rml201801a Trial 11: 参数 {'dropout': 0.11085351225993631, 'lambda_lifting': 0.012639165448392015}
2025-08-06 05:34:11,077 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 05:34:11,077 - INFO - ====================================================================================================
2025-08-06 05:34:11,077 - INFO - 🚀 开始训练 - rml201801a Trial 11
2025-08-06 05:34:11,077 - INFO - ====================================================================================================
2025-08-06 05:34:11,078 - INFO - 📋 本次训练参数:
2025-08-06 05:34:11,078 - INFO -   dropout: 0.1109
  lambda_lifting: 0.0126
2025-08-06 05:34:11,078 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 05:34:11,078 - INFO -   #1 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 05:34:11,078 - INFO -   #2 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 05:34:11,078 - INFO -   #3 | Trial0 | 准确率:0.5337 | dropout=0.2910, lambda_lifting=0.3510
2025-08-06 05:34:11,078 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 05:34:11,078 - INFO - ====================================================================================================
2025-08-06 05:34:11,078 - INFO - 🎯 开始训练...
2025-08-06 05:34:11,078 - INFO - ====================================================================================================
2025-08-06 05:34:11,078 - INFO - 🎯 开始训练 rml201801a Trial 11 (第12次试验)
2025-08-06 05:34:11,078 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 05:34:11,126 - INFO - 开始训练，配置文件: /tmp/tmp6qb9g39r.yaml
2025-08-06 05:34:11,126 - INFO - 数据集类型: rml201801a
2025-08-06 05:34:11,126 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_053411
2025-08-06 05:34:11,137 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_053411/configs/config_backup.yaml
2025-08-06 05:34:11,137 - INFO - 使用设备: cuda
2025-08-06 05:34:11,137 - INFO - 加载数据...
2025-08-06 05:36:26,396 - INFO - 创建模型...
2025-08-06 05:36:26,456 - INFO - 可训练参数数量: 867,608
2025-08-06 05:36:26,456 - INFO - 总参数数量: 867,608
2025-08-06 05:36:26,456 - INFO - 可训练参数比例: 100.00%
2025-08-06 05:36:26,458 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 05:36:26,458 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 12, 'trial_number': 11}
2025-08-06 05:36:26,458 - INFO - 📊 最少训练轮数: 2
2025-08-06 05:36:26,458 - INFO - Epoch 1/400
2025-08-06 05:54:34,183 - INFO - Train Loss: 1.5393, Train Acc: 47.15%
2025-08-06 05:55:31,862 - INFO - Val Loss: 1.3865, Val Acc: 52.56%, Macro-F1: 52.82%, Kappa: 0.5050
2025-08-06 05:55:31,863 - INFO - Epoch 1 训练时间: 1145.40 秒
2025-08-06 05:55:31,924 - INFO - 保存最佳模型，验证准确率: 52.56%, Macro-F1: 52.82%, Kappa: 0.5050
2025-08-06 05:55:31,924 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_053411/models/best_model.pth
2025-08-06 05:55:31,924 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_053411/models/model_epoch_1_acc_52.56.pth
2025-08-06 05:55:31,924 - INFO - Epoch 2/400
2025-08-06 06:13:36,700 - INFO - Train Loss: 1.2944, Train Acc: 56.22%
2025-08-06 06:14:33,906 - INFO - Val Loss: 1.2878, Val Acc: 56.61%, Macro-F1: 58.99%, Kappa: 0.5473
2025-08-06 06:14:33,907 - INFO - Epoch 2 训练时间: 1141.98 秒
2025-08-06 06:14:33,907 - INFO - 🔍 第2轮门槛检查: 准确率 56.6117%, 门槛 60.0000
2025-08-06 06:14:33,907 - INFO - ⚠️ 第2轮准确率 56.6117% 未达到门槛 60.0000，提前结束训练
2025-08-06 06:14:33,907 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_053411/results/training_history.json
2025-08-06 06:14:33,907 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_053411/results/training_summary.json
2025-08-06 06:14:33,907 - INFO - 训练完成！
2025-08-06 06:14:33,907 - INFO - 最佳验证准确率: 52.56%
2025-08-06 06:14:33,907 - INFO - 最佳Macro-F1: 52.82%
2025-08-06 06:14:33,907 - INFO - 最佳Kappa: 0.5050
2025-08-06 06:14:33,907 - INFO - 总训练时间: 2287.39 秒 (38.1 分钟)
2025-08-06 06:14:33,907 - INFO - 平均每轮训练时间: 1143.69 秒
2025-08-06 06:14:33,907 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_053411
2025-08-06 06:14:33,907 - INFO - 🔍 第2轮准确率记录: 0.5661
2025-08-06 06:14:36,474 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 06:14:36,474 - INFO -    - 当前门槛: 60.0000
2025-08-06 06:14:36,474 - INFO -    - 第2轮准确率: 56.6117
2025-08-06 06:14:36,474 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 06:14:36,474 - INFO - 训练完成 rml201801a Trial 11, 结果: {'best_val_acc': 0.5256436314363144, 'best_val_f1': 0.5282432911003794, 'best_val_kappa': 0.5050194414987628, 'total_epochs': 2, 'total_training_time': 2287.386987686157, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5661168438607462}
2025-08-06 06:14:36,474 - INFO - rml201801a Trial 11 早停训练，使用第2轮准确率: 0.5661
2025-08-06 06:14:36,475 - INFO - 🎉 rml201801a 发现新的最佳结果! 试验 11, 准确率: 0.5661
2025-08-06 06:14:36,475 - INFO - rml201801a 最佳参数: {'dropout': 0.11085351225993631, 'lambda_lifting': 0.012639165448392015}
2025-08-06 06:14:36,476 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 06:14:36,476 - INFO - --------------------------------------------------------------------------------
2025-08-06 06:14:36,476 - INFO - #1 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 06:14:36,476 - INFO - #2 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 06:14:36,476 - INFO - #3 | 试验4 | 准确率:0.5614 | dropout=0.17041612078998825, lambda_lifting=0.10770851894149434
2025-08-06 06:14:36,476 - INFO - --------------------------------------------------------------------------------
2025-08-06 06:14:36,798 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 06:14:36,799 - INFO -    - dropout: 0.10172189671003123 (数据集特定 + 通用)
2025-08-06 06:14:36,799 - INFO -    - lambda_lifting: 0.013577754816239018
2025-08-06 06:14:36,809 - INFO - 📄 临时配置文件创建: /tmp/tmpnphc2ig8.yaml
2025-08-06 06:14:36,810 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.10172189671003123, 'batch_size': 256}
2025-08-06 06:14:36,810 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.10172189671003123
2025-08-06 06:14:36,810 - INFO - ✅ 最终训练参数: lambda_lifting=0.013577754816239018, batch_size=128
2025-08-06 06:14:36,810 - INFO - rml201801a Trial 12: 参数 {'dropout': 0.10172189671003123, 'lambda_lifting': 0.013577754816239018}
2025-08-06 06:14:36,903 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 06:14:36,903 - INFO - ====================================================================================================
2025-08-06 06:14:36,903 - INFO - 🚀 开始训练 - rml201801a Trial 12
2025-08-06 06:14:36,903 - INFO - ====================================================================================================
2025-08-06 06:14:36,903 - INFO - 📋 本次训练参数:
2025-08-06 06:14:36,903 - INFO -   dropout: 0.1017
  lambda_lifting: 0.0136
2025-08-06 06:14:36,903 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 06:14:36,903 - INFO -   #1 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 06:14:36,903 - INFO -   #2 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 06:14:36,903 - INFO -   #3 | Trial4 | 准确率:0.5614 | dropout=0.1704, lambda_lifting=0.1077
2025-08-06 06:14:36,903 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 06:14:36,903 - INFO - ====================================================================================================
2025-08-06 06:14:36,903 - INFO - 🎯 开始训练...
2025-08-06 06:14:36,903 - INFO - ====================================================================================================
2025-08-06 06:14:36,903 - INFO - 🎯 开始训练 rml201801a Trial 12 (第13次试验)
2025-08-06 06:14:36,904 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 06:14:36,952 - INFO - 开始训练，配置文件: /tmp/tmpnphc2ig8.yaml
2025-08-06 06:14:36,952 - INFO - 数据集类型: rml201801a
2025-08-06 06:14:36,952 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_061436
2025-08-06 06:14:36,962 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_061436/configs/config_backup.yaml
2025-08-06 06:14:36,963 - INFO - 使用设备: cuda
2025-08-06 06:14:36,963 - INFO - 加载数据...
2025-08-06 06:16:50,695 - INFO - 创建模型...
2025-08-06 06:16:50,735 - INFO - 可训练参数数量: 867,608
2025-08-06 06:16:50,735 - INFO - 总参数数量: 867,608
2025-08-06 06:16:50,735 - INFO - 可训练参数比例: 100.00%
2025-08-06 06:16:50,736 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 06:16:50,736 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 13, 'trial_number': 12}
2025-08-06 06:16:50,736 - INFO - 📊 最少训练轮数: 2
2025-08-06 06:16:50,736 - INFO - Epoch 1/400
2025-08-06 06:34:58,998 - INFO - Train Loss: 1.5367, Train Acc: 47.27%
2025-08-06 06:35:56,859 - INFO - Val Loss: 1.3779, Val Acc: 53.08%, Macro-F1: 54.64%, Kappa: 0.5104
2025-08-06 06:35:56,860 - INFO - Epoch 1 训练时间: 1146.12 秒
2025-08-06 06:35:56,922 - INFO - 保存最佳模型，验证准确率: 53.08%, Macro-F1: 54.64%, Kappa: 0.5104
2025-08-06 06:35:56,922 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_061436/models/best_model.pth
2025-08-06 06:35:56,922 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_061436/models/model_epoch_1_acc_53.08.pth
2025-08-06 06:35:56,922 - INFO - Epoch 2/400
2025-08-06 06:54:02,970 - INFO - Train Loss: 1.2920, Train Acc: 56.35%
2025-08-06 06:55:00,112 - INFO - Val Loss: 1.2646, Val Acc: 57.45%, Macro-F1: 59.27%, Kappa: 0.5560
2025-08-06 06:55:00,112 - INFO - Epoch 2 训练时间: 1143.19 秒
2025-08-06 06:55:00,112 - INFO - 🔍 第2轮门槛检查: 准确率 57.4476%, 门槛 60.0000
2025-08-06 06:55:00,112 - INFO - ⚠️ 第2轮准确率 57.4476% 未达到门槛 60.0000，提前结束训练
2025-08-06 06:55:00,113 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_061436/results/training_history.json
2025-08-06 06:55:00,113 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_061436/results/training_summary.json
2025-08-06 06:55:00,113 - INFO - 训练完成！
2025-08-06 06:55:00,113 - INFO - 最佳验证准确率: 53.08%
2025-08-06 06:55:00,113 - INFO - 最佳Macro-F1: 54.64%
2025-08-06 06:55:00,113 - INFO - 最佳Kappa: 0.5104
2025-08-06 06:55:00,113 - INFO - 总训练时间: 2289.31 秒 (38.2 分钟)
2025-08-06 06:55:00,113 - INFO - 平均每轮训练时间: 1144.66 秒
2025-08-06 06:55:00,113 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_061436
2025-08-06 06:55:00,113 - INFO - 🔍 第2轮准确率记录: 0.5745
2025-08-06 06:55:02,582 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 06:55:02,583 - INFO -    - 当前门槛: 60.0000
2025-08-06 06:55:02,583 - INFO -    - 第2轮准确率: 57.4476
2025-08-06 06:55:02,583 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 06:55:02,583 - INFO - 训练完成 rml201801a Trial 12, 结果: {'best_val_acc': 0.5307770481550969, 'best_val_f1': 0.546436191966398, 'best_val_kappa': 0.5103760502487968, 'total_epochs': 2, 'total_training_time': 2289.3132309913635, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5744762351469669}
2025-08-06 06:55:02,583 - INFO - rml201801a Trial 12 早停训练，使用第2轮准确率: 0.5745
2025-08-06 06:55:02,583 - INFO - 🎉 rml201801a 发现新的最佳结果! 试验 12, 准确率: 0.5745
2025-08-06 06:55:02,583 - INFO - rml201801a 最佳参数: {'dropout': 0.10172189671003123, 'lambda_lifting': 0.013577754816239018}
2025-08-06 06:55:02,585 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 06:55:02,585 - INFO - --------------------------------------------------------------------------------
2025-08-06 06:55:02,585 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 06:55:02,585 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 06:55:02,585 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 06:55:02,585 - INFO - --------------------------------------------------------------------------------
2025-08-06 06:55:03,023 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 06:55:03,023 - INFO -    - dropout: 0.13077394844648713 (数据集特定 + 通用)
2025-08-06 06:55:03,023 - INFO -    - lambda_lifting: 0.01365782966030925
2025-08-06 06:55:03,034 - INFO - 📄 临时配置文件创建: /tmp/tmps148ymk9.yaml
2025-08-06 06:55:03,034 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.13077394844648713, 'batch_size': 256}
2025-08-06 06:55:03,034 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.13077394844648713
2025-08-06 06:55:03,034 - INFO - ✅ 最终训练参数: lambda_lifting=0.01365782966030925, batch_size=128
2025-08-06 06:55:03,034 - INFO - rml201801a Trial 13: 参数 {'dropout': 0.13077394844648713, 'lambda_lifting': 0.01365782966030925}
2025-08-06 06:55:03,136 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 06:55:03,136 - INFO - ====================================================================================================
2025-08-06 06:55:03,136 - INFO - 🚀 开始训练 - rml201801a Trial 13
2025-08-06 06:55:03,136 - INFO - ====================================================================================================
2025-08-06 06:55:03,136 - INFO - 📋 本次训练参数:
2025-08-06 06:55:03,136 - INFO -   dropout: 0.1308
  lambda_lifting: 0.0137
2025-08-06 06:55:03,136 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 06:55:03,136 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 06:55:03,136 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 06:55:03,136 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 06:55:03,136 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 06:55:03,136 - INFO - ====================================================================================================
2025-08-06 06:55:03,136 - INFO - 🎯 开始训练...
2025-08-06 06:55:03,136 - INFO - ====================================================================================================
2025-08-06 06:55:03,136 - INFO - 🎯 开始训练 rml201801a Trial 13 (第14次试验)
2025-08-06 06:55:03,136 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 06:55:03,184 - INFO - 开始训练，配置文件: /tmp/tmps148ymk9.yaml
2025-08-06 06:55:03,184 - INFO - 数据集类型: rml201801a
2025-08-06 06:55:03,184 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_065503
2025-08-06 06:55:03,195 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_065503/configs/config_backup.yaml
2025-08-06 06:55:03,195 - INFO - 使用设备: cuda
2025-08-06 06:55:03,195 - INFO - 加载数据...
2025-08-06 07:03:19,280 - INFO - 创建模型...
2025-08-06 07:03:19,340 - INFO - 可训练参数数量: 867,608
2025-08-06 07:03:19,341 - INFO - 总参数数量: 867,608
2025-08-06 07:03:19,341 - INFO - 可训练参数比例: 100.00%
2025-08-06 07:03:19,342 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 07:03:19,342 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 14, 'trial_number': 13}
2025-08-06 07:03:19,342 - INFO - 📊 最少训练轮数: 2
2025-08-06 07:03:19,342 - INFO - Epoch 1/400
2025-08-06 07:21:29,198 - INFO - Train Loss: 1.5449, Train Acc: 46.88%
2025-08-06 07:22:27,359 - INFO - Val Loss: 1.3784, Val Acc: 52.81%, Macro-F1: 52.63%, Kappa: 0.5076
2025-08-06 07:22:27,359 - INFO - Epoch 1 训练时间: 1148.02 秒
2025-08-06 07:22:27,420 - INFO - 保存最佳模型，验证准确率: 52.81%, Macro-F1: 52.63%, Kappa: 0.5076
2025-08-06 07:22:27,420 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_065503/models/best_model.pth
2025-08-06 07:22:27,420 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_065503/models/model_epoch_1_acc_52.81.pth
2025-08-06 07:22:27,420 - INFO - Epoch 2/400
2025-08-06 07:40:34,392 - INFO - Train Loss: 1.3052, Train Acc: 55.81%
2025-08-06 07:41:31,522 - INFO - Val Loss: 1.2978, Val Acc: 56.19%, Macro-F1: 58.41%, Kappa: 0.5429
2025-08-06 07:41:31,523 - INFO - Epoch 2 训练时间: 1144.10 秒
2025-08-06 07:41:31,523 - INFO - 🔍 第2轮门槛检查: 准确率 56.1945%, 门槛 60.0000
2025-08-06 07:41:31,523 - INFO - ⚠️ 第2轮准确率 56.1945% 未达到门槛 60.0000，提前结束训练
2025-08-06 07:41:31,523 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_065503/results/training_history.json
2025-08-06 07:41:31,523 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_065503/results/training_summary.json
2025-08-06 07:41:31,523 - INFO - 训练完成！
2025-08-06 07:41:31,523 - INFO - 最佳验证准确率: 52.81%
2025-08-06 07:41:31,524 - INFO - 最佳Macro-F1: 52.63%
2025-08-06 07:41:31,524 - INFO - 最佳Kappa: 0.5076
2025-08-06 07:41:31,524 - INFO - 总训练时间: 2292.12 秒 (38.2 分钟)
2025-08-06 07:41:31,524 - INFO - 平均每轮训练时间: 1146.06 秒
2025-08-06 07:41:31,524 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_065503
2025-08-06 07:41:31,524 - INFO - 🔍 第2轮准确率记录: 0.5619
2025-08-06 07:41:34,258 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 07:41:34,259 - INFO -    - 当前门槛: 60.0000
2025-08-06 07:41:34,259 - INFO -    - 第2轮准确率: 56.1945
2025-08-06 07:41:34,259 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 07:41:34,259 - INFO - 训练完成 rml201801a Trial 13, 结果: {'best_val_acc': 0.5281113195747342, 'best_val_f1': 0.5263002943953125, 'best_val_kappa': 0.5075944204258096, 'total_epochs': 2, 'total_training_time': 2292.1193845272064, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5619449656035022}
2025-08-06 07:41:34,259 - INFO - rml201801a Trial 13 早停训练，使用第2轮准确率: 0.5619
2025-08-06 07:41:34,261 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 07:41:34,261 - INFO - --------------------------------------------------------------------------------
2025-08-06 07:41:34,261 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 07:41:34,261 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 07:41:34,261 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 07:41:34,261 - INFO - --------------------------------------------------------------------------------
2025-08-06 07:41:34,616 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 07:41:34,616 - INFO -    - dropout: 0.11584021634636985 (数据集特定 + 通用)
2025-08-06 07:41:34,617 - INFO -    - lambda_lifting: 0.11819306668931777
2025-08-06 07:41:34,627 - INFO - 📄 临时配置文件创建: /tmp/tmprm5r1aie.yaml
2025-08-06 07:41:34,628 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.11584021634636985, 'batch_size': 256}
2025-08-06 07:41:34,628 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.11584021634636985
2025-08-06 07:41:34,628 - INFO - ✅ 最终训练参数: lambda_lifting=0.11819306668931777, batch_size=128
2025-08-06 07:41:34,628 - INFO - rml201801a Trial 14: 参数 {'dropout': 0.11584021634636985, 'lambda_lifting': 0.11819306668931777}
2025-08-06 07:41:34,784 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 07:41:34,785 - INFO - ====================================================================================================
2025-08-06 07:41:34,785 - INFO - 🚀 开始训练 - rml201801a Trial 14
2025-08-06 07:41:34,785 - INFO - ====================================================================================================
2025-08-06 07:41:34,785 - INFO - 📋 本次训练参数:
2025-08-06 07:41:34,785 - INFO -   dropout: 0.1158
  lambda_lifting: 0.1182
2025-08-06 07:41:34,785 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 07:41:34,785 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 07:41:34,785 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 07:41:34,785 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 07:41:34,785 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 07:41:34,785 - INFO - ====================================================================================================
2025-08-06 07:41:34,785 - INFO - 🎯 开始训练...
2025-08-06 07:41:34,785 - INFO - ====================================================================================================
2025-08-06 07:41:34,785 - INFO - 🎯 开始训练 rml201801a Trial 14 (第15次试验)
2025-08-06 07:41:34,785 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 07:41:34,833 - INFO - 开始训练，配置文件: /tmp/tmprm5r1aie.yaml
2025-08-06 07:41:34,833 - INFO - 数据集类型: rml201801a
2025-08-06 07:41:34,833 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_074134
2025-08-06 07:41:34,844 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_074134/configs/config_backup.yaml
2025-08-06 07:41:34,844 - INFO - 使用设备: cuda
2025-08-06 07:41:34,844 - INFO - 加载数据...
2025-08-06 07:47:29,896 - INFO - 创建模型...
2025-08-06 07:47:29,959 - INFO - 可训练参数数量: 867,608
2025-08-06 07:47:29,959 - INFO - 总参数数量: 867,608
2025-08-06 07:47:29,959 - INFO - 可训练参数比例: 100.00%
2025-08-06 07:47:29,960 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 07:47:29,961 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 15, 'trial_number': 14}
2025-08-06 07:47:29,961 - INFO - 📊 最少训练轮数: 2
2025-08-06 07:47:29,961 - INFO - Epoch 1/400
2025-08-06 08:05:39,589 - INFO - Train Loss: 1.5925, Train Acc: 45.84%
2025-08-06 08:06:37,731 - INFO - Val Loss: 1.4097, Val Acc: 51.52%, Macro-F1: 51.70%, Kappa: 0.4941
2025-08-06 08:06:37,731 - INFO - Epoch 1 训练时间: 1147.77 秒
2025-08-06 08:06:37,795 - INFO - 保存最佳模型，验证准确率: 51.52%, Macro-F1: 51.70%, Kappa: 0.4941
2025-08-06 08:06:37,795 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_074134/models/best_model.pth
2025-08-06 08:06:37,795 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_074134/models/model_epoch_1_acc_51.52.pth
2025-08-06 08:06:37,795 - INFO - Epoch 2/400
2025-08-06 08:24:45,353 - INFO - Train Loss: 1.3327, Train Acc: 55.36%
2025-08-06 08:25:42,514 - INFO - Val Loss: 1.2949, Val Acc: 56.55%, Macro-F1: 57.60%, Kappa: 0.5466
2025-08-06 08:25:42,514 - INFO - Epoch 2 训练时间: 1144.72 秒
2025-08-06 08:25:42,514 - INFO - 🔍 第2轮门槛检查: 准确率 56.5463%, 门槛 60.0000
2025-08-06 08:25:42,514 - INFO - ⚠️ 第2轮准确率 56.5463% 未达到门槛 60.0000，提前结束训练
2025-08-06 08:25:42,515 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_074134/results/training_history.json
2025-08-06 08:25:42,515 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_074134/results/training_summary.json
2025-08-06 08:25:42,515 - INFO - 训练完成！
2025-08-06 08:25:42,515 - INFO - 最佳验证准确率: 51.52%
2025-08-06 08:25:42,515 - INFO - 最佳Macro-F1: 51.70%
2025-08-06 08:25:42,515 - INFO - 最佳Kappa: 0.4941
2025-08-06 08:25:42,515 - INFO - 总训练时间: 2292.49 秒 (38.2 分钟)
2025-08-06 08:25:42,515 - INFO - 平均每轮训练时间: 1146.24 秒
2025-08-06 08:25:42,515 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_074134
2025-08-06 08:25:42,515 - INFO - 🔍 第2轮准确率记录: 0.5655
2025-08-06 08:25:45,304 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 08:25:45,305 - INFO -    - 当前门槛: 60.0000
2025-08-06 08:25:45,305 - INFO -    - 第2轮准确率: 56.5463
2025-08-06 08:25:45,305 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 08:25:45,305 - INFO - 训练完成 rml201801a Trial 14, 结果: {'best_val_acc': 0.5151787575568063, 'best_val_f1': 0.5170110065641894, 'best_val_kappa': 0.49409957310275443, 'total_epochs': 2, 'total_training_time': 2292.48965549469, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.565462789243277}
2025-08-06 08:25:45,305 - INFO - rml201801a Trial 14 早停训练，使用第2轮准确率: 0.5655
2025-08-06 08:25:45,307 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 08:25:45,307 - INFO - --------------------------------------------------------------------------------
2025-08-06 08:25:45,307 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 08:25:45,307 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 08:25:45,307 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 08:25:45,307 - INFO - --------------------------------------------------------------------------------
2025-08-06 08:25:45,677 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 08:25:45,677 - INFO -    - dropout: 0.42117508708370305 (数据集特定 + 通用)
2025-08-06 08:25:45,678 - INFO -    - lambda_lifting: 0.11084364581588171
2025-08-06 08:25:45,688 - INFO - 📄 临时配置文件创建: /tmp/tmpnti_4f8z.yaml
2025-08-06 08:25:45,688 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.42117508708370305, 'batch_size': 256}
2025-08-06 08:25:45,688 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.42117508708370305
2025-08-06 08:25:45,688 - INFO - ✅ 最终训练参数: lambda_lifting=0.11084364581588171, batch_size=128
2025-08-06 08:25:45,689 - INFO - rml201801a Trial 15: 参数 {'dropout': 0.42117508708370305, 'lambda_lifting': 0.11084364581588171}
2025-08-06 08:25:45,812 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 08:25:45,812 - INFO - ====================================================================================================
2025-08-06 08:25:45,812 - INFO - 🚀 开始训练 - rml201801a Trial 15
2025-08-06 08:25:45,812 - INFO - ====================================================================================================
2025-08-06 08:25:45,812 - INFO - 📋 本次训练参数:
2025-08-06 08:25:45,812 - INFO -   dropout: 0.4212
  lambda_lifting: 0.1108
2025-08-06 08:25:45,812 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 08:25:45,812 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 08:25:45,812 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 08:25:45,812 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 08:25:45,812 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 08:25:45,812 - INFO - ====================================================================================================
2025-08-06 08:25:45,812 - INFO - 🎯 开始训练...
2025-08-06 08:25:45,812 - INFO - ====================================================================================================
2025-08-06 08:25:45,812 - INFO - 🎯 开始训练 rml201801a Trial 15 (第16次试验)
2025-08-06 08:25:45,812 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 08:25:45,860 - INFO - 开始训练，配置文件: /tmp/tmpnti_4f8z.yaml
2025-08-06 08:25:45,860 - INFO - 数据集类型: rml201801a
2025-08-06 08:25:45,860 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_082545
2025-08-06 08:25:45,871 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_082545/configs/config_backup.yaml
2025-08-06 08:25:45,871 - INFO - 使用设备: cuda
2025-08-06 08:25:45,871 - INFO - 加载数据...
2025-08-06 08:28:03,202 - INFO - 创建模型...
2025-08-06 08:28:03,240 - INFO - 可训练参数数量: 867,608
2025-08-06 08:28:03,240 - INFO - 总参数数量: 867,608
2025-08-06 08:28:03,240 - INFO - 可训练参数比例: 100.00%
2025-08-06 08:28:03,241 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 08:28:03,241 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 16, 'trial_number': 15}
2025-08-06 08:28:03,241 - INFO - 📊 最少训练轮数: 2
2025-08-06 08:28:03,241 - INFO - Epoch 1/400
2025-08-06 08:46:13,775 - INFO - Train Loss: 1.6818, Train Acc: 43.08%
2025-08-06 08:47:11,828 - INFO - Val Loss: 1.4984, Val Acc: 47.99%, Macro-F1: 46.90%, Kappa: 0.4573
2025-08-06 08:47:11,828 - INFO - Epoch 1 训练时间: 1148.59 秒
2025-08-06 08:47:11,895 - INFO - 保存最佳模型，验证准确率: 47.99%, Macro-F1: 46.90%, Kappa: 0.4573
2025-08-06 08:47:11,896 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_082545/models/best_model.pth
2025-08-06 08:47:11,896 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_082545/models/model_epoch_1_acc_47.99.pth
2025-08-06 08:47:11,896 - INFO - Epoch 2/400
2025-08-06 09:05:18,948 - INFO - Train Loss: 1.4408, Train Acc: 51.00%
2025-08-06 09:06:16,120 - INFO - Val Loss: 1.4127, Val Acc: 51.39%, Macro-F1: 52.84%, Kappa: 0.4928
2025-08-06 09:06:16,120 - INFO - Epoch 2 训练时间: 1144.22 秒
2025-08-06 09:06:16,121 - INFO - 🔍 第2轮门槛检查: 准确率 51.3886%, 门槛 60.0000
2025-08-06 09:06:16,121 - INFO - ⚠️ 第2轮准确率 51.3886% 未达到门槛 60.0000，提前结束训练
2025-08-06 09:06:16,121 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_082545/results/training_history.json
2025-08-06 09:06:16,121 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_082545/results/training_summary.json
2025-08-06 09:06:16,121 - INFO - 训练完成！
2025-08-06 09:06:16,121 - INFO - 最佳验证准确率: 47.99%
2025-08-06 09:06:16,121 - INFO - 最佳Macro-F1: 46.90%
2025-08-06 09:06:16,121 - INFO - 最佳Kappa: 0.4573
2025-08-06 09:06:16,121 - INFO - 总训练时间: 2292.81 秒 (38.2 分钟)
2025-08-06 09:06:16,121 - INFO - 平均每轮训练时间: 1146.41 秒
2025-08-06 09:06:16,121 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_082545
2025-08-06 09:06:16,121 - INFO - 🔍 第2轮准确率记录: 0.5139
2025-08-06 09:06:18,839 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 09:06:18,839 - INFO -    - 当前门槛: 60.0000
2025-08-06 09:06:18,839 - INFO -    - 第2轮准确率: 51.3886
2025-08-06 09:06:18,839 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 09:06:18,839 - INFO - 训练完成 rml201801a Trial 15, 结果: {'best_val_acc': 0.4798858661663539, 'best_val_f1': 0.46898519517123916, 'best_val_kappa': 0.4572722081735867, 'total_epochs': 2, 'total_training_time': 2292.81174659729, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5138862830936002}
2025-08-06 09:06:18,839 - INFO - rml201801a Trial 15 早停训练，使用第2轮准确率: 0.5139
2025-08-06 09:06:18,841 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 09:06:18,841 - INFO - --------------------------------------------------------------------------------
2025-08-06 09:06:18,841 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 09:06:18,841 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 09:06:18,841 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 09:06:18,841 - INFO - --------------------------------------------------------------------------------
2025-08-06 09:06:19,378 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 09:06:19,378 - INFO -    - dropout: 0.22203274903391282 (数据集特定 + 通用)
2025-08-06 09:06:19,379 - INFO -    - lambda_lifting: 0.06737019642538318
2025-08-06 09:06:19,390 - INFO - 📄 临时配置文件创建: /tmp/tmpet3jnyvo.yaml
2025-08-06 09:06:19,390 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.22203274903391282, 'batch_size': 256}
2025-08-06 09:06:19,390 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.22203274903391282
2025-08-06 09:06:19,390 - INFO - ✅ 最终训练参数: lambda_lifting=0.06737019642538318, batch_size=128
2025-08-06 09:06:19,390 - INFO - rml201801a Trial 16: 参数 {'dropout': 0.22203274903391282, 'lambda_lifting': 0.06737019642538318}
2025-08-06 09:06:19,483 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 09:06:19,484 - INFO - ====================================================================================================
2025-08-06 09:06:19,484 - INFO - 🚀 开始训练 - rml201801a Trial 16
2025-08-06 09:06:19,484 - INFO - ====================================================================================================
2025-08-06 09:06:19,484 - INFO - 📋 本次训练参数:
2025-08-06 09:06:19,484 - INFO -   dropout: 0.2220
  lambda_lifting: 0.0674
2025-08-06 09:06:19,484 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 09:06:19,484 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 09:06:19,484 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 09:06:19,484 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 09:06:19,484 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 09:06:19,484 - INFO - ====================================================================================================
2025-08-06 09:06:19,484 - INFO - 🎯 开始训练...
2025-08-06 09:06:19,484 - INFO - ====================================================================================================
2025-08-06 09:06:19,484 - INFO - 🎯 开始训练 rml201801a Trial 16 (第17次试验)
2025-08-06 09:06:19,484 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 09:06:19,532 - INFO - 开始训练，配置文件: /tmp/tmpet3jnyvo.yaml
2025-08-06 09:06:19,532 - INFO - 数据集类型: rml201801a
2025-08-06 09:06:19,532 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_090619
2025-08-06 09:06:19,543 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_090619/configs/config_backup.yaml
2025-08-06 09:06:19,543 - INFO - 使用设备: cuda
2025-08-06 09:06:19,543 - INFO - 加载数据...
2025-08-06 09:08:26,093 - INFO - 创建模型...
2025-08-06 09:08:26,135 - INFO - 可训练参数数量: 867,608
2025-08-06 09:08:26,135 - INFO - 总参数数量: 867,608
2025-08-06 09:08:26,135 - INFO - 可训练参数比例: 100.00%
2025-08-06 09:08:26,136 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 09:08:26,136 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 17, 'trial_number': 16}
2025-08-06 09:08:26,136 - INFO - 📊 最少训练轮数: 2
2025-08-06 09:08:26,136 - INFO - Epoch 1/400
2025-08-06 09:26:36,756 - INFO - Train Loss: 1.6243, Train Acc: 44.70%
2025-08-06 09:27:35,055 - INFO - Val Loss: 1.4296, Val Acc: 50.84%, Macro-F1: 50.56%, Kappa: 0.4871
2025-08-06 09:27:35,056 - INFO - Epoch 1 训练时间: 1148.92 秒
2025-08-06 09:27:35,119 - INFO - 保存最佳模型，验证准确率: 50.84%, Macro-F1: 50.56%, Kappa: 0.4871
2025-08-06 09:27:35,120 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_090619/models/best_model.pth
2025-08-06 09:27:35,120 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_090619/models/model_epoch_1_acc_50.84.pth
2025-08-06 09:27:35,120 - INFO - Epoch 2/400
2025-08-06 09:45:42,325 - INFO - Train Loss: 1.3786, Train Acc: 53.68%
2025-08-06 09:46:39,794 - INFO - Val Loss: 1.3393, Val Acc: 55.04%, Macro-F1: 56.63%, Kappa: 0.5308
2025-08-06 09:46:39,794 - INFO - Epoch 2 训练时间: 1144.67 秒
2025-08-06 09:46:39,794 - INFO - 🔍 第2轮门槛检查: 准确率 55.0383%, 门槛 60.0000
2025-08-06 09:46:39,794 - INFO - ⚠️ 第2轮准确率 55.0383% 未达到门槛 60.0000，提前结束训练
2025-08-06 09:46:39,795 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_090619/results/training_history.json
2025-08-06 09:46:39,795 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_090619/results/training_summary.json
2025-08-06 09:46:39,795 - INFO - 训练完成！
2025-08-06 09:46:39,795 - INFO - 最佳验证准确率: 50.84%
2025-08-06 09:46:39,795 - INFO - 最佳Macro-F1: 50.56%
2025-08-06 09:46:39,795 - INFO - 最佳Kappa: 0.4871
2025-08-06 09:46:39,795 - INFO - 总训练时间: 2293.59 秒 (38.2 分钟)
2025-08-06 09:46:39,795 - INFO - 平均每轮训练时间: 1146.80 秒
2025-08-06 09:46:39,795 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_090619
2025-08-06 09:46:39,795 - INFO - 🔍 第2轮准确率记录: 0.5504
2025-08-06 09:46:42,577 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 09:46:42,577 - INFO -    - 当前门槛: 60.0000
2025-08-06 09:46:42,577 - INFO -    - 第2轮准确率: 55.0383
2025-08-06 09:46:42,577 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 09:46:42,577 - INFO - 训练完成 rml201801a Trial 16, 结果: {'best_val_acc': 0.508440170940171, 'best_val_f1': 0.5055979396376106, 'best_val_kappa': 0.48706800445930876, 'total_epochs': 2, 'total_training_time': 2293.593867301941, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5503830519074422}
2025-08-06 09:46:42,577 - INFO - rml201801a Trial 16 早停训练，使用第2轮准确率: 0.5504
2025-08-06 09:46:42,579 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 09:46:42,579 - INFO - --------------------------------------------------------------------------------
2025-08-06 09:46:42,579 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 09:46:42,579 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 09:46:42,579 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 09:46:42,579 - INFO - --------------------------------------------------------------------------------
2025-08-06 09:46:43,202 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 09:46:43,202 - INFO -    - dropout: 0.34485872018444197 (数据集特定 + 通用)
2025-08-06 09:46:43,202 - INFO -    - lambda_lifting: 0.2051766603962944
2025-08-06 09:46:43,213 - INFO - 📄 临时配置文件创建: /tmp/tmpe9ch8wqm.yaml
2025-08-06 09:46:43,213 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.34485872018444197, 'batch_size': 256}
2025-08-06 09:46:43,213 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.34485872018444197
2025-08-06 09:46:43,213 - INFO - ✅ 最终训练参数: lambda_lifting=0.2051766603962944, batch_size=128
2025-08-06 09:46:43,213 - INFO - rml201801a Trial 17: 参数 {'dropout': 0.34485872018444197, 'lambda_lifting': 0.2051766603962944}
2025-08-06 09:46:43,349 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 09:46:43,349 - INFO - ====================================================================================================
2025-08-06 09:46:43,349 - INFO - 🚀 开始训练 - rml201801a Trial 17
2025-08-06 09:46:43,349 - INFO - ====================================================================================================
2025-08-06 09:46:43,349 - INFO - 📋 本次训练参数:
2025-08-06 09:46:43,349 - INFO -   dropout: 0.3449
  lambda_lifting: 0.2052
2025-08-06 09:46:43,349 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 09:46:43,349 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 09:46:43,349 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 09:46:43,349 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 09:46:43,349 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 09:46:43,349 - INFO - ====================================================================================================
2025-08-06 09:46:43,349 - INFO - 🎯 开始训练...
2025-08-06 09:46:43,349 - INFO - ====================================================================================================
2025-08-06 09:46:43,349 - INFO - 🎯 开始训练 rml201801a Trial 17 (第18次试验)
2025-08-06 09:46:43,349 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 09:46:43,398 - INFO - 开始训练，配置文件: /tmp/tmpe9ch8wqm.yaml
2025-08-06 09:46:43,398 - INFO - 数据集类型: rml201801a
2025-08-06 09:46:43,398 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_094643
2025-08-06 09:46:43,408 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_094643/configs/config_backup.yaml
2025-08-06 09:46:43,408 - INFO - 使用设备: cuda
2025-08-06 09:46:43,409 - INFO - 加载数据...
2025-08-06 09:48:52,396 - INFO - 创建模型...
2025-08-06 09:48:52,459 - INFO - 可训练参数数量: 867,608
2025-08-06 09:48:52,460 - INFO - 总参数数量: 867,608
2025-08-06 09:48:52,460 - INFO - 可训练参数比例: 100.00%
2025-08-06 09:48:52,461 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 09:48:52,461 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 18, 'trial_number': 17}
2025-08-06 09:48:52,461 - INFO - 📊 最少训练轮数: 2
2025-08-06 09:48:52,461 - INFO - Epoch 1/400
2025-08-06 10:07:02,963 - INFO - Train Loss: 1.6741, Train Acc: 43.44%
2025-08-06 10:08:01,070 - INFO - Val Loss: 1.4621, Val Acc: 49.18%, Macro-F1: 48.54%, Kappa: 0.4697
2025-08-06 10:08:01,071 - INFO - Epoch 1 训练时间: 1148.61 秒
2025-08-06 10:08:01,132 - INFO - 保存最佳模型，验证准确率: 49.18%, Macro-F1: 48.54%, Kappa: 0.4697
2025-08-06 10:08:01,132 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_094643/models/best_model.pth
2025-08-06 10:08:01,132 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_094643/models/model_epoch_1_acc_49.18.pth
2025-08-06 10:08:01,132 - INFO - Epoch 2/400
2025-08-06 10:26:08,831 - INFO - Train Loss: 1.4157, Train Acc: 52.02%
2025-08-06 10:27:06,149 - INFO - Val Loss: 1.3883, Val Acc: 53.16%, Macro-F1: 54.29%, Kappa: 0.5113
2025-08-06 10:27:06,149 - INFO - Epoch 2 训练时间: 1145.02 秒
2025-08-06 10:27:06,149 - INFO - 🔍 第2轮门槛检查: 准确率 53.1637%, 门槛 60.0000
2025-08-06 10:27:06,150 - INFO - ⚠️ 第2轮准确率 53.1637% 未达到门槛 60.0000，提前结束训练
2025-08-06 10:27:06,150 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_094643/results/training_history.json
2025-08-06 10:27:06,150 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_094643/results/training_summary.json
2025-08-06 10:27:06,150 - INFO - 训练完成！
2025-08-06 10:27:06,150 - INFO - 最佳验证准确率: 49.18%
2025-08-06 10:27:06,150 - INFO - 最佳Macro-F1: 48.54%
2025-08-06 10:27:06,150 - INFO - 最佳Kappa: 0.4697
2025-08-06 10:27:06,150 - INFO - 总训练时间: 2293.63 秒 (38.2 分钟)
2025-08-06 10:27:06,150 - INFO - 平均每轮训练时间: 1146.81 秒
2025-08-06 10:27:06,150 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_094643
2025-08-06 10:27:06,150 - INFO - 🔍 第2轮准确率记录: 0.5316
2025-08-06 10:27:08,532 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 10:27:08,533 - INFO -    - 当前门槛: 60.0000
2025-08-06 10:27:08,533 - INFO -    - 第2轮准确率: 53.1637
2025-08-06 10:27:08,533 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 10:27:08,533 - INFO - 训练完成 rml201801a Trial 17, 结果: {'best_val_acc': 0.4917682926829269, 'best_val_f1': 0.4853560114685214, 'best_val_kappa': 0.46967126193001063, 'total_epochs': 2, 'total_training_time': 2293.6270904541016, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5316369606003752}
2025-08-06 10:27:08,533 - INFO - rml201801a Trial 17 早停训练，使用第2轮准确率: 0.5316
2025-08-06 10:27:08,535 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 10:27:08,535 - INFO - --------------------------------------------------------------------------------
2025-08-06 10:27:08,535 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 10:27:08,535 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 10:27:08,535 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 10:27:08,535 - INFO - --------------------------------------------------------------------------------
2025-08-06 10:27:08,905 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 10:27:08,905 - INFO -    - dropout: 0.4982493119467225 (数据集特定 + 通用)
2025-08-06 10:27:08,905 - INFO -    - lambda_lifting: 0.17329349740503833
2025-08-06 10:27:08,916 - INFO - 📄 临时配置文件创建: /tmp/tmpxf9vtonb.yaml
2025-08-06 10:27:08,916 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.4982493119467225, 'batch_size': 256}
2025-08-06 10:27:08,916 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.4982493119467225
2025-08-06 10:27:08,916 - INFO - ✅ 最终训练参数: lambda_lifting=0.17329349740503833, batch_size=128
2025-08-06 10:27:08,916 - INFO - rml201801a Trial 18: 参数 {'dropout': 0.4982493119467225, 'lambda_lifting': 0.17329349740503833}
2025-08-06 10:27:09,077 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 10:27:09,077 - INFO - ====================================================================================================
2025-08-06 10:27:09,077 - INFO - 🚀 开始训练 - rml201801a Trial 18
2025-08-06 10:27:09,077 - INFO - ====================================================================================================
2025-08-06 10:27:09,077 - INFO - 📋 本次训练参数:
2025-08-06 10:27:09,077 - INFO -   dropout: 0.4982
  lambda_lifting: 0.1733
2025-08-06 10:27:09,077 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 10:27:09,077 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 10:27:09,077 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 10:27:09,077 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 10:27:09,077 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 10:27:09,077 - INFO - ====================================================================================================
2025-08-06 10:27:09,077 - INFO - 🎯 开始训练...
2025-08-06 10:27:09,077 - INFO - ====================================================================================================
2025-08-06 10:27:09,077 - INFO - 🎯 开始训练 rml201801a Trial 18 (第19次试验)
2025-08-06 10:27:09,077 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 10:27:09,125 - INFO - 开始训练，配置文件: /tmp/tmpxf9vtonb.yaml
2025-08-06 10:27:09,125 - INFO - 数据集类型: rml201801a
2025-08-06 10:27:09,125 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_102709
2025-08-06 10:27:09,136 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_102709/configs/config_backup.yaml
2025-08-06 10:27:09,136 - INFO - 使用设备: cuda
2025-08-06 10:27:09,136 - INFO - 加载数据...
2025-08-06 10:29:23,873 - INFO - 创建模型...
2025-08-06 10:29:23,939 - INFO - 可训练参数数量: 867,608
2025-08-06 10:29:23,939 - INFO - 总参数数量: 867,608
2025-08-06 10:29:23,939 - INFO - 可训练参数比例: 100.00%
2025-08-06 10:29:23,940 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 10:29:23,940 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 19, 'trial_number': 18}
2025-08-06 10:29:23,940 - INFO - 📊 最少训练轮数: 2
2025-08-06 10:29:23,940 - INFO - Epoch 1/400
2025-08-06 10:47:33,909 - INFO - Train Loss: 1.7223, Train Acc: 41.68%
2025-08-06 10:48:32,061 - INFO - Val Loss: 1.5830, Val Acc: 44.84%, Macro-F1: 43.90%, Kappa: 0.4244
2025-08-06 10:48:32,062 - INFO - Epoch 1 训练时间: 1148.12 秒
2025-08-06 10:48:32,124 - INFO - 保存最佳模型，验证准确率: 44.84%, Macro-F1: 43.90%, Kappa: 0.4244
2025-08-06 10:48:32,124 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_102709/models/best_model.pth
2025-08-06 10:48:32,124 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_102709/models/model_epoch_1_acc_44.84.pth
2025-08-06 10:48:32,124 - INFO - Epoch 2/400
2025-08-06 11:06:38,065 - INFO - Train Loss: 1.4615, Train Acc: 50.33%
2025-08-06 11:07:35,182 - INFO - Val Loss: 1.4133, Val Acc: 51.24%, Macro-F1: 51.13%, Kappa: 0.4912
2025-08-06 11:07:35,183 - INFO - Epoch 2 训练时间: 1143.06 秒
2025-08-06 11:07:35,183 - INFO - 🔍 第2轮门槛检查: 准确率 51.2419%, 门槛 60.0000
2025-08-06 11:07:35,183 - INFO - ⚠️ 第2轮准确率 51.2419% 未达到门槛 60.0000，提前结束训练
2025-08-06 11:07:35,183 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_102709/results/training_history.json
2025-08-06 11:07:35,184 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_102709/results/training_summary.json
2025-08-06 11:07:35,184 - INFO - 训练完成！
2025-08-06 11:07:35,184 - INFO - 最佳验证准确率: 44.84%
2025-08-06 11:07:35,184 - INFO - 最佳Macro-F1: 43.90%
2025-08-06 11:07:35,184 - INFO - 最佳Kappa: 0.4244
2025-08-06 11:07:35,184 - INFO - 总训练时间: 2291.18 秒 (38.2 分钟)
2025-08-06 11:07:35,184 - INFO - 平均每轮训练时间: 1145.59 秒
2025-08-06 11:07:35,184 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_102709
2025-08-06 11:07:35,184 - INFO - 🔍 第2轮准确率记录: 0.5124
2025-08-06 11:07:37,842 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 11:07:37,842 - INFO -    - 当前门槛: 60.0000
2025-08-06 11:07:37,842 - INFO -    - 第2轮准确率: 51.2419
2025-08-06 11:07:37,842 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 11:07:37,842 - INFO - 训练完成 rml201801a Trial 18, 结果: {'best_val_acc': 0.4484208880550344, 'best_val_f1': 0.43903401617283366, 'best_val_kappa': 0.4244391875356881, 'total_epochs': 2, 'total_training_time': 2291.1800334453583, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5124192203460496}
2025-08-06 11:07:37,842 - INFO - rml201801a Trial 18 早停训练，使用第2轮准确率: 0.5124
2025-08-06 11:07:37,844 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 11:07:37,844 - INFO - --------------------------------------------------------------------------------
2025-08-06 11:07:37,845 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 11:07:37,845 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 11:07:37,845 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 11:07:37,845 - INFO - --------------------------------------------------------------------------------
2025-08-06 11:07:38,311 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 11:07:38,311 - INFO -    - dropout: 0.6924209643082504 (数据集特定 + 通用)
2025-08-06 11:07:38,311 - INFO -    - lambda_lifting: 0.06809196611452875
2025-08-06 11:07:38,322 - INFO - 📄 临时配置文件创建: /tmp/tmpsld0dk29.yaml
2025-08-06 11:07:38,322 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.6924209643082504, 'batch_size': 256}
2025-08-06 11:07:38,322 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.6924209643082504
2025-08-06 11:07:38,322 - INFO - ✅ 最终训练参数: lambda_lifting=0.06809196611452875, batch_size=128
2025-08-06 11:07:38,322 - INFO - rml201801a Trial 19: 参数 {'dropout': 0.6924209643082504, 'lambda_lifting': 0.06809196611452875}
2025-08-06 11:07:38,437 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 11:07:38,438 - INFO - ====================================================================================================
2025-08-06 11:07:38,438 - INFO - 🚀 开始训练 - rml201801a Trial 19
2025-08-06 11:07:38,438 - INFO - ====================================================================================================
2025-08-06 11:07:38,438 - INFO - 📋 本次训练参数:
2025-08-06 11:07:38,438 - INFO -   dropout: 0.6924
  lambda_lifting: 0.0681
2025-08-06 11:07:38,438 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 11:07:38,438 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 11:07:38,438 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 11:07:38,438 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 11:07:38,438 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 11:07:38,438 - INFO - ====================================================================================================
2025-08-06 11:07:38,438 - INFO - 🎯 开始训练...
2025-08-06 11:07:38,438 - INFO - ====================================================================================================
2025-08-06 11:07:38,438 - INFO - 🎯 开始训练 rml201801a Trial 19 (第20次试验)
2025-08-06 11:07:38,438 - INFO - 🚪 当前第2轮门槛: 60.0000
2025-08-06 11:07:38,486 - INFO - 开始训练，配置文件: /tmp/tmpsld0dk29.yaml
2025-08-06 11:07:38,486 - INFO - 数据集类型: rml201801a
2025-08-06 11:07:38,486 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_110738
2025-08-06 11:07:38,497 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_110738/configs/config_backup.yaml
2025-08-06 11:07:38,497 - INFO - 使用设备: cuda
2025-08-06 11:07:38,497 - INFO - 加载数据...
2025-08-06 11:10:37,276 - INFO - 创建模型...
2025-08-06 11:10:37,396 - INFO - 可训练参数数量: 867,608
2025-08-06 11:10:37,397 - INFO - 总参数数量: 867,608
2025-08-06 11:10:37,397 - INFO - 可训练参数比例: 100.00%
2025-08-06 11:10:37,399 - INFO - 🎯 启用第二轮门槛检查，门槛: 60.0000
2025-08-06 11:10:37,399 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 20, 'trial_number': 19}
2025-08-06 11:10:37,399 - INFO - 📊 最少训练轮数: 2
2025-08-06 11:10:37,399 - INFO - Epoch 1/400
2025-08-06 11:28:46,592 - INFO - Train Loss: 1.7795, Train Acc: 38.87%
2025-08-06 11:29:44,562 - INFO - Val Loss: 1.8298, Val Acc: 38.75%, Macro-F1: 36.33%, Kappa: 0.3609
2025-08-06 11:29:44,562 - INFO - Epoch 1 训练时间: 1147.16 秒
2025-08-06 11:29:44,622 - INFO - 保存最佳模型，验证准确率: 38.75%, Macro-F1: 36.33%, Kappa: 0.3609
2025-08-06 11:29:44,623 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_110738/models/best_model.pth
2025-08-06 11:29:44,623 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_110738/models/model_epoch_1_acc_38.75.pth
2025-08-06 11:29:44,623 - INFO - Epoch 2/400
2025-08-06 11:47:50,003 - INFO - Train Loss: 1.5340, Train Acc: 47.37%
2025-08-06 11:48:46,998 - INFO - Val Loss: 1.8610, Val Acc: 42.54%, Macro-F1: 42.51%, Kappa: 0.4004
2025-08-06 11:48:46,998 - INFO - Epoch 2 训练时间: 1142.38 秒
2025-08-06 11:48:46,998 - INFO - 🔍 第2轮门槛检查: 准确率 42.5425%, 门槛 60.0000
2025-08-06 11:48:46,998 - INFO - ⚠️ 第2轮准确率 42.5425% 未达到门槛 60.0000，提前结束训练
2025-08-06 11:48:46,999 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_110738/results/training_history.json
2025-08-06 11:48:46,999 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_110738/results/training_summary.json
2025-08-06 11:48:46,999 - INFO - 训练完成！
2025-08-06 11:48:46,999 - INFO - 最佳验证准确率: 38.75%
2025-08-06 11:48:46,999 - INFO - 最佳Macro-F1: 36.33%
2025-08-06 11:48:46,999 - INFO - 最佳Kappa: 0.3609
2025-08-06 11:48:46,999 - INFO - 总训练时间: 2289.54 秒 (38.2 分钟)
2025-08-06 11:48:46,999 - INFO - 平均每轮训练时间: 1144.77 秒
2025-08-06 11:48:46,999 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_110738
2025-08-06 11:48:46,999 - INFO - 🔍 第2轮准确率记录: 0.4254
2025-08-06 11:48:49,622 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 11:48:49,622 - INFO -    - 当前门槛: 60.0000
2025-08-06 11:48:49,623 - INFO -    - 第2轮准确率: 42.5425
2025-08-06 11:48:49,623 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 60.0000
2025-08-06 11:48:49,623 - INFO - 训练完成 rml201801a Trial 19, 结果: {'best_val_acc': 0.38749478840942253, 'best_val_f1': 0.3632678963224105, 'best_val_kappa': 0.3608641270359192, 'total_epochs': 2, 'total_training_time': 2289.538379430771, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.4254247446320617}
2025-08-06 11:48:49,623 - INFO - rml201801a Trial 19 早停训练，使用第2轮准确率: 0.4254
2025-08-06 11:48:49,625 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 11:48:49,625 - INFO - --------------------------------------------------------------------------------
2025-08-06 11:48:49,625 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 11:48:49,625 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 11:48:49,625 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 11:48:49,625 - INFO - --------------------------------------------------------------------------------
2025-08-06 11:48:50,013 - INFO - ✅ 阶段1完成:
2025-08-06 11:48:50,013 - INFO -    - 计划试验次数: 20
2025-08-06 11:48:50,013 - INFO -    - 实际完成试验次数: 20
2025-08-06 11:48:50,013 - INFO -    - 完整训练完成次数: 0
2025-08-06 11:48:50,013 - INFO - ⚠️ 阶段2: 前20轮试验没有完整训练完成，自动调整门槛
2025-08-06 11:48:50,013 - INFO - 📊 前20轮试验分析:
2025-08-06 11:48:50,013 - INFO -    - 原始门槛: 60.0000
2025-08-06 11:48:50,013 - INFO -    - 前20轮最优准确率: 57.4476
2025-08-06 11:48:50,013 - INFO - 🔧 rml201801a 门槛自动调整: 60.0000 → 57.4476
2025-08-06 11:48:50,013 - INFO - 📊 新策略: 使用前20轮最优准确率作为门槛，继续试验直到完整训练完成
2025-08-06 11:48:50,013 - INFO - ✅ 门槛降低合理，从 60.0000 降至 57.4476
2025-08-06 11:48:50,013 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 0 次)
2025-08-06 11:48:50,037 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 11:48:50,037 - INFO -    - dropout: 0.18616317405804933 (数据集特定 + 通用)
2025-08-06 11:48:50,037 - INFO -    - lambda_lifting: 0.5357172492448996
2025-08-06 11:48:50,048 - INFO - 📄 临时配置文件创建: /tmp/tmp4dc7le8u.yaml
2025-08-06 11:48:50,048 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.18616317405804933, 'batch_size': 256}
2025-08-06 11:48:50,048 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.18616317405804933
2025-08-06 11:48:50,048 - INFO - ✅ 最终训练参数: lambda_lifting=0.5357172492448996, batch_size=128
2025-08-06 11:48:50,048 - INFO - rml201801a Trial 20: 参数 {'dropout': 0.18616317405804933, 'lambda_lifting': 0.5357172492448996}
2025-08-06 11:48:50,183 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 11:48:50,183 - INFO - ====================================================================================================
2025-08-06 11:48:50,183 - INFO - 🚀 开始训练 - rml201801a Trial 20
2025-08-06 11:48:50,183 - INFO - ====================================================================================================
2025-08-06 11:48:50,183 - INFO - 📋 本次训练参数:
2025-08-06 11:48:50,183 - INFO -   dropout: 0.1862
  lambda_lifting: 0.5357
2025-08-06 11:48:50,183 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 11:48:50,183 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 11:48:50,183 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 11:48:50,183 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 11:48:50,183 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 11:48:50,183 - INFO - ====================================================================================================
2025-08-06 11:48:50,183 - INFO - 🎯 开始训练...
2025-08-06 11:48:50,184 - INFO - ====================================================================================================
2025-08-06 11:48:50,184 - INFO - 🎯 开始训练 rml201801a Trial 20 (第21次试验)
2025-08-06 11:48:50,184 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 11:48:50,232 - INFO - 开始训练，配置文件: /tmp/tmp4dc7le8u.yaml
2025-08-06 11:48:50,232 - INFO - 数据集类型: rml201801a
2025-08-06 11:48:50,232 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_114850
2025-08-06 11:48:50,243 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_114850/configs/config_backup.yaml
2025-08-06 11:48:50,243 - INFO - 使用设备: cuda
2025-08-06 11:48:50,243 - INFO - 加载数据...
2025-08-06 11:57:46,584 - INFO - 创建模型...
2025-08-06 11:57:46,681 - INFO - 可训练参数数量: 867,608
2025-08-06 11:57:46,682 - INFO - 总参数数量: 867,608
2025-08-06 11:57:46,682 - INFO - 可训练参数比例: 100.00%
2025-08-06 11:57:46,683 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 11:57:46,683 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 21, 'trial_number': 20}
2025-08-06 11:57:46,683 - INFO - 📊 最少训练轮数: 2
2025-08-06 11:57:46,683 - INFO - Epoch 1/400
2025-08-06 12:15:55,940 - INFO - Train Loss: 1.6928, Train Acc: 43.60%
2025-08-06 12:16:53,939 - INFO - Val Loss: 1.4511, Val Acc: 49.77%, Macro-F1: 48.92%, Kappa: 0.4758
2025-08-06 12:16:53,940 - INFO - Epoch 1 训练时间: 1147.26 秒
2025-08-06 12:16:54,022 - INFO - 保存最佳模型，验证准确率: 49.77%, Macro-F1: 48.92%, Kappa: 0.4758
2025-08-06 12:16:54,022 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_114850/models/best_model.pth
2025-08-06 12:16:54,022 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_114850/models/model_epoch_1_acc_49.77.pth
2025-08-06 12:16:54,022 - INFO - Epoch 2/400
2025-08-06 12:35:02,043 - INFO - Train Loss: 1.4044, Train Acc: 53.02%
2025-08-06 12:35:59,306 - INFO - Val Loss: 1.3531, Val Acc: 54.45%, Macro-F1: 55.81%, Kappa: 0.5247
2025-08-06 12:35:59,306 - INFO - Epoch 2 训练时间: 1145.28 秒
2025-08-06 12:35:59,306 - INFO - 🔍 第2轮门槛检查: 准确率 54.4491%, 门槛 57.4476
2025-08-06 12:35:59,306 - INFO - ⚠️ 第2轮准确率 54.4491% 未达到门槛 57.4476，提前结束训练
2025-08-06 12:35:59,307 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_114850/results/training_history.json
2025-08-06 12:35:59,307 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_114850/results/training_summary.json
2025-08-06 12:35:59,307 - INFO - 训练完成！
2025-08-06 12:35:59,307 - INFO - 最佳验证准确率: 49.77%
2025-08-06 12:35:59,307 - INFO - 最佳Macro-F1: 48.92%
2025-08-06 12:35:59,307 - INFO - 最佳Kappa: 0.4758
2025-08-06 12:35:59,307 - INFO - 总训练时间: 2292.54 秒 (38.2 分钟)
2025-08-06 12:35:59,307 - INFO - 平均每轮训练时间: 1146.27 秒
2025-08-06 12:35:59,307 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_114850
2025-08-06 12:35:59,307 - INFO - 🔍 第2轮准确率记录: 0.5445
2025-08-06 12:36:01,739 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 12:36:01,739 - INFO -    - 当前门槛: 57.4476
2025-08-06 12:36:01,739 - INFO -    - 第2轮准确率: 54.4491
2025-08-06 12:36:01,739 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 12:36:01,739 - INFO - 训练完成 rml201801a Trial 20, 结果: {'best_val_acc': 0.49768865957890346, 'best_val_f1': 0.48920145098908, 'best_val_kappa': 0.4758490360823341, 'total_epochs': 2, 'total_training_time': 2292.5402960777283, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5444913487596414}
2025-08-06 12:36:01,740 - INFO - rml201801a Trial 20 早停训练，使用第2轮准确率: 0.5445
2025-08-06 12:36:01,742 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 12:36:01,742 - INFO - --------------------------------------------------------------------------------
2025-08-06 12:36:01,742 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 12:36:01,742 - INFO - #2 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 12:36:01,742 - INFO - #3 | 试验10 | 准确率:0.5659 | dropout=0.14440589784927163, lambda_lifting=0.015854139739503323
2025-08-06 12:36:01,742 - INFO - --------------------------------------------------------------------------------
2025-08-06 12:36:02,110 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 12:36:02,110 - INFO -    - dropout: 0.10255948279039259 (数据集特定 + 通用)
2025-08-06 12:36:02,111 - INFO -    - lambda_lifting: 0.024320486227991597
2025-08-06 12:36:02,121 - INFO - 📄 临时配置文件创建: /tmp/tmpiioco0lv.yaml
2025-08-06 12:36:02,121 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.10255948279039259, 'batch_size': 256}
2025-08-06 12:36:02,121 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.10255948279039259
2025-08-06 12:36:02,121 - INFO - ✅ 最终训练参数: lambda_lifting=0.024320486227991597, batch_size=128
2025-08-06 12:36:02,121 - INFO - rml201801a Trial 21: 参数 {'dropout': 0.10255948279039259, 'lambda_lifting': 0.024320486227991597}
2025-08-06 12:36:02,265 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 12:36:02,265 - INFO - ====================================================================================================
2025-08-06 12:36:02,265 - INFO - 🚀 开始训练 - rml201801a Trial 21
2025-08-06 12:36:02,265 - INFO - ====================================================================================================
2025-08-06 12:36:02,265 - INFO - 📋 本次训练参数:
2025-08-06 12:36:02,265 - INFO -   dropout: 0.1026
  lambda_lifting: 0.0243
2025-08-06 12:36:02,265 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 12:36:02,265 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 12:36:02,265 - INFO -   #2 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 12:36:02,265 - INFO -   #3 | Trial10 | 准确率:0.5659 | dropout=0.1444, lambda_lifting=0.0159
2025-08-06 12:36:02,265 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 12:36:02,265 - INFO - ====================================================================================================
2025-08-06 12:36:02,265 - INFO - 🎯 开始训练...
2025-08-06 12:36:02,265 - INFO - ====================================================================================================
2025-08-06 12:36:02,265 - INFO - 🎯 开始训练 rml201801a Trial 21 (第22次试验)
2025-08-06 12:36:02,265 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 12:36:02,313 - INFO - 开始训练，配置文件: /tmp/tmpiioco0lv.yaml
2025-08-06 12:36:02,313 - INFO - 数据集类型: rml201801a
2025-08-06 12:36:02,313 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_123602
2025-08-06 12:36:02,324 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_123602/configs/config_backup.yaml
2025-08-06 12:36:02,324 - INFO - 使用设备: cuda
2025-08-06 12:36:02,324 - INFO - 加载数据...
2025-08-06 12:43:03,208 - INFO - 创建模型...
2025-08-06 12:43:03,262 - INFO - 可训练参数数量: 867,608
2025-08-06 12:43:03,263 - INFO - 总参数数量: 867,608
2025-08-06 12:43:03,263 - INFO - 可训练参数比例: 100.00%
2025-08-06 12:43:03,264 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 12:43:03,264 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 22, 'trial_number': 21}
2025-08-06 12:43:03,264 - INFO - 📊 最少训练轮数: 2
2025-08-06 12:43:03,265 - INFO - Epoch 1/400
2025-08-06 13:01:14,054 - INFO - Train Loss: 1.5494, Train Acc: 47.04%
2025-08-06 13:02:11,884 - INFO - Val Loss: 1.3728, Val Acc: 53.31%, Macro-F1: 54.56%, Kappa: 0.5128
2025-08-06 13:02:11,884 - INFO - Epoch 1 训练时间: 1148.62 秒
2025-08-06 13:02:11,947 - INFO - 保存最佳模型，验证准确率: 53.31%, Macro-F1: 54.56%, Kappa: 0.5128
2025-08-06 13:02:11,947 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_123602/models/best_model.pth
2025-08-06 13:02:11,947 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_123602/models/model_epoch_1_acc_53.31.pth
2025-08-06 13:02:11,947 - INFO - Epoch 2/400
2025-08-06 13:20:16,498 - INFO - Train Loss: 1.3070, Train Acc: 55.92%
2025-08-06 13:21:13,516 - INFO - Val Loss: 1.2836, Val Acc: 56.67%, Macro-F1: 57.57%, Kappa: 0.5479
2025-08-06 13:21:13,516 - INFO - Epoch 2 训练时间: 1141.57 秒
2025-08-06 13:21:13,516 - INFO - 🔍 第2轮门槛检查: 准确率 56.6693%, 门槛 57.4476
2025-08-06 13:21:13,516 - INFO - ⚠️ 第2轮准确率 56.6693% 未达到门槛 57.4476，提前结束训练
2025-08-06 13:21:13,517 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_123602/results/training_history.json
2025-08-06 13:21:13,517 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_123602/results/training_summary.json
2025-08-06 13:21:13,517 - INFO - 训练完成！
2025-08-06 13:21:13,517 - INFO - 最佳验证准确率: 53.31%
2025-08-06 13:21:13,517 - INFO - 最佳Macro-F1: 54.56%
2025-08-06 13:21:13,517 - INFO - 最佳Kappa: 0.5128
2025-08-06 13:21:13,517 - INFO - 总训练时间: 2290.19 秒 (38.2 分钟)
2025-08-06 13:21:13,517 - INFO - 平均每轮训练时间: 1145.09 秒
2025-08-06 13:21:13,517 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_123602
2025-08-06 13:21:13,517 - INFO - 🔍 第2轮准确率记录: 0.5667
2025-08-06 13:21:15,633 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 13:21:15,633 - INFO -    - 当前门槛: 57.4476
2025-08-06 13:21:15,633 - INFO -    - 第2轮准确率: 56.6693
2025-08-06 13:21:15,633 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 13:21:15,633 - INFO - 训练完成 rml201801a Trial 21, 结果: {'best_val_acc': 0.5330727538044612, 'best_val_f1': 0.5455526520315161, 'best_val_kappa': 0.5127715691872639, 'total_epochs': 2, 'total_training_time': 2290.1887419223785, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5666927246195539}
2025-08-06 13:21:15,633 - INFO - rml201801a Trial 21 早停训练，使用第2轮准确率: 0.5667
2025-08-06 13:21:15,636 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 13:21:15,636 - INFO - --------------------------------------------------------------------------------
2025-08-06 13:21:15,636 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 13:21:15,636 - INFO - #2 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 13:21:15,636 - INFO - #3 | 试验11 | 准确率:0.5661 | dropout=0.11085351225993631, lambda_lifting=0.012639165448392015
2025-08-06 13:21:15,636 - INFO - --------------------------------------------------------------------------------
2025-08-06 13:21:15,976 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 13:21:15,977 - INFO -    - dropout: 0.10826720091836849 (数据集特定 + 通用)
2025-08-06 13:21:15,977 - INFO -    - lambda_lifting: 0.0465412101136831
2025-08-06 13:21:15,987 - INFO - 📄 临时配置文件创建: /tmp/tmpz45kvwhw.yaml
2025-08-06 13:21:15,988 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.10826720091836849, 'batch_size': 256}
2025-08-06 13:21:15,988 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.10826720091836849
2025-08-06 13:21:15,988 - INFO - ✅ 最终训练参数: lambda_lifting=0.0465412101136831, batch_size=128
2025-08-06 13:21:15,988 - INFO - rml201801a Trial 22: 参数 {'dropout': 0.10826720091836849, 'lambda_lifting': 0.0465412101136831}
2025-08-06 13:21:16,110 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 13:21:16,110 - INFO - ====================================================================================================
2025-08-06 13:21:16,110 - INFO - 🚀 开始训练 - rml201801a Trial 22
2025-08-06 13:21:16,110 - INFO - ====================================================================================================
2025-08-06 13:21:16,110 - INFO - 📋 本次训练参数:
2025-08-06 13:21:16,111 - INFO -   dropout: 0.1083
  lambda_lifting: 0.0465
2025-08-06 13:21:16,111 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 13:21:16,111 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 13:21:16,111 - INFO -   #2 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 13:21:16,111 - INFO -   #3 | Trial11 | 准确率:0.5661 | dropout=0.1109, lambda_lifting=0.0126
2025-08-06 13:21:16,111 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 13:21:16,111 - INFO - ====================================================================================================
2025-08-06 13:21:16,111 - INFO - 🎯 开始训练...
2025-08-06 13:21:16,111 - INFO - ====================================================================================================
2025-08-06 13:21:16,111 - INFO - 🎯 开始训练 rml201801a Trial 22 (第23次试验)
2025-08-06 13:21:16,111 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 13:21:16,159 - INFO - 开始训练，配置文件: /tmp/tmpz45kvwhw.yaml
2025-08-06 13:21:16,159 - INFO - 数据集类型: rml201801a
2025-08-06 13:21:16,159 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_132116
2025-08-06 13:21:16,170 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_132116/configs/config_backup.yaml
2025-08-06 13:21:16,170 - INFO - 使用设备: cuda
2025-08-06 13:21:16,170 - INFO - 加载数据...
2025-08-06 13:24:35,385 - INFO - 创建模型...
2025-08-06 13:24:35,456 - INFO - 可训练参数数量: 867,608
2025-08-06 13:24:35,456 - INFO - 总参数数量: 867,608
2025-08-06 13:24:35,457 - INFO - 可训练参数比例: 100.00%
2025-08-06 13:24:35,458 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 13:24:35,458 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 23, 'trial_number': 22}
2025-08-06 13:24:35,458 - INFO - 📊 最少训练轮数: 2
2025-08-06 13:24:35,458 - INFO - Epoch 1/400
2025-08-06 13:42:44,645 - INFO - Train Loss: 1.5573, Train Acc: 46.83%
2025-08-06 13:43:42,454 - INFO - Val Loss: 1.3635, Val Acc: 53.24%, Macro-F1: 54.52%, Kappa: 0.5121
2025-08-06 13:43:42,454 - INFO - Epoch 1 训练时间: 1147.00 秒
2025-08-06 13:43:42,516 - INFO - 保存最佳模型，验证准确率: 53.24%, Macro-F1: 54.52%, Kappa: 0.5121
2025-08-06 13:43:42,516 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_132116/models/best_model.pth
2025-08-06 13:43:42,516 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_132116/models/model_epoch_1_acc_53.24.pth
2025-08-06 13:43:42,516 - INFO - Epoch 2/400
2025-08-06 14:01:47,864 - INFO - Train Loss: 1.3108, Train Acc: 55.77%
2025-08-06 14:02:44,937 - INFO - Val Loss: 1.2780, Val Acc: 56.73%, Macro-F1: 57.89%, Kappa: 0.5485
2025-08-06 14:02:44,937 - INFO - Epoch 2 训练时间: 1142.42 秒
2025-08-06 14:02:44,937 - INFO - 🔍 第2轮门槛检查: 准确率 56.7323%, 门槛 57.4476
2025-08-06 14:02:44,937 - INFO - ⚠️ 第2轮准确率 56.7323% 未达到门槛 57.4476，提前结束训练
2025-08-06 14:02:44,938 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_132116/results/training_history.json
2025-08-06 14:02:44,938 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_132116/results/training_summary.json
2025-08-06 14:02:44,938 - INFO - 训练完成！
2025-08-06 14:02:44,938 - INFO - 最佳验证准确率: 53.24%
2025-08-06 14:02:44,938 - INFO - 最佳Macro-F1: 54.52%
2025-08-06 14:02:44,938 - INFO - 最佳Kappa: 0.5121
2025-08-06 14:02:44,938 - INFO - 总训练时间: 2289.42 秒 (38.2 分钟)
2025-08-06 14:02:44,938 - INFO - 平均每轮训练时间: 1144.71 秒
2025-08-06 14:02:44,938 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_132116
2025-08-06 14:02:44,938 - INFO - 🔍 第2轮准确率记录: 0.5673
2025-08-06 14:02:47,104 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 14:02:47,104 - INFO -    - 当前门槛: 57.4476
2025-08-06 14:02:47,104 - INFO -    - 第2轮准确率: 56.7323
2025-08-06 14:02:47,104 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 14:02:47,105 - INFO - 训练完成 rml201801a Trial 22, 结果: {'best_val_acc': 0.5324447571398792, 'best_val_f1': 0.5451517255205316, 'best_val_kappa': 0.5121162683198739, 'total_epochs': 2, 'total_training_time': 2289.4169969558716, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5673233270794246}
2025-08-06 14:02:47,105 - INFO - rml201801a Trial 22 早停训练，使用第2轮准确率: 0.5673
2025-08-06 14:02:47,107 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 14:02:47,107 - INFO - --------------------------------------------------------------------------------
2025-08-06 14:02:47,107 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 14:02:47,107 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 14:02:47,107 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 14:02:47,107 - INFO - --------------------------------------------------------------------------------
2025-08-06 14:02:47,469 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 14:02:47,469 - INFO -    - dropout: 0.1940292726535822 (数据集特定 + 通用)
2025-08-06 14:02:47,469 - INFO -    - lambda_lifting: 0.07449127104192901
2025-08-06 14:02:47,482 - INFO - 📄 临时配置文件创建: /tmp/tmp9xrebn4k.yaml
2025-08-06 14:02:47,482 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1940292726535822, 'batch_size': 256}
2025-08-06 14:02:47,483 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1940292726535822
2025-08-06 14:02:47,483 - INFO - ✅ 最终训练参数: lambda_lifting=0.07449127104192901, batch_size=128
2025-08-06 14:02:47,483 - INFO - rml201801a Trial 23: 参数 {'dropout': 0.1940292726535822, 'lambda_lifting': 0.07449127104192901}
2025-08-06 14:02:47,605 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 14:02:47,605 - INFO - ====================================================================================================
2025-08-06 14:02:47,605 - INFO - 🚀 开始训练 - rml201801a Trial 23
2025-08-06 14:02:47,605 - INFO - ====================================================================================================
2025-08-06 14:02:47,605 - INFO - 📋 本次训练参数:
2025-08-06 14:02:47,605 - INFO -   dropout: 0.1940
  lambda_lifting: 0.0745
2025-08-06 14:02:47,605 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 14:02:47,605 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 14:02:47,605 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 14:02:47,605 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 14:02:47,606 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 14:02:47,606 - INFO - ====================================================================================================
2025-08-06 14:02:47,606 - INFO - 🎯 开始训练...
2025-08-06 14:02:47,606 - INFO - ====================================================================================================
2025-08-06 14:02:47,606 - INFO - 🎯 开始训练 rml201801a Trial 23 (第24次试验)
2025-08-06 14:02:47,606 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 14:02:47,672 - INFO - 开始训练，配置文件: /tmp/tmp9xrebn4k.yaml
2025-08-06 14:02:47,672 - INFO - 数据集类型: rml201801a
2025-08-06 14:02:47,672 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_140247
2025-08-06 14:02:47,687 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_140247/configs/config_backup.yaml
2025-08-06 14:02:47,687 - INFO - 使用设备: cuda
2025-08-06 14:02:47,688 - INFO - 加载数据...
2025-08-06 14:04:56,569 - INFO - 创建模型...
2025-08-06 14:04:56,604 - INFO - 可训练参数数量: 867,608
2025-08-06 14:04:56,604 - INFO - 总参数数量: 867,608
2025-08-06 14:04:56,604 - INFO - 可训练参数比例: 100.00%
2025-08-06 14:04:56,605 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 14:04:56,605 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 24, 'trial_number': 23}
2025-08-06 14:04:56,605 - INFO - 📊 最少训练轮数: 2
2025-08-06 14:04:56,605 - INFO - Epoch 1/400
2025-08-06 14:23:04,330 - INFO - Train Loss: 1.6208, Train Acc: 44.68%
2025-08-06 14:24:02,264 - INFO - Val Loss: 1.4576, Val Acc: 49.51%, Macro-F1: 49.16%, Kappa: 0.4732
2025-08-06 14:24:02,264 - INFO - Epoch 1 训练时间: 1145.66 秒
2025-08-06 14:24:02,327 - INFO - 保存最佳模型，验证准确率: 49.51%, Macro-F1: 49.16%, Kappa: 0.4732
2025-08-06 14:24:02,327 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_140247/models/best_model.pth
2025-08-06 14:24:02,327 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_140247/models/model_epoch_1_acc_49.51.pth
2025-08-06 14:24:02,327 - INFO - Epoch 2/400
2025-08-06 14:42:06,595 - INFO - Train Loss: 1.3669, Train Acc: 54.08%
2025-08-06 14:43:03,687 - INFO - Val Loss: 1.3063, Val Acc: 56.22%, Macro-F1: 57.52%, Kappa: 0.5432
2025-08-06 14:43:03,687 - INFO - Epoch 2 训练时间: 1141.36 秒
2025-08-06 14:43:03,687 - INFO - 🔍 第2轮门槛检查: 准确率 56.2237%, 门槛 57.4476
2025-08-06 14:43:03,687 - INFO - ⚠️ 第2轮准确率 56.2237% 未达到门槛 57.4476，提前结束训练
2025-08-06 14:43:03,687 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_140247/results/training_history.json
2025-08-06 14:43:03,688 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_140247/results/training_summary.json
2025-08-06 14:43:03,688 - INFO - 训练完成！
2025-08-06 14:43:03,688 - INFO - 最佳验证准确率: 49.51%
2025-08-06 14:43:03,688 - INFO - 最佳Macro-F1: 49.16%
2025-08-06 14:43:03,688 - INFO - 最佳Kappa: 0.4732
2025-08-06 14:43:03,688 - INFO - 总训练时间: 2287.02 秒 (38.1 分钟)
2025-08-06 14:43:03,688 - INFO - 平均每轮训练时间: 1143.51 秒
2025-08-06 14:43:03,688 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_140247
2025-08-06 14:43:03,688 - INFO - 🔍 第2轮准确率记录: 0.5622
2025-08-06 14:43:05,841 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 14:43:05,841 - INFO -    - 当前门槛: 57.4476
2025-08-06 14:43:05,841 - INFO -    - 第2轮准确率: 56.2237
2025-08-06 14:43:05,841 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 14:43:05,842 - INFO - 训练完成 rml201801a Trial 23, 结果: {'best_val_acc': 0.4951401917865333, 'best_val_f1': 0.49162763679565225, 'best_val_kappa': 0.4731897653424695, 'total_epochs': 2, 'total_training_time': 2287.018234014511, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5622368146758391}
2025-08-06 14:43:05,842 - INFO - rml201801a Trial 23 早停训练，使用第2轮准确率: 0.5622
2025-08-06 14:43:05,844 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 14:43:05,844 - INFO - --------------------------------------------------------------------------------
2025-08-06 14:43:05,844 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 14:43:05,844 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 14:43:05,844 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 14:43:05,844 - INFO - --------------------------------------------------------------------------------
2025-08-06 14:43:06,172 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 14:43:06,172 - INFO -    - dropout: 0.1014707019418255 (数据集特定 + 通用)
2025-08-06 14:43:06,172 - INFO -    - lambda_lifting: 0.14844740618744584
2025-08-06 14:43:06,182 - INFO - 📄 临时配置文件创建: /tmp/tmpubz26lqm.yaml
2025-08-06 14:43:06,183 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1014707019418255, 'batch_size': 256}
2025-08-06 14:43:06,183 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1014707019418255
2025-08-06 14:43:06,183 - INFO - ✅ 最终训练参数: lambda_lifting=0.14844740618744584, batch_size=128
2025-08-06 14:43:06,183 - INFO - rml201801a Trial 24: 参数 {'dropout': 0.1014707019418255, 'lambda_lifting': 0.14844740618744584}
2025-08-06 14:43:06,284 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 14:43:06,284 - INFO - ====================================================================================================
2025-08-06 14:43:06,285 - INFO - 🚀 开始训练 - rml201801a Trial 24
2025-08-06 14:43:06,285 - INFO - ====================================================================================================
2025-08-06 14:43:06,285 - INFO - 📋 本次训练参数:
2025-08-06 14:43:06,285 - INFO -   dropout: 0.1015
  lambda_lifting: 0.1484
2025-08-06 14:43:06,285 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 14:43:06,285 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 14:43:06,285 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 14:43:06,285 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 14:43:06,285 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 14:43:06,285 - INFO - ====================================================================================================
2025-08-06 14:43:06,285 - INFO - 🎯 开始训练...
2025-08-06 14:43:06,285 - INFO - ====================================================================================================
2025-08-06 14:43:06,285 - INFO - 🎯 开始训练 rml201801a Trial 24 (第25次试验)
2025-08-06 14:43:06,285 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 14:43:06,333 - INFO - 开始训练，配置文件: /tmp/tmpubz26lqm.yaml
2025-08-06 14:43:06,333 - INFO - 数据集类型: rml201801a
2025-08-06 14:43:06,333 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_144306
2025-08-06 14:43:06,344 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_144306/configs/config_backup.yaml
2025-08-06 14:43:06,344 - INFO - 使用设备: cuda
2025-08-06 14:43:06,344 - INFO - 加载数据...
2025-08-06 14:45:18,494 - INFO - 创建模型...
2025-08-06 14:45:18,556 - INFO - 可训练参数数量: 867,608
2025-08-06 14:45:18,556 - INFO - 总参数数量: 867,608
2025-08-06 14:45:18,556 - INFO - 可训练参数比例: 100.00%
2025-08-06 14:45:18,557 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 14:45:18,557 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 25, 'trial_number': 24}
2025-08-06 14:45:18,557 - INFO - 📊 最少训练轮数: 2
2025-08-06 14:45:18,557 - INFO - Epoch 1/400
2025-08-06 15:03:25,745 - INFO - Train Loss: 1.6023, Train Acc: 45.73%
2025-08-06 15:04:23,497 - INFO - Val Loss: 1.4275, Val Acc: 50.90%, Macro-F1: 50.55%, Kappa: 0.4877
2025-08-06 15:04:23,497 - INFO - Epoch 1 训练时间: 1144.94 秒
2025-08-06 15:04:23,559 - INFO - 保存最佳模型，验证准确率: 50.90%, Macro-F1: 50.55%, Kappa: 0.4877
2025-08-06 15:04:23,559 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_144306/models/best_model.pth
2025-08-06 15:04:23,559 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_144306/models/model_epoch_1_acc_50.90.pth
2025-08-06 15:04:23,559 - INFO - Epoch 2/400
2025-08-06 15:22:28,137 - INFO - Train Loss: 1.3501, Train Acc: 54.81%
2025-08-06 15:23:25,146 - INFO - Val Loss: 1.3096, Val Acc: 56.64%, Macro-F1: 58.34%, Kappa: 0.5476
2025-08-06 15:23:25,147 - INFO - Epoch 2 训练时间: 1141.59 秒
2025-08-06 15:23:25,147 - INFO - 🔍 第2轮门槛检查: 准确率 56.6443%, 门槛 57.4476
2025-08-06 15:23:25,147 - INFO - ⚠️ 第2轮准确率 56.6443% 未达到门槛 57.4476，提前结束训练
2025-08-06 15:23:25,147 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_144306/results/training_history.json
2025-08-06 15:23:25,147 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_144306/results/training_summary.json
2025-08-06 15:23:25,147 - INFO - 训练完成！
2025-08-06 15:23:25,147 - INFO - 最佳验证准确率: 50.90%
2025-08-06 15:23:25,147 - INFO - 最佳Macro-F1: 50.55%
2025-08-06 15:23:25,147 - INFO - 最佳Kappa: 0.4877
2025-08-06 15:23:25,147 - INFO - 总训练时间: 2286.53 秒 (38.1 分钟)
2025-08-06 15:23:25,147 - INFO - 平均每轮训练时间: 1143.26 秒
2025-08-06 15:23:25,147 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_144306
2025-08-06 15:23:25,148 - INFO - 🔍 第2轮准确率记录: 0.5664
2025-08-06 15:23:27,550 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 15:23:27,550 - INFO -    - 当前门槛: 57.4476
2025-08-06 15:23:27,550 - INFO -    - 第2轮准确率: 56.6443
2025-08-06 15:23:27,550 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 15:23:27,550 - INFO - 训练完成 rml201801a Trial 24, 结果: {'best_val_acc': 0.5090082343131124, 'best_val_f1': 0.5055217579808883, 'best_val_kappa': 0.4876607662397694, 'total_epochs': 2, 'total_training_time': 2286.5270788669586, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5664425682718366}
2025-08-06 15:23:27,550 - INFO - rml201801a Trial 24 早停训练，使用第2轮准确率: 0.5664
2025-08-06 15:23:27,553 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 15:23:27,553 - INFO - --------------------------------------------------------------------------------
2025-08-06 15:23:27,553 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 15:23:27,553 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 15:23:27,553 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 15:23:27,553 - INFO - --------------------------------------------------------------------------------
2025-08-06 15:23:27,846 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-06 15:23:27,846 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 5 次)
2025-08-06 15:23:27,874 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 15:23:27,874 - INFO -    - dropout: 0.2984542243356675 (数据集特定 + 通用)
2025-08-06 15:23:27,874 - INFO -    - lambda_lifting: 0.26270239003648516
2025-08-06 15:23:27,884 - INFO - 📄 临时配置文件创建: /tmp/tmpe1717kkm.yaml
2025-08-06 15:23:27,885 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.2984542243356675, 'batch_size': 256}
2025-08-06 15:23:27,885 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.2984542243356675
2025-08-06 15:23:27,885 - INFO - ✅ 最终训练参数: lambda_lifting=0.26270239003648516, batch_size=128
2025-08-06 15:23:27,885 - INFO - rml201801a Trial 25: 参数 {'dropout': 0.2984542243356675, 'lambda_lifting': 0.26270239003648516}
2025-08-06 15:23:27,983 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 15:23:27,984 - INFO - ====================================================================================================
2025-08-06 15:23:27,984 - INFO - 🚀 开始训练 - rml201801a Trial 25
2025-08-06 15:23:27,984 - INFO - ====================================================================================================
2025-08-06 15:23:27,984 - INFO - 📋 本次训练参数:
2025-08-06 15:23:27,984 - INFO -   dropout: 0.2985
  lambda_lifting: 0.2627
2025-08-06 15:23:27,984 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 15:23:27,984 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 15:23:27,984 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 15:23:27,984 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 15:23:27,984 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 15:23:27,984 - INFO - ====================================================================================================
2025-08-06 15:23:27,984 - INFO - 🎯 开始训练...
2025-08-06 15:23:27,984 - INFO - ====================================================================================================
2025-08-06 15:23:27,984 - INFO - 🎯 开始训练 rml201801a Trial 25 (第26次试验)
2025-08-06 15:23:27,984 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 15:23:28,032 - INFO - 开始训练，配置文件: /tmp/tmpe1717kkm.yaml
2025-08-06 15:23:28,032 - INFO - 数据集类型: rml201801a
2025-08-06 15:23:28,032 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_152328
2025-08-06 15:23:28,043 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_152328/configs/config_backup.yaml
2025-08-06 15:23:28,043 - INFO - 使用设备: cuda
2025-08-06 15:23:28,043 - INFO - 加载数据...
2025-08-06 15:25:37,878 - INFO - 创建模型...
2025-08-06 15:25:37,936 - INFO - 可训练参数数量: 867,608
2025-08-06 15:25:37,936 - INFO - 总参数数量: 867,608
2025-08-06 15:25:37,936 - INFO - 可训练参数比例: 100.00%
2025-08-06 15:25:37,937 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 15:25:37,937 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 26, 'trial_number': 25}
2025-08-06 15:25:37,937 - INFO - 📊 最少训练轮数: 2
2025-08-06 15:25:37,937 - INFO - Epoch 1/400
2025-08-06 15:43:43,906 - INFO - Train Loss: 1.6735, Train Acc: 43.59%
2025-08-06 15:44:41,550 - INFO - Val Loss: 1.4624, Val Acc: 49.39%, Macro-F1: 48.74%, Kappa: 0.4719
2025-08-06 15:44:41,550 - INFO - Epoch 1 训练时间: 1143.61 秒
2025-08-06 15:44:41,612 - INFO - 保存最佳模型，验证准确率: 49.39%, Macro-F1: 48.74%, Kappa: 0.4719
2025-08-06 15:44:41,613 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_152328/models/best_model.pth
2025-08-06 15:44:41,613 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_152328/models/model_epoch_1_acc_49.39.pth
2025-08-06 15:44:41,613 - INFO - Epoch 2/400
2025-08-06 16:02:44,784 - INFO - Train Loss: 1.4089, Train Acc: 52.57%
2025-08-06 16:03:41,744 - INFO - Val Loss: 1.4117, Val Acc: 51.55%, Macro-F1: 53.28%, Kappa: 0.4944
2025-08-06 16:03:41,744 - INFO - Epoch 2 训练时间: 1140.13 秒
2025-08-06 16:03:41,744 - INFO - 🔍 第2轮门槛检查: 准确率 51.5471%, 门槛 57.4476
2025-08-06 16:03:41,744 - INFO - ⚠️ 第2轮准确率 51.5471% 未达到门槛 57.4476，提前结束训练
2025-08-06 16:03:41,745 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_152328/results/training_history.json
2025-08-06 16:03:41,745 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_152328/results/training_summary.json
2025-08-06 16:03:41,745 - INFO - 训练完成！
2025-08-06 16:03:41,745 - INFO - 最佳验证准确率: 49.39%
2025-08-06 16:03:41,745 - INFO - 最佳Macro-F1: 48.74%
2025-08-06 16:03:41,745 - INFO - 最佳Kappa: 0.4719
2025-08-06 16:03:41,745 - INFO - 总训练时间: 2283.74 秒 (38.1 分钟)
2025-08-06 16:03:41,745 - INFO - 平均每轮训练时间: 1141.87 秒
2025-08-06 16:03:41,745 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_152328
2025-08-06 16:03:41,745 - INFO - 🔍 第2轮准确率记录: 0.5155
2025-08-06 16:03:44,068 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 16:03:44,069 - INFO -    - 当前门槛: 57.4476
2025-08-06 16:03:44,069 - INFO -    - 第2轮准确率: 51.5471
2025-08-06 16:03:44,069 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 16:03:44,069 - INFO - 训练完成 rml201801a Trial 25, 结果: {'best_val_acc': 0.49391025641025643, 'best_val_f1': 0.4873624932447431, 'best_val_kappa': 0.47190635451505014, 'total_epochs': 2, 'total_training_time': 2283.744827747345, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5154706066291432}
2025-08-06 16:03:44,069 - INFO - rml201801a Trial 25 早停训练，使用第2轮准确率: 0.5155
2025-08-06 16:03:44,071 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 16:03:44,071 - INFO - --------------------------------------------------------------------------------
2025-08-06 16:03:44,071 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 16:03:44,072 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 16:03:44,072 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 16:03:44,072 - INFO - --------------------------------------------------------------------------------
2025-08-06 16:03:44,399 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 16:03:44,399 - INFO -    - dropout: 0.15801474603995552 (数据集特定 + 通用)
2025-08-06 16:03:44,399 - INFO -    - lambda_lifting: 0.05652996957322522
2025-08-06 16:03:44,410 - INFO - 📄 临时配置文件创建: /tmp/tmp11d6aruy.yaml
2025-08-06 16:03:44,410 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.15801474603995552, 'batch_size': 256}
2025-08-06 16:03:44,410 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.15801474603995552
2025-08-06 16:03:44,410 - INFO - ✅ 最终训练参数: lambda_lifting=0.05652996957322522, batch_size=128
2025-08-06 16:03:44,410 - INFO - rml201801a Trial 26: 参数 {'dropout': 0.15801474603995552, 'lambda_lifting': 0.05652996957322522}
2025-08-06 16:03:44,515 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 16:03:44,515 - INFO - ====================================================================================================
2025-08-06 16:03:44,515 - INFO - 🚀 开始训练 - rml201801a Trial 26
2025-08-06 16:03:44,515 - INFO - ====================================================================================================
2025-08-06 16:03:44,515 - INFO - 📋 本次训练参数:
2025-08-06 16:03:44,515 - INFO -   dropout: 0.1580
  lambda_lifting: 0.0565
2025-08-06 16:03:44,515 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 16:03:44,515 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 16:03:44,516 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 16:03:44,516 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 16:03:44,516 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 16:03:44,516 - INFO - ====================================================================================================
2025-08-06 16:03:44,516 - INFO - 🎯 开始训练...
2025-08-06 16:03:44,516 - INFO - ====================================================================================================
2025-08-06 16:03:44,516 - INFO - 🎯 开始训练 rml201801a Trial 26 (第27次试验)
2025-08-06 16:03:44,516 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 16:03:44,563 - INFO - 开始训练，配置文件: /tmp/tmp11d6aruy.yaml
2025-08-06 16:03:44,563 - INFO - 数据集类型: rml201801a
2025-08-06 16:03:44,563 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_160344
2025-08-06 16:03:44,574 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_160344/configs/config_backup.yaml
2025-08-06 16:03:44,574 - INFO - 使用设备: cuda
2025-08-06 16:03:44,574 - INFO - 加载数据...
2025-08-06 16:05:53,892 - INFO - 创建模型...
2025-08-06 16:05:53,934 - INFO - 可训练参数数量: 867,608
2025-08-06 16:05:53,934 - INFO - 总参数数量: 867,608
2025-08-06 16:05:53,934 - INFO - 可训练参数比例: 100.00%
2025-08-06 16:05:53,935 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 16:05:53,935 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 27, 'trial_number': 26}
2025-08-06 16:05:53,935 - INFO - 📊 最少训练轮数: 2
2025-08-06 16:05:53,935 - INFO - Epoch 1/400
2025-08-06 16:24:02,607 - INFO - Train Loss: 1.5845, Train Acc: 45.72%
2025-08-06 16:25:00,518 - INFO - Val Loss: 1.4518, Val Acc: 50.19%, Macro-F1: 51.17%, Kappa: 0.4803
2025-08-06 16:25:00,518 - INFO - Epoch 1 训练时间: 1146.58 秒
2025-08-06 16:25:00,580 - INFO - 保存最佳模型，验证准确率: 50.19%, Macro-F1: 51.17%, Kappa: 0.4803
2025-08-06 16:25:00,580 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_160344/models/best_model.pth
2025-08-06 16:25:00,580 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_160344/models/model_epoch_1_acc_50.19.pth
2025-08-06 16:25:00,580 - INFO - Epoch 2/400
2025-08-06 16:43:05,315 - INFO - Train Loss: 1.3349, Train Acc: 54.85%
2025-08-06 16:44:02,314 - INFO - Val Loss: 1.2872, Val Acc: 56.31%, Macro-F1: 57.11%, Kappa: 0.5441
2025-08-06 16:44:02,314 - INFO - Epoch 2 训练时间: 1141.73 秒
2025-08-06 16:44:02,314 - INFO - 🔍 第2轮门槛检查: 准确率 56.3110%, 门槛 57.4476
2025-08-06 16:44:02,314 - INFO - ⚠️ 第2轮准确率 56.3110% 未达到门槛 57.4476，提前结束训练
2025-08-06 16:44:02,315 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_160344/results/training_history.json
2025-08-06 16:44:02,315 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_160344/results/training_summary.json
2025-08-06 16:44:02,315 - INFO - 训练完成！
2025-08-06 16:44:02,315 - INFO - 最佳验证准确率: 50.19%
2025-08-06 16:44:02,315 - INFO - 最佳Macro-F1: 51.17%
2025-08-06 16:44:02,315 - INFO - 最佳Kappa: 0.4803
2025-08-06 16:44:02,315 - INFO - 总训练时间: 2288.32 秒 (38.1 分钟)
2025-08-06 16:44:02,315 - INFO - 平均每轮训练时间: 1144.16 秒
2025-08-06 16:44:02,315 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_160344
2025-08-06 16:44:02,315 - INFO - 🔍 第2轮准确率记录: 0.5631
2025-08-06 16:44:04,804 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 16:44:04,804 - INFO -    - 当前门槛: 57.4476
2025-08-06 16:44:04,804 - INFO -    - 第2轮准确率: 56.3110
2025-08-06 16:44:04,804 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 16:44:04,804 - INFO - 训练完成 rml201801a Trial 26, 结果: {'best_val_acc': 0.5019230769230769, 'best_val_f1': 0.5116520069721507, 'best_val_kappa': 0.48026755852842806, 'total_epochs': 2, 'total_training_time': 2288.3174653053284, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.563109756097561}
2025-08-06 16:44:04,804 - INFO - rml201801a Trial 26 早停训练，使用第2轮准确率: 0.5631
2025-08-06 16:44:04,807 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 16:44:04,807 - INFO - --------------------------------------------------------------------------------
2025-08-06 16:44:04,807 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 16:44:04,807 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 16:44:04,807 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 16:44:04,807 - INFO - --------------------------------------------------------------------------------
2025-08-06 16:44:05,124 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 16:44:05,125 - INFO -    - dropout: 0.28545044929670754 (数据集特定 + 通用)
2025-08-06 16:44:05,125 - INFO -    - lambda_lifting: 0.14825810294567573
2025-08-06 16:44:05,135 - INFO - 📄 临时配置文件创建: /tmp/tmphzfeg9x_.yaml
2025-08-06 16:44:05,135 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.28545044929670754, 'batch_size': 256}
2025-08-06 16:44:05,135 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.28545044929670754
2025-08-06 16:44:05,136 - INFO - ✅ 最终训练参数: lambda_lifting=0.14825810294567573, batch_size=128
2025-08-06 16:44:05,136 - INFO - rml201801a Trial 27: 参数 {'dropout': 0.28545044929670754, 'lambda_lifting': 0.14825810294567573}
2025-08-06 16:44:05,235 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 16:44:05,235 - INFO - ====================================================================================================
2025-08-06 16:44:05,235 - INFO - 🚀 开始训练 - rml201801a Trial 27
2025-08-06 16:44:05,235 - INFO - ====================================================================================================
2025-08-06 16:44:05,235 - INFO - 📋 本次训练参数:
2025-08-06 16:44:05,235 - INFO -   dropout: 0.2855
  lambda_lifting: 0.1483
2025-08-06 16:44:05,235 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 16:44:05,235 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 16:44:05,235 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 16:44:05,235 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 16:44:05,235 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 16:44:05,235 - INFO - ====================================================================================================
2025-08-06 16:44:05,235 - INFO - 🎯 开始训练...
2025-08-06 16:44:05,236 - INFO - ====================================================================================================
2025-08-06 16:44:05,236 - INFO - 🎯 开始训练 rml201801a Trial 27 (第28次试验)
2025-08-06 16:44:05,236 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 16:44:05,283 - INFO - 开始训练，配置文件: /tmp/tmphzfeg9x_.yaml
2025-08-06 16:44:05,283 - INFO - 数据集类型: rml201801a
2025-08-06 16:44:05,283 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_164405
2025-08-06 16:44:05,294 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_164405/configs/config_backup.yaml
2025-08-06 16:44:05,294 - INFO - 使用设备: cuda
2025-08-06 16:44:05,294 - INFO - 加载数据...
2025-08-06 16:46:12,349 - INFO - 创建模型...
2025-08-06 16:46:12,389 - INFO - 可训练参数数量: 867,608
2025-08-06 16:46:12,389 - INFO - 总参数数量: 867,608
2025-08-06 16:46:12,389 - INFO - 可训练参数比例: 100.00%
2025-08-06 16:46:12,390 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 16:46:12,390 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 28, 'trial_number': 27}
2025-08-06 16:46:12,390 - INFO - 📊 最少训练轮数: 2
2025-08-06 16:46:12,390 - INFO - Epoch 1/400
2025-08-06 17:04:20,019 - INFO - Train Loss: 1.6559, Train Acc: 43.87%
2025-08-06 17:05:17,870 - INFO - Val Loss: 1.4286, Val Acc: 50.57%, Macro-F1: 51.35%, Kappa: 0.4842
2025-08-06 17:05:17,871 - INFO - Epoch 1 训练时间: 1145.48 秒
2025-08-06 17:05:17,932 - INFO - 保存最佳模型，验证准确率: 50.57%, Macro-F1: 51.35%, Kappa: 0.4842
2025-08-06 17:05:17,932 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_164405/models/best_model.pth
2025-08-06 17:05:17,932 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_164405/models/model_epoch_1_acc_50.57.pth
2025-08-06 17:05:17,932 - INFO - Epoch 2/400
2025-08-06 17:23:22,662 - INFO - Train Loss: 1.4040, Train Acc: 52.40%
2025-08-06 17:24:19,740 - INFO - Val Loss: 1.4531, Val Acc: 50.72%, Macro-F1: 50.70%, Kappa: 0.4858
2025-08-06 17:24:19,741 - INFO - Epoch 2 训练时间: 1141.81 秒
2025-08-06 17:24:19,741 - INFO - 🔍 第2轮门槛检查: 准确率 50.7239%, 门槛 57.4476
2025-08-06 17:24:19,741 - INFO - ⚠️ 第2轮准确率 50.7239% 未达到门槛 57.4476，提前结束训练
2025-08-06 17:24:19,741 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_164405/results/training_history.json
2025-08-06 17:24:19,741 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_164405/results/training_summary.json
2025-08-06 17:24:19,741 - INFO - 训练完成！
2025-08-06 17:24:19,741 - INFO - 最佳验证准确率: 50.57%
2025-08-06 17:24:19,741 - INFO - 最佳Macro-F1: 51.35%
2025-08-06 17:24:19,741 - INFO - 最佳Kappa: 0.4842
2025-08-06 17:24:19,741 - INFO - 总训练时间: 2287.29 秒 (38.1 分钟)
2025-08-06 17:24:19,741 - INFO - 平均每轮训练时间: 1143.64 秒
2025-08-06 17:24:19,741 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_164405
2025-08-06 17:24:19,741 - INFO - 🔍 第2轮准确率记录: 0.5072
2025-08-06 17:24:22,239 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 17:24:22,239 - INFO -    - 当前门槛: 57.4476
2025-08-06 17:24:22,239 - INFO -    - 第2轮准确率: 50.7239
2025-08-06 17:24:22,239 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 17:24:22,240 - INFO - 训练完成 rml201801a Trial 27, 结果: {'best_val_acc': 0.5056519699812383, 'best_val_f1': 0.5135039354535977, 'best_val_kappa': 0.48415857737172685, 'total_epochs': 2, 'total_training_time': 2287.2889637947083, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.50723889931207}
2025-08-06 17:24:22,240 - INFO - rml201801a Trial 27 早停训练，使用第2轮准确率: 0.5072
2025-08-06 17:24:22,242 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 17:24:22,242 - INFO - --------------------------------------------------------------------------------
2025-08-06 17:24:22,242 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 17:24:22,242 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 17:24:22,242 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 17:24:22,242 - INFO - --------------------------------------------------------------------------------
2025-08-06 17:24:22,564 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 17:24:22,564 - INFO -    - dropout: 0.19976922424547017 (数据集特定 + 通用)
2025-08-06 17:24:22,564 - INFO -    - lambda_lifting: 0.09623976230807928
2025-08-06 17:24:22,575 - INFO - 📄 临时配置文件创建: /tmp/tmp_obp2zoi.yaml
2025-08-06 17:24:22,575 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.19976922424547017, 'batch_size': 256}
2025-08-06 17:24:22,575 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.19976922424547017
2025-08-06 17:24:22,575 - INFO - ✅ 最终训练参数: lambda_lifting=0.09623976230807928, batch_size=128
2025-08-06 17:24:22,575 - INFO - rml201801a Trial 28: 参数 {'dropout': 0.19976922424547017, 'lambda_lifting': 0.09623976230807928}
2025-08-06 17:24:22,669 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 17:24:22,670 - INFO - ====================================================================================================
2025-08-06 17:24:22,670 - INFO - 🚀 开始训练 - rml201801a Trial 28
2025-08-06 17:24:22,670 - INFO - ====================================================================================================
2025-08-06 17:24:22,670 - INFO - 📋 本次训练参数:
2025-08-06 17:24:22,670 - INFO -   dropout: 0.1998
  lambda_lifting: 0.0962
2025-08-06 17:24:22,670 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 17:24:22,670 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 17:24:22,670 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 17:24:22,670 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 17:24:22,670 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 17:24:22,670 - INFO - ====================================================================================================
2025-08-06 17:24:22,670 - INFO - 🎯 开始训练...
2025-08-06 17:24:22,670 - INFO - ====================================================================================================
2025-08-06 17:24:22,670 - INFO - 🎯 开始训练 rml201801a Trial 28 (第29次试验)
2025-08-06 17:24:22,670 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 17:24:22,718 - INFO - 开始训练，配置文件: /tmp/tmp_obp2zoi.yaml
2025-08-06 17:24:22,718 - INFO - 数据集类型: rml201801a
2025-08-06 17:24:22,718 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_172422
2025-08-06 17:24:22,729 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_172422/configs/config_backup.yaml
2025-08-06 17:24:22,729 - INFO - 使用设备: cuda
2025-08-06 17:24:22,729 - INFO - 加载数据...
2025-08-06 17:26:32,507 - INFO - 创建模型...
2025-08-06 17:26:32,557 - INFO - 可训练参数数量: 867,608
2025-08-06 17:26:32,557 - INFO - 总参数数量: 867,608
2025-08-06 17:26:32,557 - INFO - 可训练参数比例: 100.00%
2025-08-06 17:26:32,558 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 17:26:32,558 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 29, 'trial_number': 28}
2025-08-06 17:26:32,558 - INFO - 📊 最少训练轮数: 2
2025-08-06 17:26:32,558 - INFO - Epoch 1/400
2025-08-06 17:44:40,913 - INFO - Train Loss: 1.6192, Train Acc: 44.93%
2025-08-06 17:45:39,098 - INFO - Val Loss: 1.4032, Val Acc: 51.72%, Macro-F1: 52.11%, Kappa: 0.4962
2025-08-06 17:45:39,099 - INFO - Epoch 1 训练时间: 1146.54 秒
2025-08-06 17:45:39,162 - INFO - 保存最佳模型，验证准确率: 51.72%, Macro-F1: 52.11%, Kappa: 0.4962
2025-08-06 17:45:39,162 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_172422/models/best_model.pth
2025-08-06 17:45:39,162 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_172422/models/model_epoch_1_acc_51.72.pth
2025-08-06 17:45:39,162 - INFO - Epoch 2/400
2025-08-06 18:03:44,980 - INFO - Train Loss: 1.3650, Train Acc: 54.14%
2025-08-06 18:04:41,990 - INFO - Val Loss: 1.3236, Val Acc: 55.93%, Macro-F1: 57.63%, Kappa: 0.5402
2025-08-06 18:04:41,990 - INFO - Epoch 2 训练时间: 1142.83 秒
2025-08-06 18:04:41,991 - INFO - 🔍 第2轮门槛检查: 准确率 55.9337%, 门槛 57.4476
2025-08-06 18:04:41,991 - INFO - ⚠️ 第2轮准确率 55.9337% 未达到门槛 57.4476，提前结束训练
2025-08-06 18:04:41,991 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_172422/results/training_history.json
2025-08-06 18:04:41,991 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_172422/results/training_summary.json
2025-08-06 18:04:41,991 - INFO - 训练完成！
2025-08-06 18:04:41,991 - INFO - 最佳验证准确率: 51.72%
2025-08-06 18:04:41,991 - INFO - 最佳Macro-F1: 52.11%
2025-08-06 18:04:41,991 - INFO - 最佳Kappa: 0.4962
2025-08-06 18:04:41,991 - INFO - 总训练时间: 2289.37 秒 (38.2 分钟)
2025-08-06 18:04:41,991 - INFO - 平均每轮训练时间: 1144.68 秒
2025-08-06 18:04:41,991 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_172422
2025-08-06 18:04:41,991 - INFO - 🔍 第2轮准确率记录: 0.5593
2025-08-06 18:04:44,711 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 18:04:44,712 - INFO -    - 当前门槛: 57.4476
2025-08-06 18:04:44,712 - INFO -    - 第2轮准确率: 55.9337
2025-08-06 18:04:44,712 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 18:04:44,712 - INFO - 训练完成 rml201801a Trial 28, 结果: {'best_val_acc': 0.5171852199291224, 'best_val_f1': 0.5211411635798199, 'best_val_kappa': 0.496193272969519, 'total_epochs': 2, 'total_training_time': 2289.3693342208862, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5593365645194913}
2025-08-06 18:04:44,712 - INFO - rml201801a Trial 28 早停训练，使用第2轮准确率: 0.5593
2025-08-06 18:04:44,715 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 18:04:44,715 - INFO - --------------------------------------------------------------------------------
2025-08-06 18:04:44,715 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 18:04:44,715 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 18:04:44,715 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 18:04:44,715 - INFO - --------------------------------------------------------------------------------
2025-08-06 18:04:45,281 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 18:04:45,281 - INFO -    - dropout: 0.30568188285243714 (数据集特定 + 通用)
2025-08-06 18:04:45,281 - INFO -    - lambda_lifting: 0.05078182737946532
2025-08-06 18:04:45,291 - INFO - 📄 临时配置文件创建: /tmp/tmp00dnag6h.yaml
2025-08-06 18:04:45,291 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.30568188285243714, 'batch_size': 256}
2025-08-06 18:04:45,291 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.30568188285243714
2025-08-06 18:04:45,291 - INFO - ✅ 最终训练参数: lambda_lifting=0.05078182737946532, batch_size=128
2025-08-06 18:04:45,292 - INFO - rml201801a Trial 29: 参数 {'dropout': 0.30568188285243714, 'lambda_lifting': 0.05078182737946532}
2025-08-06 18:04:45,374 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 18:04:45,375 - INFO - ====================================================================================================
2025-08-06 18:04:45,375 - INFO - 🚀 开始训练 - rml201801a Trial 29
2025-08-06 18:04:45,375 - INFO - ====================================================================================================
2025-08-06 18:04:45,375 - INFO - 📋 本次训练参数:
2025-08-06 18:04:45,375 - INFO -   dropout: 0.3057
  lambda_lifting: 0.0508
2025-08-06 18:04:45,375 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 18:04:45,375 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 18:04:45,375 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 18:04:45,375 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 18:04:45,375 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 18:04:45,375 - INFO - ====================================================================================================
2025-08-06 18:04:45,375 - INFO - 🎯 开始训练...
2025-08-06 18:04:45,375 - INFO - ====================================================================================================
2025-08-06 18:04:45,375 - INFO - 🎯 开始训练 rml201801a Trial 29 (第30次试验)
2025-08-06 18:04:45,375 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 18:04:45,423 - INFO - 开始训练，配置文件: /tmp/tmp00dnag6h.yaml
2025-08-06 18:04:45,423 - INFO - 数据集类型: rml201801a
2025-08-06 18:04:45,423 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_180445
2025-08-06 18:04:45,434 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_180445/configs/config_backup.yaml
2025-08-06 18:04:45,434 - INFO - 使用设备: cuda
2025-08-06 18:04:45,434 - INFO - 加载数据...
2025-08-06 18:06:55,150 - INFO - 创建模型...
2025-08-06 18:06:55,230 - INFO - 可训练参数数量: 867,608
2025-08-06 18:06:55,230 - INFO - 总参数数量: 867,608
2025-08-06 18:06:55,230 - INFO - 可训练参数比例: 100.00%
2025-08-06 18:06:55,231 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 18:06:55,231 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 30, 'trial_number': 29}
2025-08-06 18:06:55,231 - INFO - 📊 最少训练轮数: 2
2025-08-06 18:06:55,231 - INFO - Epoch 1/400
2025-08-06 18:25:03,542 - INFO - Train Loss: 1.6325, Train Acc: 44.26%
2025-08-06 18:26:01,574 - INFO - Val Loss: 1.4272, Val Acc: 50.44%, Macro-F1: 50.41%, Kappa: 0.4828
2025-08-06 18:26:01,574 - INFO - Epoch 1 训练时间: 1146.34 秒
2025-08-06 18:26:01,635 - INFO - 保存最佳模型，验证准确率: 50.44%, Macro-F1: 50.41%, Kappa: 0.4828
2025-08-06 18:26:01,636 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_180445/models/best_model.pth
2025-08-06 18:26:01,636 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_180445/models/model_epoch_1_acc_50.44.pth
2025-08-06 18:26:01,636 - INFO - Epoch 2/400
2025-08-06 18:44:07,705 - INFO - Train Loss: 1.3945, Train Acc: 52.85%
2025-08-06 18:45:04,930 - INFO - Val Loss: 1.3982, Val Acc: 53.88%, Macro-F1: 55.12%, Kappa: 0.5188
2025-08-06 18:45:04,930 - INFO - Epoch 2 训练时间: 1143.29 秒
2025-08-06 18:45:04,930 - INFO - 🔍 第2轮门槛检查: 准确率 53.8839%, 门槛 57.4476
2025-08-06 18:45:04,930 - INFO - ⚠️ 第2轮准确率 53.8839% 未达到门槛 57.4476，提前结束训练
2025-08-06 18:45:04,930 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_180445/results/training_history.json
2025-08-06 18:45:04,931 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_180445/results/training_summary.json
2025-08-06 18:45:04,931 - INFO - 训练完成！
2025-08-06 18:45:04,931 - INFO - 最佳验证准确率: 50.44%
2025-08-06 18:45:04,931 - INFO - 最佳Macro-F1: 50.41%
2025-08-06 18:45:04,931 - INFO - 最佳Kappa: 0.4828
2025-08-06 18:45:04,931 - INFO - 总训练时间: 2289.64 秒 (38.2 分钟)
2025-08-06 18:45:04,931 - INFO - 平均每轮训练时间: 1144.82 秒
2025-08-06 18:45:04,931 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_180445
2025-08-06 18:45:04,931 - INFO - 🔍 第2轮准确率记录: 0.5388
2025-08-06 18:45:07,825 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 18:45:07,826 - INFO -    - 当前门槛: 57.4476
2025-08-06 18:45:07,826 - INFO -    - 第2轮准确率: 53.8839
2025-08-06 18:45:07,826 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 18:45:07,826 - INFO - 训练完成 rml201801a Trial 29, 结果: {'best_val_acc': 0.5043568897227434, 'best_val_f1': 0.5041249141459261, 'best_val_kappa': 0.48280718927590616, 'total_epochs': 2, 'total_training_time': 2289.637164592743, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5388393787784032}
2025-08-06 18:45:07,826 - INFO - rml201801a Trial 29 早停训练，使用第2轮准确率: 0.5388
2025-08-06 18:45:07,829 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 18:45:07,829 - INFO - --------------------------------------------------------------------------------
2025-08-06 18:45:07,829 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 18:45:07,829 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 18:45:07,829 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 18:45:07,829 - INFO - --------------------------------------------------------------------------------
2025-08-06 18:45:08,178 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-06 18:45:08,178 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 10 次)
2025-08-06 18:45:08,205 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 18:45:08,206 - INFO -    - dropout: 0.2574793972678259 (数据集特定 + 通用)
2025-08-06 18:45:08,206 - INFO -    - lambda_lifting: 0.14777091408958076
2025-08-06 18:45:08,216 - INFO - 📄 临时配置文件创建: /tmp/tmpmokuuf8x.yaml
2025-08-06 18:45:08,216 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.2574793972678259, 'batch_size': 256}
2025-08-06 18:45:08,217 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.2574793972678259
2025-08-06 18:45:08,217 - INFO - ✅ 最终训练参数: lambda_lifting=0.14777091408958076, batch_size=128
2025-08-06 18:45:08,217 - INFO - rml201801a Trial 30: 参数 {'dropout': 0.2574793972678259, 'lambda_lifting': 0.14777091408958076}
2025-08-06 18:45:08,311 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 18:45:08,311 - INFO - ====================================================================================================
2025-08-06 18:45:08,312 - INFO - 🚀 开始训练 - rml201801a Trial 30
2025-08-06 18:45:08,312 - INFO - ====================================================================================================
2025-08-06 18:45:08,312 - INFO - 📋 本次训练参数:
2025-08-06 18:45:08,312 - INFO -   dropout: 0.2575
  lambda_lifting: 0.1478
2025-08-06 18:45:08,312 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 18:45:08,312 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 18:45:08,312 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 18:45:08,312 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 18:45:08,312 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 18:45:08,312 - INFO - ====================================================================================================
2025-08-06 18:45:08,312 - INFO - 🎯 开始训练...
2025-08-06 18:45:08,312 - INFO - ====================================================================================================
2025-08-06 18:45:08,312 - INFO - 🎯 开始训练 rml201801a Trial 30 (第31次试验)
2025-08-06 18:45:08,312 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 18:45:08,360 - INFO - 开始训练，配置文件: /tmp/tmpmokuuf8x.yaml
2025-08-06 18:45:08,360 - INFO - 数据集类型: rml201801a
2025-08-06 18:45:08,360 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_184508
2025-08-06 18:45:08,371 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_184508/configs/config_backup.yaml
2025-08-06 18:45:08,371 - INFO - 使用设备: cuda
2025-08-06 18:45:08,371 - INFO - 加载数据...
2025-08-06 18:47:17,472 - INFO - 创建模型...
2025-08-06 18:47:17,515 - INFO - 可训练参数数量: 867,608
2025-08-06 18:47:17,515 - INFO - 总参数数量: 867,608
2025-08-06 18:47:17,515 - INFO - 可训练参数比例: 100.00%
2025-08-06 18:47:17,516 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 18:47:17,516 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 31, 'trial_number': 30}
2025-08-06 18:47:17,516 - INFO - 📊 最少训练轮数: 2
2025-08-06 18:47:17,516 - INFO - Epoch 1/400
2025-08-06 19:05:28,936 - INFO - Train Loss: 1.6482, Train Acc: 44.04%
2025-08-06 19:06:27,226 - INFO - Val Loss: 1.4390, Val Acc: 50.35%, Macro-F1: 50.23%, Kappa: 0.4819
2025-08-06 19:06:27,226 - INFO - Epoch 1 训练时间: 1149.71 秒
2025-08-06 19:06:27,307 - INFO - 保存最佳模型，验证准确率: 50.35%, Macro-F1: 50.23%, Kappa: 0.4819
2025-08-06 19:06:27,307 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_184508/models/best_model.pth
2025-08-06 19:06:27,307 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_184508/models/model_epoch_1_acc_50.35.pth
2025-08-06 19:06:27,308 - INFO - Epoch 2/400
2025-08-06 19:24:34,967 - INFO - Train Loss: 1.3919, Train Acc: 53.04%
2025-08-06 19:25:32,100 - INFO - Val Loss: 1.4688, Val Acc: 51.01%, Macro-F1: 51.36%, Kappa: 0.4888
2025-08-06 19:25:32,100 - INFO - Epoch 2 训练时间: 1144.79 秒
2025-08-06 19:25:32,101 - INFO - 🔍 第2轮门槛检查: 准确率 51.0082%, 门槛 57.4476
2025-08-06 19:25:32,101 - INFO - ⚠️ 第2轮准确率 51.0082% 未达到门槛 57.4476，提前结束训练
2025-08-06 19:25:32,101 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_184508/results/training_history.json
2025-08-06 19:25:32,101 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_184508/results/training_summary.json
2025-08-06 19:25:32,101 - INFO - 训练完成！
2025-08-06 19:25:32,101 - INFO - 最佳验证准确率: 50.35%
2025-08-06 19:25:32,101 - INFO - 最佳Macro-F1: 50.23%
2025-08-06 19:25:32,101 - INFO - 最佳Kappa: 0.4819
2025-08-06 19:25:32,101 - INFO - 总训练时间: 2294.50 秒 (38.2 分钟)
2025-08-06 19:25:32,101 - INFO - 平均每轮训练时间: 1147.25 秒
2025-08-06 19:25:32,101 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_184508
2025-08-06 19:25:32,101 - INFO - 🔍 第2轮准确率记录: 0.5101
2025-08-06 19:25:35,144 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 19:25:35,144 - INFO -    - 当前门槛: 57.4476
2025-08-06 19:25:35,144 - INFO -    - 第2轮准确率: 51.0082
2025-08-06 19:25:35,144 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 19:25:35,145 - INFO - 训练完成 rml201801a Trial 30, 结果: {'best_val_acc': 0.5034526787575568, 'best_val_f1': 0.5022581343827551, 'best_val_kappa': 0.48186366479049403, 'total_epochs': 2, 'total_training_time': 2294.5029373168945, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5100818219720659}
2025-08-06 19:25:35,145 - INFO - rml201801a Trial 30 早停训练，使用第2轮准确率: 0.5101
2025-08-06 19:25:35,147 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 19:25:35,148 - INFO - --------------------------------------------------------------------------------
2025-08-06 19:25:35,148 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 19:25:35,148 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 19:25:35,148 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 19:25:35,148 - INFO - --------------------------------------------------------------------------------
2025-08-06 19:25:35,471 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 19:25:35,471 - INFO -    - dropout: 0.11845459404772883 (数据集特定 + 通用)
2025-08-06 19:25:35,471 - INFO -    - lambda_lifting: 0.04261267189830338
2025-08-06 19:25:35,482 - INFO - 📄 临时配置文件创建: /tmp/tmp15wxopjj.yaml
2025-08-06 19:25:35,482 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.11845459404772883, 'batch_size': 256}
2025-08-06 19:25:35,482 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.11845459404772883
2025-08-06 19:25:35,482 - INFO - ✅ 最终训练参数: lambda_lifting=0.04261267189830338, batch_size=128
2025-08-06 19:25:35,482 - INFO - rml201801a Trial 31: 参数 {'dropout': 0.11845459404772883, 'lambda_lifting': 0.04261267189830338}
2025-08-06 19:25:35,594 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 19:25:35,594 - INFO - ====================================================================================================
2025-08-06 19:25:35,595 - INFO - 🚀 开始训练 - rml201801a Trial 31
2025-08-06 19:25:35,595 - INFO - ====================================================================================================
2025-08-06 19:25:35,595 - INFO - 📋 本次训练参数:
2025-08-06 19:25:35,595 - INFO -   dropout: 0.1185
  lambda_lifting: 0.0426
2025-08-06 19:25:35,595 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 19:25:35,595 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 19:25:35,595 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 19:25:35,595 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 19:25:35,595 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 19:25:35,595 - INFO - ====================================================================================================
2025-08-06 19:25:35,595 - INFO - 🎯 开始训练...
2025-08-06 19:25:35,595 - INFO - ====================================================================================================
2025-08-06 19:25:35,595 - INFO - 🎯 开始训练 rml201801a Trial 31 (第32次试验)
2025-08-06 19:25:35,595 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 19:25:35,643 - INFO - 开始训练，配置文件: /tmp/tmp15wxopjj.yaml
2025-08-06 19:25:35,643 - INFO - 数据集类型: rml201801a
2025-08-06 19:25:35,643 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_192535
2025-08-06 19:25:35,653 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_192535/configs/config_backup.yaml
2025-08-06 19:25:35,653 - INFO - 使用设备: cuda
2025-08-06 19:25:35,654 - INFO - 加载数据...
2025-08-06 19:27:43,405 - INFO - 创建模型...
2025-08-06 19:27:43,462 - INFO - 可训练参数数量: 867,608
2025-08-06 19:27:43,463 - INFO - 总参数数量: 867,608
2025-08-06 19:27:43,463 - INFO - 可训练参数比例: 100.00%
2025-08-06 19:27:43,464 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 19:27:43,464 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 32, 'trial_number': 31}
2025-08-06 19:27:43,464 - INFO - 📊 最少训练轮数: 2
2025-08-06 19:27:43,464 - INFO - Epoch 1/400
2025-08-06 19:45:54,502 - INFO - Train Loss: 1.5653, Train Acc: 46.38%
2025-08-06 19:46:52,691 - INFO - Val Loss: 1.3512, Val Acc: 54.01%, Macro-F1: 53.76%, Kappa: 0.5201
2025-08-06 19:46:52,691 - INFO - Epoch 1 训练时间: 1149.23 秒
2025-08-06 19:46:52,753 - INFO - 保存最佳模型，验证准确率: 54.01%, Macro-F1: 53.76%, Kappa: 0.5201
2025-08-06 19:46:52,753 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_192535/models/best_model.pth
2025-08-06 19:46:52,753 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_192535/models/model_epoch_1_acc_54.01.pth
2025-08-06 19:46:52,753 - INFO - Epoch 2/400
2025-08-06 20:05:00,493 - INFO - Train Loss: 1.3166, Train Acc: 55.59%
2025-08-06 20:05:57,644 - INFO - Val Loss: 1.2865, Val Acc: 56.53%, Macro-F1: 58.11%, Kappa: 0.5464
2025-08-06 20:05:57,645 - INFO - Epoch 2 训练时间: 1144.89 秒
2025-08-06 20:05:57,645 - INFO - 🔍 第2轮门槛检查: 准确率 56.5322%, 门槛 57.4476
2025-08-06 20:05:57,645 - INFO - ⚠️ 第2轮准确率 56.5322% 未达到门槛 57.4476，提前结束训练
2025-08-06 20:05:57,645 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_192535/results/training_history.json
2025-08-06 20:05:57,645 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_192535/results/training_summary.json
2025-08-06 20:05:57,646 - INFO - 训练完成！
2025-08-06 20:05:57,646 - INFO - 最佳验证准确率: 54.01%
2025-08-06 20:05:57,646 - INFO - 最佳Macro-F1: 53.76%
2025-08-06 20:05:57,646 - INFO - 最佳Kappa: 0.5201
2025-08-06 20:05:57,646 - INFO - 总训练时间: 2294.12 秒 (38.2 分钟)
2025-08-06 20:05:57,646 - INFO - 平均每轮训练时间: 1147.06 秒
2025-08-06 20:05:57,646 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_192535
2025-08-06 20:05:57,646 - INFO - 🔍 第2轮准确率记录: 0.5653
2025-08-06 20:06:00,687 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 20:06:00,688 - INFO -    - 当前门槛: 57.4476
2025-08-06 20:06:00,688 - INFO -    - 第2轮准确率: 56.5322
2025-08-06 20:06:00,688 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 20:06:00,688 - INFO - 训练完成 rml201801a Trial 31, 结果: {'best_val_acc': 0.5400641025641025, 'best_val_f1': 0.5376226229090078, 'best_val_kappa': 0.520066889632107, 'total_epochs': 2, 'total_training_time': 2294.118821144104, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.565322076297686}
2025-08-06 20:06:00,688 - INFO - rml201801a Trial 31 早停训练，使用第2轮准确率: 0.5653
2025-08-06 20:06:00,691 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 20:06:00,691 - INFO - --------------------------------------------------------------------------------
2025-08-06 20:06:00,691 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 20:06:00,691 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 20:06:00,691 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 20:06:00,691 - INFO - --------------------------------------------------------------------------------
2025-08-06 20:06:01,010 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 20:06:01,010 - INFO -    - dropout: 0.14423171992910572 (数据集特定 + 通用)
2025-08-06 20:06:01,010 - INFO -    - lambda_lifting: 0.13941811039324267
2025-08-06 20:06:01,021 - INFO - 📄 临时配置文件创建: /tmp/tmp87opnstb.yaml
2025-08-06 20:06:01,021 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.14423171992910572, 'batch_size': 256}
2025-08-06 20:06:01,021 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.14423171992910572
2025-08-06 20:06:01,021 - INFO - ✅ 最终训练参数: lambda_lifting=0.13941811039324267, batch_size=128
2025-08-06 20:06:01,021 - INFO - rml201801a Trial 32: 参数 {'dropout': 0.14423171992910572, 'lambda_lifting': 0.13941811039324267}
2025-08-06 20:06:01,130 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 20:06:01,130 - INFO - ====================================================================================================
2025-08-06 20:06:01,130 - INFO - 🚀 开始训练 - rml201801a Trial 32
2025-08-06 20:06:01,130 - INFO - ====================================================================================================
2025-08-06 20:06:01,130 - INFO - 📋 本次训练参数:
2025-08-06 20:06:01,130 - INFO -   dropout: 0.1442
  lambda_lifting: 0.1394
2025-08-06 20:06:01,130 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 20:06:01,130 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 20:06:01,130 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 20:06:01,130 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 20:06:01,130 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 20:06:01,130 - INFO - ====================================================================================================
2025-08-06 20:06:01,131 - INFO - 🎯 开始训练...
2025-08-06 20:06:01,131 - INFO - ====================================================================================================
2025-08-06 20:06:01,131 - INFO - 🎯 开始训练 rml201801a Trial 32 (第33次试验)
2025-08-06 20:06:01,131 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 20:06:01,179 - INFO - 开始训练，配置文件: /tmp/tmp87opnstb.yaml
2025-08-06 20:06:01,179 - INFO - 数据集类型: rml201801a
2025-08-06 20:06:01,179 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_200601
2025-08-06 20:06:01,189 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_200601/configs/config_backup.yaml
2025-08-06 20:06:01,190 - INFO - 使用设备: cuda
2025-08-06 20:06:01,190 - INFO - 加载数据...
2025-08-06 20:08:08,005 - INFO - 创建模型...
2025-08-06 20:08:08,049 - INFO - 可训练参数数量: 867,608
2025-08-06 20:08:08,049 - INFO - 总参数数量: 867,608
2025-08-06 20:08:08,049 - INFO - 可训练参数比例: 100.00%
2025-08-06 20:08:08,051 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 20:08:08,051 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 33, 'trial_number': 32}
2025-08-06 20:08:08,051 - INFO - 📊 最少训练轮数: 2
2025-08-06 20:08:08,051 - INFO - Epoch 1/400
2025-08-06 20:26:19,146 - INFO - Train Loss: 1.6147, Train Acc: 45.29%
2025-08-06 20:27:17,242 - INFO - Val Loss: 1.4602, Val Acc: 49.48%, Macro-F1: 49.92%, Kappa: 0.4729
2025-08-06 20:27:17,242 - INFO - Epoch 1 训练时间: 1149.19 秒
2025-08-06 20:27:17,303 - INFO - 保存最佳模型，验证准确率: 49.48%, Macro-F1: 49.92%, Kappa: 0.4729
2025-08-06 20:27:17,303 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_200601/models/best_model.pth
2025-08-06 20:27:17,303 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_200601/models/model_epoch_1_acc_49.48.pth
2025-08-06 20:27:17,303 - INFO - Epoch 2/400
2025-08-06 20:45:25,042 - INFO - Train Loss: 1.3610, Train Acc: 54.52%
2025-08-06 20:46:22,249 - INFO - Val Loss: 1.3907, Val Acc: 53.30%, Macro-F1: 55.00%, Kappa: 0.5127
2025-08-06 20:46:22,249 - INFO - Epoch 2 训练时间: 1144.95 秒
2025-08-06 20:46:22,249 - INFO - 🔍 第2轮门槛检查: 准确率 53.2961%, 门槛 57.4476
2025-08-06 20:46:22,249 - INFO - ⚠️ 第2轮准确率 53.2961% 未达到门槛 57.4476，提前结束训练
2025-08-06 20:46:22,250 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_200601/results/training_history.json
2025-08-06 20:46:22,250 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_200601/results/training_summary.json
2025-08-06 20:46:22,250 - INFO - 训练完成！
2025-08-06 20:46:22,250 - INFO - 最佳验证准确率: 49.48%
2025-08-06 20:46:22,250 - INFO - 最佳Macro-F1: 49.92%
2025-08-06 20:46:22,250 - INFO - 最佳Kappa: 0.4729
2025-08-06 20:46:22,250 - INFO - 总训练时间: 2294.14 秒 (38.2 分钟)
2025-08-06 20:46:22,250 - INFO - 平均每轮训练时间: 1147.07 秒
2025-08-06 20:46:22,250 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_200601
2025-08-06 20:46:22,250 - INFO - 🔍 第2轮准确率记录: 0.5330
2025-08-06 20:46:25,240 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 20:46:25,240 - INFO -    - 当前门槛: 57.4476
2025-08-06 20:46:25,240 - INFO -    - 第2轮准确率: 53.2961
2025-08-06 20:46:25,240 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 20:46:25,240 - INFO - 训练完成 rml201801a Trial 32, 结果: {'best_val_acc': 0.4948301021471753, 'best_val_f1': 0.4992256699582825, 'best_val_kappa': 0.4728661935448786, 'total_epochs': 2, 'total_training_time': 2294.1373114585876, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.532960704607046}
2025-08-06 20:46:25,240 - INFO - rml201801a Trial 32 早停训练，使用第2轮准确率: 0.5330
2025-08-06 20:46:25,243 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 20:46:25,244 - INFO - --------------------------------------------------------------------------------
2025-08-06 20:46:25,244 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 20:46:25,244 - INFO - #2 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 20:46:25,244 - INFO - #3 | 试验21 | 准确率:0.5667 | dropout=0.10255948279039259, lambda_lifting=0.024320486227991597
2025-08-06 20:46:25,244 - INFO - --------------------------------------------------------------------------------
2025-08-06 20:46:25,575 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 20:46:25,575 - INFO -    - dropout: 0.10449021418159912 (数据集特定 + 通用)
2025-08-06 20:46:25,575 - INFO -    - lambda_lifting: 0.03607260164657145
2025-08-06 20:46:25,586 - INFO - 📄 临时配置文件创建: /tmp/tmp5qvjq88n.yaml
2025-08-06 20:46:25,586 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.10449021418159912, 'batch_size': 256}
2025-08-06 20:46:25,586 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.10449021418159912
2025-08-06 20:46:25,586 - INFO - ✅ 最终训练参数: lambda_lifting=0.03607260164657145, batch_size=128
2025-08-06 20:46:25,586 - INFO - rml201801a Trial 33: 参数 {'dropout': 0.10449021418159912, 'lambda_lifting': 0.03607260164657145}
2025-08-06 20:46:25,691 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 20:46:25,692 - INFO - ====================================================================================================
2025-08-06 20:46:25,692 - INFO - 🚀 开始训练 - rml201801a Trial 33
2025-08-06 20:46:25,692 - INFO - ====================================================================================================
2025-08-06 20:46:25,692 - INFO - 📋 本次训练参数:
2025-08-06 20:46:25,692 - INFO -   dropout: 0.1045
  lambda_lifting: 0.0361
2025-08-06 20:46:25,692 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 20:46:25,692 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 20:46:25,692 - INFO -   #2 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 20:46:25,692 - INFO -   #3 | Trial21 | 准确率:0.5667 | dropout=0.1026, lambda_lifting=0.0243
2025-08-06 20:46:25,692 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 20:46:25,692 - INFO - ====================================================================================================
2025-08-06 20:46:25,692 - INFO - 🎯 开始训练...
2025-08-06 20:46:25,692 - INFO - ====================================================================================================
2025-08-06 20:46:25,692 - INFO - 🎯 开始训练 rml201801a Trial 33 (第34次试验)
2025-08-06 20:46:25,692 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 20:46:25,740 - INFO - 开始训练，配置文件: /tmp/tmp5qvjq88n.yaml
2025-08-06 20:46:25,740 - INFO - 数据集类型: rml201801a
2025-08-06 20:46:25,740 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_204625
2025-08-06 20:46:25,751 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_204625/configs/config_backup.yaml
2025-08-06 20:46:25,751 - INFO - 使用设备: cuda
2025-08-06 20:46:25,751 - INFO - 加载数据...
2025-08-06 20:48:37,372 - INFO - 创建模型...
2025-08-06 20:48:37,400 - INFO - 可训练参数数量: 867,608
2025-08-06 20:48:37,400 - INFO - 总参数数量: 867,608
2025-08-06 20:48:37,400 - INFO - 可训练参数比例: 100.00%
2025-08-06 20:48:37,401 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 20:48:37,401 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 34, 'trial_number': 33}
2025-08-06 20:48:37,401 - INFO - 📊 最少训练轮数: 2
2025-08-06 20:48:37,401 - INFO - Epoch 1/400
2025-08-06 21:06:47,928 - INFO - Train Loss: 1.5429, Train Acc: 47.29%
2025-08-06 21:07:45,957 - INFO - Val Loss: 1.3564, Val Acc: 53.65%, Macro-F1: 54.80%, Kappa: 0.5164
2025-08-06 21:07:45,957 - INFO - Epoch 1 训练时间: 1148.56 秒
2025-08-06 21:07:46,018 - INFO - 保存最佳模型，验证准确率: 53.65%, Macro-F1: 54.80%, Kappa: 0.5164
2025-08-06 21:07:46,018 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_204625/models/best_model.pth
2025-08-06 21:07:46,018 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_204625/models/model_epoch_1_acc_53.65.pth
2025-08-06 21:07:46,018 - INFO - Epoch 2/400
2025-08-06 21:25:53,684 - INFO - Train Loss: 1.3072, Train Acc: 55.88%
2025-08-06 21:26:50,880 - INFO - Val Loss: 1.2714, Val Acc: 56.96%, Macro-F1: 59.00%, Kappa: 0.5509
2025-08-06 21:26:50,881 - INFO - Epoch 2 训练时间: 1144.86 秒
2025-08-06 21:26:50,881 - INFO - 🔍 第2轮门槛检查: 准确率 56.9575%, 门槛 57.4476
2025-08-06 21:26:50,881 - INFO - ⚠️ 第2轮准确率 56.9575% 未达到门槛 57.4476，提前结束训练
2025-08-06 21:26:50,881 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_204625/results/training_history.json
2025-08-06 21:26:50,881 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_204625/results/training_summary.json
2025-08-06 21:26:50,881 - INFO - 训练完成！
2025-08-06 21:26:50,881 - INFO - 最佳验证准确率: 53.65%
2025-08-06 21:26:50,881 - INFO - 最佳Macro-F1: 54.80%
2025-08-06 21:26:50,881 - INFO - 最佳Kappa: 0.5164
2025-08-06 21:26:50,881 - INFO - 总训练时间: 2293.42 秒 (38.2 分钟)
2025-08-06 21:26:50,881 - INFO - 平均每轮训练时间: 1146.71 秒
2025-08-06 21:26:50,881 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_204625
2025-08-06 21:26:50,881 - INFO - 🔍 第2轮准确率记录: 0.5696
2025-08-06 21:26:53,580 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 21:26:53,580 - INFO -    - 当前门槛: 57.4476
2025-08-06 21:26:53,580 - INFO -    - 第2轮准确率: 56.9575
2025-08-06 21:26:53,580 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 21:26:53,580 - INFO - 训练完成 rml201801a Trial 33, 结果: {'best_val_acc': 0.5365045861997081, 'best_val_f1': 0.5480130362996781, 'best_val_kappa': 0.5163526116866519, 'total_epochs': 2, 'total_training_time': 2293.4181411266327, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5695747342088806}
2025-08-06 21:26:53,580 - INFO - rml201801a Trial 33 早停训练，使用第2轮准确率: 0.5696
2025-08-06 21:26:53,583 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 21:26:53,583 - INFO - --------------------------------------------------------------------------------
2025-08-06 21:26:53,584 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 21:26:53,584 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-06 21:26:53,584 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 21:26:53,584 - INFO - --------------------------------------------------------------------------------
2025-08-06 21:26:53,984 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 21:26:53,984 - INFO -    - dropout: 0.16338082959479072 (数据集特定 + 通用)
2025-08-06 21:26:53,985 - INFO -    - lambda_lifting: 0.03299363994159852
2025-08-06 21:26:53,995 - INFO - 📄 临时配置文件创建: /tmp/tmpp68l4m31.yaml
2025-08-06 21:26:53,995 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.16338082959479072, 'batch_size': 256}
2025-08-06 21:26:53,995 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.16338082959479072
2025-08-06 21:26:53,995 - INFO - ✅ 最终训练参数: lambda_lifting=0.03299363994159852, batch_size=128
2025-08-06 21:26:53,995 - INFO - rml201801a Trial 34: 参数 {'dropout': 0.16338082959479072, 'lambda_lifting': 0.03299363994159852}
2025-08-06 21:26:54,107 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 21:26:54,107 - INFO - ====================================================================================================
2025-08-06 21:26:54,107 - INFO - 🚀 开始训练 - rml201801a Trial 34
2025-08-06 21:26:54,107 - INFO - ====================================================================================================
2025-08-06 21:26:54,107 - INFO - 📋 本次训练参数:
2025-08-06 21:26:54,107 - INFO -   dropout: 0.1634
  lambda_lifting: 0.0330
2025-08-06 21:26:54,107 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 21:26:54,107 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 21:26:54,107 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-06 21:26:54,107 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 21:26:54,107 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 21:26:54,107 - INFO - ====================================================================================================
2025-08-06 21:26:54,107 - INFO - 🎯 开始训练...
2025-08-06 21:26:54,107 - INFO - ====================================================================================================
2025-08-06 21:26:54,107 - INFO - 🎯 开始训练 rml201801a Trial 34 (第35次试验)
2025-08-06 21:26:54,107 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 21:26:54,155 - INFO - 开始训练，配置文件: /tmp/tmpp68l4m31.yaml
2025-08-06 21:26:54,155 - INFO - 数据集类型: rml201801a
2025-08-06 21:26:54,155 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_212654
2025-08-06 21:26:54,166 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_212654/configs/config_backup.yaml
2025-08-06 21:26:54,166 - INFO - 使用设备: cuda
2025-08-06 21:26:54,166 - INFO - 加载数据...
2025-08-06 21:29:00,887 - INFO - 创建模型...
2025-08-06 21:29:00,937 - INFO - 可训练参数数量: 867,608
2025-08-06 21:29:00,937 - INFO - 总参数数量: 867,608
2025-08-06 21:29:00,938 - INFO - 可训练参数比例: 100.00%
2025-08-06 21:29:00,939 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 21:29:00,939 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 35, 'trial_number': 34}
2025-08-06 21:29:00,939 - INFO - 📊 最少训练轮数: 2
2025-08-06 21:29:00,939 - INFO - Epoch 1/400
2025-08-06 21:47:11,636 - INFO - Train Loss: 1.5840, Train Acc: 45.46%
2025-08-06 21:48:09,756 - INFO - Val Loss: 1.4214, Val Acc: 50.70%, Macro-F1: 51.10%, Kappa: 0.4856
2025-08-06 21:48:09,756 - INFO - Epoch 1 训练时间: 1148.82 秒
2025-08-06 21:48:09,818 - INFO - 保存最佳模型，验证准确率: 50.70%, Macro-F1: 51.10%, Kappa: 0.4856
2025-08-06 21:48:09,818 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_212654/models/best_model.pth
2025-08-06 21:48:09,818 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_212654/models/model_epoch_1_acc_50.70.pth
2025-08-06 21:48:09,818 - INFO - Epoch 2/400
2025-08-06 22:06:17,359 - INFO - Train Loss: 1.3303, Train Acc: 55.03%
2025-08-06 22:07:14,690 - INFO - Val Loss: 1.3172, Val Acc: 55.54%, Macro-F1: 56.77%, Kappa: 0.5361
2025-08-06 22:07:14,691 - INFO - Epoch 2 训练时间: 1144.87 秒
2025-08-06 22:07:14,691 - INFO - 🔍 第2轮门槛检查: 准确率 55.5402%, 门槛 57.4476
2025-08-06 22:07:14,691 - INFO - ⚠️ 第2轮准确率 55.5402% 未达到门槛 57.4476，提前结束训练
2025-08-06 22:07:14,691 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_212654/results/training_history.json
2025-08-06 22:07:14,691 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_212654/results/training_summary.json
2025-08-06 22:07:14,691 - INFO - 训练完成！
2025-08-06 22:07:14,691 - INFO - 最佳验证准确率: 50.70%
2025-08-06 22:07:14,691 - INFO - 最佳Macro-F1: 51.10%
2025-08-06 22:07:14,691 - INFO - 最佳Kappa: 0.4856
2025-08-06 22:07:14,691 - INFO - 总训练时间: 2293.69 秒 (38.2 分钟)
2025-08-06 22:07:14,691 - INFO - 平均每轮训练时间: 1146.84 秒
2025-08-06 22:07:14,692 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_212654
2025-08-06 22:07:14,692 - INFO - 🔍 第2轮准确率记录: 0.5554
2025-08-06 22:07:17,431 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 22:07:17,431 - INFO -    - 当前门槛: 57.4476
2025-08-06 22:07:17,431 - INFO -    - 第2轮准确率: 55.5402
2025-08-06 22:07:17,431 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 22:07:17,432 - INFO - 训练完成 rml201801a Trial 34, 结果: {'best_val_acc': 0.5069939545549302, 'best_val_f1': 0.5110084917858562, 'best_val_kappa': 0.4855589091007967, 'total_epochs': 2, 'total_training_time': 2293.6898822784424, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5554018136335209}
2025-08-06 22:07:17,432 - INFO - rml201801a Trial 34 早停训练，使用第2轮准确率: 0.5554
2025-08-06 22:07:17,435 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 22:07:17,435 - INFO - --------------------------------------------------------------------------------
2025-08-06 22:07:17,435 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 22:07:17,435 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-06 22:07:17,435 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 22:07:17,435 - INFO - --------------------------------------------------------------------------------
2025-08-06 22:07:17,944 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-06 22:07:17,944 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 15 次)
2025-08-06 22:07:17,972 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 22:07:17,972 - INFO -    - dropout: 0.21117186948749847 (数据集特定 + 通用)
2025-08-06 22:07:17,972 - INFO -    - lambda_lifting: 0.08518217999094388
2025-08-06 22:07:17,983 - INFO - 📄 临时配置文件创建: /tmp/tmpl97e8u1e.yaml
2025-08-06 22:07:17,983 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.21117186948749847, 'batch_size': 256}
2025-08-06 22:07:17,983 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.21117186948749847
2025-08-06 22:07:17,983 - INFO - ✅ 最终训练参数: lambda_lifting=0.08518217999094388, batch_size=128
2025-08-06 22:07:17,983 - INFO - rml201801a Trial 35: 参数 {'dropout': 0.21117186948749847, 'lambda_lifting': 0.08518217999094388}
2025-08-06 22:07:18,088 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 22:07:18,088 - INFO - ====================================================================================================
2025-08-06 22:07:18,088 - INFO - 🚀 开始训练 - rml201801a Trial 35
2025-08-06 22:07:18,088 - INFO - ====================================================================================================
2025-08-06 22:07:18,088 - INFO - 📋 本次训练参数:
2025-08-06 22:07:18,088 - INFO -   dropout: 0.2112
  lambda_lifting: 0.0852
2025-08-06 22:07:18,088 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 22:07:18,088 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 22:07:18,088 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-06 22:07:18,088 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 22:07:18,088 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 22:07:18,088 - INFO - ====================================================================================================
2025-08-06 22:07:18,088 - INFO - 🎯 开始训练...
2025-08-06 22:07:18,088 - INFO - ====================================================================================================
2025-08-06 22:07:18,088 - INFO - 🎯 开始训练 rml201801a Trial 35 (第36次试验)
2025-08-06 22:07:18,088 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 22:07:18,136 - INFO - 开始训练，配置文件: /tmp/tmpl97e8u1e.yaml
2025-08-06 22:07:18,136 - INFO - 数据集类型: rml201801a
2025-08-06 22:07:18,136 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_220718
2025-08-06 22:07:18,147 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_220718/configs/config_backup.yaml
2025-08-06 22:07:18,147 - INFO - 使用设备: cuda
2025-08-06 22:07:18,147 - INFO - 加载数据...
2025-08-06 22:09:29,506 - INFO - 创建模型...
2025-08-06 22:09:29,563 - INFO - 可训练参数数量: 867,608
2025-08-06 22:09:29,564 - INFO - 总参数数量: 867,608
2025-08-06 22:09:29,564 - INFO - 可训练参数比例: 100.00%
2025-08-06 22:09:29,565 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 22:09:29,565 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 36, 'trial_number': 35}
2025-08-06 22:09:29,565 - INFO - 📊 最少训练轮数: 2
2025-08-06 22:09:29,565 - INFO - Epoch 1/400
2025-08-06 22:27:39,979 - INFO - Train Loss: 1.6321, Train Acc: 44.42%
2025-08-06 22:28:37,917 - INFO - Val Loss: 1.4220, Val Acc: 50.69%, Macro-F1: 50.44%, Kappa: 0.4855
2025-08-06 22:28:37,917 - INFO - Epoch 1 训练时间: 1148.35 秒
2025-08-06 22:28:37,979 - INFO - 保存最佳模型，验证准确率: 50.69%, Macro-F1: 50.44%, Kappa: 0.4855
2025-08-06 22:28:37,980 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_220718/models/best_model.pth
2025-08-06 22:28:37,980 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_220718/models/model_epoch_1_acc_50.69.pth
2025-08-06 22:28:37,980 - INFO - Epoch 2/400
2025-08-06 22:46:45,734 - INFO - Train Loss: 1.3788, Train Acc: 53.43%
2025-08-06 22:47:42,836 - INFO - Val Loss: 1.3510, Val Acc: 54.62%, Macro-F1: 56.33%, Kappa: 0.5265
2025-08-06 22:47:42,836 - INFO - Epoch 2 训练时间: 1144.86 秒
2025-08-06 22:47:42,836 - INFO - 🔍 第2轮门槛检查: 准确率 54.6219%, 门槛 57.4476
2025-08-06 22:47:42,836 - INFO - ⚠️ 第2轮准确率 54.6219% 未达到门槛 57.4476，提前结束训练
2025-08-06 22:47:42,837 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_220718/results/training_history.json
2025-08-06 22:47:42,837 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_220718/results/training_summary.json
2025-08-06 22:47:42,837 - INFO - 训练完成！
2025-08-06 22:47:42,837 - INFO - 最佳验证准确率: 50.69%
2025-08-06 22:47:42,837 - INFO - 最佳Macro-F1: 50.44%
2025-08-06 22:47:42,837 - INFO - 最佳Kappa: 0.4855
2025-08-06 22:47:42,837 - INFO - 总训练时间: 2293.21 秒 (38.2 分钟)
2025-08-06 22:47:42,837 - INFO - 平均每轮训练时间: 1146.60 秒
2025-08-06 22:47:42,837 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_220718
2025-08-06 22:47:42,837 - INFO - 🔍 第2轮准确率记录: 0.5462
2025-08-06 22:47:45,347 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 22:47:45,347 - INFO -    - 当前门槛: 57.4476
2025-08-06 22:47:45,348 - INFO -    - 第2轮准确率: 54.6219
2025-08-06 22:47:45,348 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 22:47:45,348 - INFO - 训练完成 rml201801a Trial 35, 结果: {'best_val_acc': 0.5069340212632896, 'best_val_f1': 0.5043566924200564, 'best_val_kappa': 0.48549637001386736, 'total_epochs': 2, 'total_training_time': 2293.208431005478, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5462189910360642}
2025-08-06 22:47:45,348 - INFO - rml201801a Trial 35 早停训练，使用第2轮准确率: 0.5462
2025-08-06 22:47:45,352 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 22:47:45,352 - INFO - --------------------------------------------------------------------------------
2025-08-06 22:47:45,352 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 22:47:45,352 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-06 22:47:45,352 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 22:47:45,352 - INFO - --------------------------------------------------------------------------------
2025-08-06 22:47:45,762 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 22:47:45,763 - INFO -    - dropout: 0.1022720418725496 (数据集特定 + 通用)
2025-08-06 22:47:45,763 - INFO -    - lambda_lifting: 0.4687391996852591
2025-08-06 22:47:45,773 - INFO - 📄 临时配置文件创建: /tmp/tmponnesxhp.yaml
2025-08-06 22:47:45,773 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1022720418725496, 'batch_size': 256}
2025-08-06 22:47:45,773 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1022720418725496
2025-08-06 22:47:45,773 - INFO - ✅ 最终训练参数: lambda_lifting=0.4687391996852591, batch_size=128
2025-08-06 22:47:45,773 - INFO - rml201801a Trial 36: 参数 {'dropout': 0.1022720418725496, 'lambda_lifting': 0.4687391996852591}
2025-08-06 22:47:45,882 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 22:47:45,882 - INFO - ====================================================================================================
2025-08-06 22:47:45,882 - INFO - 🚀 开始训练 - rml201801a Trial 36
2025-08-06 22:47:45,882 - INFO - ====================================================================================================
2025-08-06 22:47:45,882 - INFO - 📋 本次训练参数:
2025-08-06 22:47:45,882 - INFO -   dropout: 0.1023
  lambda_lifting: 0.4687
2025-08-06 22:47:45,882 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 22:47:45,882 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 22:47:45,882 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-06 22:47:45,882 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 22:47:45,882 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 22:47:45,882 - INFO - ====================================================================================================
2025-08-06 22:47:45,882 - INFO - 🎯 开始训练...
2025-08-06 22:47:45,882 - INFO - ====================================================================================================
2025-08-06 22:47:45,882 - INFO - 🎯 开始训练 rml201801a Trial 36 (第37次试验)
2025-08-06 22:47:45,882 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 22:47:45,930 - INFO - 开始训练，配置文件: /tmp/tmponnesxhp.yaml
2025-08-06 22:47:45,930 - INFO - 数据集类型: rml201801a
2025-08-06 22:47:45,930 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_224745
2025-08-06 22:47:45,941 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_224745/configs/config_backup.yaml
2025-08-06 22:47:45,941 - INFO - 使用设备: cuda
2025-08-06 22:47:45,941 - INFO - 加载数据...
2025-08-06 22:49:54,597 - INFO - 创建模型...
2025-08-06 22:49:54,655 - INFO - 可训练参数数量: 867,608
2025-08-06 22:49:54,655 - INFO - 总参数数量: 867,608
2025-08-06 22:49:54,655 - INFO - 可训练参数比例: 100.00%
2025-08-06 22:49:54,656 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 22:49:54,656 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 37, 'trial_number': 36}
2025-08-06 22:49:54,656 - INFO - 📊 最少训练轮数: 2
2025-08-06 22:49:54,656 - INFO - Epoch 1/400
2025-08-06 23:08:05,258 - INFO - Train Loss: 1.6700, Train Acc: 44.16%
2025-08-06 23:09:03,429 - INFO - Val Loss: 1.4434, Val Acc: 50.25%, Macro-F1: 50.89%, Kappa: 0.4808
2025-08-06 23:09:03,430 - INFO - Epoch 1 训练时间: 1148.77 秒
2025-08-06 23:09:03,491 - INFO - 保存最佳模型，验证准确率: 50.25%, Macro-F1: 50.89%, Kappa: 0.4808
2025-08-06 23:09:03,491 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_224745/models/best_model.pth
2025-08-06 23:09:03,491 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_224745/models/model_epoch_1_acc_50.25.pth
2025-08-06 23:09:03,491 - INFO - Epoch 2/400
2025-08-06 23:27:11,768 - INFO - Train Loss: 1.3786, Train Acc: 54.03%
2025-08-06 23:28:09,122 - INFO - Val Loss: 1.3644, Val Acc: 53.93%, Macro-F1: 55.76%, Kappa: 0.5193
2025-08-06 23:28:09,122 - INFO - Epoch 2 训练时间: 1145.63 秒
2025-08-06 23:28:09,122 - INFO - 🔍 第2轮门槛检查: 准确率 53.9327%, 门槛 57.4476
2025-08-06 23:28:09,122 - INFO - ⚠️ 第2轮准确率 53.9327% 未达到门槛 57.4476，提前结束训练
2025-08-06 23:28:09,123 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_224745/results/training_history.json
2025-08-06 23:28:09,123 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_224745/results/training_summary.json
2025-08-06 23:28:09,123 - INFO - 训练完成！
2025-08-06 23:28:09,123 - INFO - 最佳验证准确率: 50.25%
2025-08-06 23:28:09,123 - INFO - 最佳Macro-F1: 50.89%
2025-08-06 23:28:09,123 - INFO - 最佳Kappa: 0.4808
2025-08-06 23:28:09,123 - INFO - 总训练时间: 2294.40 秒 (38.2 分钟)
2025-08-06 23:28:09,123 - INFO - 平均每轮训练时间: 1147.20 秒
2025-08-06 23:28:09,123 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_224745
2025-08-06 23:28:09,123 - INFO - 🔍 第2轮准确率记录: 0.5393
2025-08-06 23:28:11,877 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-06 23:28:11,877 - INFO -    - 当前门槛: 57.4476
2025-08-06 23:28:11,877 - INFO -    - 第2轮准确率: 53.9327
2025-08-06 23:28:11,877 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-06 23:28:11,878 - INFO - 训练完成 rml201801a Trial 36, 结果: {'best_val_acc': 0.5024728997289973, 'best_val_f1': 0.5089081285945772, 'best_val_kappa': 0.48084128667373627, 'total_epochs': 2, 'total_training_time': 2294.404551744461, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5393266624973942}
2025-08-06 23:28:11,878 - INFO - rml201801a Trial 36 早停训练，使用第2轮准确率: 0.5393
2025-08-06 23:28:11,881 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-06 23:28:11,881 - INFO - --------------------------------------------------------------------------------
2025-08-06 23:28:11,881 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-06 23:28:11,881 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-06 23:28:11,881 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-06 23:28:11,881 - INFO - --------------------------------------------------------------------------------
2025-08-06 23:28:12,217 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-06 23:28:12,218 - INFO -    - dropout: 0.38096181206222446 (数据集特定 + 通用)
2025-08-06 23:28:12,218 - INFO -    - lambda_lifting: 0.03906209628052036
2025-08-06 23:28:12,231 - INFO - 📄 临时配置文件创建: /tmp/tmp0smoiw7m.yaml
2025-08-06 23:28:12,231 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.38096181206222446, 'batch_size': 256}
2025-08-06 23:28:12,231 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.38096181206222446
2025-08-06 23:28:12,231 - INFO - ✅ 最终训练参数: lambda_lifting=0.03906209628052036, batch_size=128
2025-08-06 23:28:12,231 - INFO - rml201801a Trial 37: 参数 {'dropout': 0.38096181206222446, 'lambda_lifting': 0.03906209628052036}
2025-08-06 23:28:12,350 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-06 23:28:12,351 - INFO - ====================================================================================================
2025-08-06 23:28:12,351 - INFO - 🚀 开始训练 - rml201801a Trial 37
2025-08-06 23:28:12,351 - INFO - ====================================================================================================
2025-08-06 23:28:12,351 - INFO - 📋 本次训练参数:
2025-08-06 23:28:12,351 - INFO -   dropout: 0.3810
  lambda_lifting: 0.0391
2025-08-06 23:28:12,351 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-06 23:28:12,351 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-06 23:28:12,351 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-06 23:28:12,351 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-06 23:28:12,351 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-06 23:28:12,351 - INFO - ====================================================================================================
2025-08-06 23:28:12,351 - INFO - 🎯 开始训练...
2025-08-06 23:28:12,351 - INFO - ====================================================================================================
2025-08-06 23:28:12,351 - INFO - 🎯 开始训练 rml201801a Trial 37 (第38次试验)
2025-08-06 23:28:12,351 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-06 23:28:12,417 - INFO - 开始训练，配置文件: /tmp/tmp0smoiw7m.yaml
2025-08-06 23:28:12,417 - INFO - 数据集类型: rml201801a
2025-08-06 23:28:12,418 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250806_232812
2025-08-06 23:28:12,433 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_232812/configs/config_backup.yaml
2025-08-06 23:28:12,433 - INFO - 使用设备: cuda
2025-08-06 23:28:12,433 - INFO - 加载数据...
2025-08-06 23:30:32,780 - INFO - 创建模型...
2025-08-06 23:30:32,842 - INFO - 可训练参数数量: 867,608
2025-08-06 23:30:32,843 - INFO - 总参数数量: 867,608
2025-08-06 23:30:32,843 - INFO - 可训练参数比例: 100.00%
2025-08-06 23:30:32,844 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-06 23:30:32,844 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 38, 'trial_number': 37}
2025-08-06 23:30:32,844 - INFO - 📊 最少训练轮数: 2
2025-08-06 23:30:32,844 - INFO - Epoch 1/400
2025-08-06 23:48:44,062 - INFO - Train Loss: 1.6382, Train Acc: 44.11%
2025-08-06 23:49:42,304 - INFO - Val Loss: 1.4900, Val Acc: 48.72%, Macro-F1: 49.19%, Kappa: 0.4649
2025-08-06 23:49:42,304 - INFO - Epoch 1 训练时间: 1149.46 秒
2025-08-06 23:49:42,366 - INFO - 保存最佳模型，验证准确率: 48.72%, Macro-F1: 49.19%, Kappa: 0.4649
2025-08-06 23:49:42,366 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_232812/models/best_model.pth
2025-08-06 23:49:42,366 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_232812/models/model_epoch_1_acc_48.72.pth
2025-08-06 23:49:42,366 - INFO - Epoch 2/400
2025-08-07 00:07:49,875 - INFO - Train Loss: 1.4035, Train Acc: 52.45%
2025-08-07 00:08:47,159 - INFO - Val Loss: 1.3698, Val Acc: 53.84%, Macro-F1: 55.09%, Kappa: 0.5183
2025-08-07 00:08:47,159 - INFO - Epoch 2 训练时间: 1144.79 秒
2025-08-07 00:08:47,159 - INFO - 🔍 第2轮门槛检查: 准确率 53.8363%, 门槛 57.4476
2025-08-07 00:08:47,159 - INFO - ⚠️ 第2轮准确率 53.8363% 未达到门槛 57.4476，提前结束训练
2025-08-07 00:08:47,160 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_232812/results/training_history.json
2025-08-07 00:08:47,160 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250806_232812/results/training_summary.json
2025-08-07 00:08:47,160 - INFO - 训练完成！
2025-08-07 00:08:47,160 - INFO - 最佳验证准确率: 48.72%
2025-08-07 00:08:47,160 - INFO - 最佳Macro-F1: 49.19%
2025-08-07 00:08:47,160 - INFO - 最佳Kappa: 0.4649
2025-08-07 00:08:47,160 - INFO - 总训练时间: 2294.25 秒 (38.2 分钟)
2025-08-07 00:08:47,160 - INFO - 平均每轮训练时间: 1147.13 秒
2025-08-07 00:08:47,160 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250806_232812
2025-08-07 00:08:47,160 - INFO - 🔍 第2轮准确率记录: 0.5384
2025-08-07 00:08:50,091 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 00:08:50,091 - INFO -    - 当前门槛: 57.4476
2025-08-07 00:08:50,091 - INFO -    - 第2轮准确率: 53.8363
2025-08-07 00:08:50,092 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 00:08:50,092 - INFO - 训练完成 rml201801a Trial 37, 结果: {'best_val_acc': 0.48721075672295183, 'best_val_f1': 0.49190957151530074, 'best_val_kappa': 0.46491557223264546, 'total_epochs': 2, 'total_training_time': 2294.253432035446, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.538362518240567}
2025-08-07 00:08:50,092 - INFO - rml201801a Trial 37 早停训练，使用第2轮准确率: 0.5384
2025-08-07 00:08:50,095 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 00:08:50,095 - INFO - --------------------------------------------------------------------------------
2025-08-07 00:08:50,095 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 00:08:50,095 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 00:08:50,095 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 00:08:50,095 - INFO - --------------------------------------------------------------------------------
2025-08-07 00:08:50,587 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 00:08:50,588 - INFO -    - dropout: 0.1719078396497284 (数据集特定 + 通用)
2025-08-07 00:08:50,588 - INFO -    - lambda_lifting: 0.10123719011661961
2025-08-07 00:08:50,598 - INFO - 📄 临时配置文件创建: /tmp/tmpsqy8usdn.yaml
2025-08-07 00:08:50,598 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1719078396497284, 'batch_size': 256}
2025-08-07 00:08:50,598 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1719078396497284
2025-08-07 00:08:50,598 - INFO - ✅ 最终训练参数: lambda_lifting=0.10123719011661961, batch_size=128
2025-08-07 00:08:50,599 - INFO - rml201801a Trial 38: 参数 {'dropout': 0.1719078396497284, 'lambda_lifting': 0.10123719011661961}
2025-08-07 00:08:50,707 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 00:08:50,707 - INFO - ====================================================================================================
2025-08-07 00:08:50,707 - INFO - 🚀 开始训练 - rml201801a Trial 38
2025-08-07 00:08:50,707 - INFO - ====================================================================================================
2025-08-07 00:08:50,707 - INFO - 📋 本次训练参数:
2025-08-07 00:08:50,707 - INFO -   dropout: 0.1719
  lambda_lifting: 0.1012
2025-08-07 00:08:50,707 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 00:08:50,707 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 00:08:50,707 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 00:08:50,707 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 00:08:50,707 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 00:08:50,708 - INFO - ====================================================================================================
2025-08-07 00:08:50,708 - INFO - 🎯 开始训练...
2025-08-07 00:08:50,708 - INFO - ====================================================================================================
2025-08-07 00:08:50,708 - INFO - 🎯 开始训练 rml201801a Trial 38 (第39次试验)
2025-08-07 00:08:50,708 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 00:08:50,755 - INFO - 开始训练，配置文件: /tmp/tmpsqy8usdn.yaml
2025-08-07 00:08:50,755 - INFO - 数据集类型: rml201801a
2025-08-07 00:08:50,755 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_000850
2025-08-07 00:08:50,766 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_000850/configs/config_backup.yaml
2025-08-07 00:08:50,766 - INFO - 使用设备: cuda
2025-08-07 00:08:50,766 - INFO - 加载数据...
2025-08-07 00:11:03,692 - INFO - 创建模型...
2025-08-07 00:11:03,753 - INFO - 可训练参数数量: 867,608
2025-08-07 00:11:03,754 - INFO - 总参数数量: 867,608
2025-08-07 00:11:03,754 - INFO - 可训练参数比例: 100.00%
2025-08-07 00:11:03,755 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 00:11:03,755 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 39, 'trial_number': 38}
2025-08-07 00:11:03,755 - INFO - 📊 最少训练轮数: 2
2025-08-07 00:11:03,755 - INFO - Epoch 1/400
2025-08-07 00:29:14,594 - INFO - Train Loss: 1.6196, Train Acc: 45.01%
2025-08-07 00:30:12,641 - INFO - Val Loss: 1.4160, Val Acc: 52.06%, Macro-F1: 53.68%, Kappa: 0.4997
2025-08-07 00:30:12,641 - INFO - Epoch 1 训练时间: 1148.89 秒
2025-08-07 00:30:12,702 - INFO - 保存最佳模型，验证准确率: 52.06%, Macro-F1: 53.68%, Kappa: 0.4997
2025-08-07 00:30:12,702 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_000850/models/best_model.pth
2025-08-07 00:30:12,702 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_000850/models/model_epoch_1_acc_52.06.pth
2025-08-07 00:30:12,702 - INFO - Epoch 2/400
2025-08-07 00:48:20,698 - INFO - Train Loss: 1.3623, Train Acc: 54.54%
2025-08-07 00:49:17,867 - INFO - Val Loss: 1.3111, Val Acc: 56.19%, Macro-F1: 56.80%, Kappa: 0.5429
2025-08-07 00:49:17,867 - INFO - Epoch 2 训练时间: 1145.17 秒
2025-08-07 00:49:17,867 - INFO - 🔍 第2轮门槛检查: 准确率 56.1942%, 门槛 57.4476
2025-08-07 00:49:17,867 - INFO - ⚠️ 第2轮准确率 56.1942% 未达到门槛 57.4476，提前结束训练
2025-08-07 00:49:17,868 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_000850/results/training_history.json
2025-08-07 00:49:17,868 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_000850/results/training_summary.json
2025-08-07 00:49:17,868 - INFO - 训练完成！
2025-08-07 00:49:17,868 - INFO - 最佳验证准确率: 52.06%
2025-08-07 00:49:17,868 - INFO - 最佳Macro-F1: 53.68%
2025-08-07 00:49:17,868 - INFO - 最佳Kappa: 0.4997
2025-08-07 00:49:17,868 - INFO - 总训练时间: 2294.05 秒 (38.2 分钟)
2025-08-07 00:49:17,868 - INFO - 平均每轮训练时间: 1147.03 秒
2025-08-07 00:49:17,868 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_000850
2025-08-07 00:49:17,868 - INFO - 🔍 第2轮准确率记录: 0.5619
2025-08-07 00:49:20,658 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 00:49:20,658 - INFO -    - 当前门槛: 57.4476
2025-08-07 00:49:20,658 - INFO -    - 第2轮准确率: 56.1942
2025-08-07 00:49:20,659 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 00:49:20,659 - INFO - 训练完成 rml201801a Trial 38, 结果: {'best_val_acc': 0.5205545132374401, 'best_val_f1': 0.5368329994200205, 'best_val_kappa': 0.4997090572912418, 'total_epochs': 2, 'total_training_time': 2294.051414489746, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5619423598082135}
2025-08-07 00:49:20,659 - INFO - rml201801a Trial 38 早停训练，使用第2轮准确率: 0.5619
2025-08-07 00:49:20,662 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 00:49:20,662 - INFO - --------------------------------------------------------------------------------
2025-08-07 00:49:20,662 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 00:49:20,662 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 00:49:20,662 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 00:49:20,662 - INFO - --------------------------------------------------------------------------------
2025-08-07 00:49:20,991 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 00:49:20,991 - INFO -    - dropout: 0.14474624850597775 (数据集特定 + 通用)
2025-08-07 00:49:20,991 - INFO -    - lambda_lifting: 0.18777392496901543
2025-08-07 00:49:21,002 - INFO - 📄 临时配置文件创建: /tmp/tmpx8r5je2e.yaml
2025-08-07 00:49:21,002 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.14474624850597775, 'batch_size': 256}
2025-08-07 00:49:21,002 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.14474624850597775
2025-08-07 00:49:21,002 - INFO - ✅ 最终训练参数: lambda_lifting=0.18777392496901543, batch_size=128
2025-08-07 00:49:21,002 - INFO - rml201801a Trial 39: 参数 {'dropout': 0.14474624850597775, 'lambda_lifting': 0.18777392496901543}
2025-08-07 00:49:21,098 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 00:49:21,098 - INFO - ====================================================================================================
2025-08-07 00:49:21,098 - INFO - 🚀 开始训练 - rml201801a Trial 39
2025-08-07 00:49:21,098 - INFO - ====================================================================================================
2025-08-07 00:49:21,098 - INFO - 📋 本次训练参数:
2025-08-07 00:49:21,098 - INFO -   dropout: 0.1447
  lambda_lifting: 0.1878
2025-08-07 00:49:21,098 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 00:49:21,098 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 00:49:21,098 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 00:49:21,098 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 00:49:21,098 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 00:49:21,098 - INFO - ====================================================================================================
2025-08-07 00:49:21,098 - INFO - 🎯 开始训练...
2025-08-07 00:49:21,099 - INFO - ====================================================================================================
2025-08-07 00:49:21,099 - INFO - 🎯 开始训练 rml201801a Trial 39 (第40次试验)
2025-08-07 00:49:21,099 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 00:49:21,146 - INFO - 开始训练，配置文件: /tmp/tmpx8r5je2e.yaml
2025-08-07 00:49:21,146 - INFO - 数据集类型: rml201801a
2025-08-07 00:49:21,146 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_004921
2025-08-07 00:49:21,157 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_004921/configs/config_backup.yaml
2025-08-07 00:49:21,157 - INFO - 使用设备: cuda
2025-08-07 00:49:21,157 - INFO - 加载数据...
2025-08-07 00:51:26,786 - INFO - 创建模型...
2025-08-07 00:51:26,832 - INFO - 可训练参数数量: 867,608
2025-08-07 00:51:26,832 - INFO - 总参数数量: 867,608
2025-08-07 00:51:26,832 - INFO - 可训练参数比例: 100.00%
2025-08-07 00:51:26,833 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 00:51:26,833 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 40, 'trial_number': 39}
2025-08-07 00:51:26,833 - INFO - 📊 最少训练轮数: 2
2025-08-07 00:51:26,833 - INFO - Epoch 1/400
2025-08-07 01:09:37,448 - INFO - Train Loss: 1.6273, Train Acc: 45.05%
2025-08-07 01:10:35,483 - INFO - Val Loss: 1.4180, Val Acc: 51.05%, Macro-F1: 51.28%, Kappa: 0.4892
2025-08-07 01:10:35,483 - INFO - Epoch 1 训练时间: 1148.65 秒
2025-08-07 01:10:35,545 - INFO - 保存最佳模型，验证准确率: 51.05%, Macro-F1: 51.28%, Kappa: 0.4892
2025-08-07 01:10:35,545 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_004921/models/best_model.pth
2025-08-07 01:10:35,545 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_004921/models/model_epoch_1_acc_51.05.pth
2025-08-07 01:10:35,545 - INFO - Epoch 2/400
2025-08-07 01:28:43,774 - INFO - Train Loss: 1.3698, Train Acc: 54.31%
2025-08-07 01:29:40,895 - INFO - Val Loss: 1.3793, Val Acc: 53.64%, Macro-F1: 55.78%, Kappa: 0.5162
2025-08-07 01:29:40,895 - INFO - Epoch 2 训练时间: 1145.35 秒
2025-08-07 01:29:40,895 - INFO - 🔍 第2轮门槛检查: 准确率 53.6380%, 门槛 57.4476
2025-08-07 01:29:40,895 - INFO - ⚠️ 第2轮准确率 53.6380% 未达到门槛 57.4476，提前结束训练
2025-08-07 01:29:40,896 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_004921/results/training_history.json
2025-08-07 01:29:40,896 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_004921/results/training_summary.json
2025-08-07 01:29:40,896 - INFO - 训练完成！
2025-08-07 01:29:40,896 - INFO - 最佳验证准确率: 51.05%
2025-08-07 01:29:40,896 - INFO - 最佳Macro-F1: 51.28%
2025-08-07 01:29:40,896 - INFO - 最佳Kappa: 0.4892
2025-08-07 01:29:40,896 - INFO - 总训练时间: 2294.00 秒 (38.2 分钟)
2025-08-07 01:29:40,896 - INFO - 平均每轮训练时间: 1147.00 秒
2025-08-07 01:29:40,896 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_004921
2025-08-07 01:29:40,896 - INFO - 🔍 第2轮准确率记录: 0.5364
2025-08-07 01:29:43,531 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 01:29:43,532 - INFO -    - 当前门槛: 57.4476
2025-08-07 01:29:43,532 - INFO -    - 第2轮准确率: 53.6380
2025-08-07 01:29:43,532 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 01:29:43,532 - INFO - 训练完成 rml201801a Trial 39, 结果: {'best_val_acc': 0.5105039608088389, 'best_val_f1': 0.5128466237776682, 'best_val_kappa': 0.4892215243222666, 'total_epochs': 2, 'total_training_time': 2293.9995579719543, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5363795080258494}
2025-08-07 01:29:43,532 - INFO - rml201801a Trial 39 早停训练，使用第2轮准确率: 0.5364
2025-08-07 01:29:43,535 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 01:29:43,536 - INFO - --------------------------------------------------------------------------------
2025-08-07 01:29:43,536 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 01:29:43,536 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 01:29:43,536 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 01:29:43,536 - INFO - --------------------------------------------------------------------------------
2025-08-07 01:29:43,827 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 01:29:43,827 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 20 次)
2025-08-07 01:29:43,855 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 01:29:43,855 - INFO -    - dropout: 0.25389021178032817 (数据集特定 + 通用)
2025-08-07 01:29:43,855 - INFO -    - lambda_lifting: 0.32863227212284596
2025-08-07 01:29:43,866 - INFO - 📄 临时配置文件创建: /tmp/tmphwoz976o.yaml
2025-08-07 01:29:43,866 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.25389021178032817, 'batch_size': 256}
2025-08-07 01:29:43,866 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.25389021178032817
2025-08-07 01:29:43,866 - INFO - ✅ 最终训练参数: lambda_lifting=0.32863227212284596, batch_size=128
2025-08-07 01:29:43,866 - INFO - rml201801a Trial 40: 参数 {'dropout': 0.25389021178032817, 'lambda_lifting': 0.32863227212284596}
2025-08-07 01:29:43,978 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 01:29:43,978 - INFO - ====================================================================================================
2025-08-07 01:29:43,979 - INFO - 🚀 开始训练 - rml201801a Trial 40
2025-08-07 01:29:43,979 - INFO - ====================================================================================================
2025-08-07 01:29:43,979 - INFO - 📋 本次训练参数:
2025-08-07 01:29:43,979 - INFO -   dropout: 0.2539
  lambda_lifting: 0.3286
2025-08-07 01:29:43,979 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 01:29:43,979 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 01:29:43,979 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 01:29:43,979 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 01:29:43,979 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 01:29:43,979 - INFO - ====================================================================================================
2025-08-07 01:29:43,979 - INFO - 🎯 开始训练...
2025-08-07 01:29:43,979 - INFO - ====================================================================================================
2025-08-07 01:29:43,979 - INFO - 🎯 开始训练 rml201801a Trial 40 (第41次试验)
2025-08-07 01:29:43,979 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 01:29:44,027 - INFO - 开始训练，配置文件: /tmp/tmphwoz976o.yaml
2025-08-07 01:29:44,027 - INFO - 数据集类型: rml201801a
2025-08-07 01:29:44,027 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_012944
2025-08-07 01:29:44,038 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_012944/configs/config_backup.yaml
2025-08-07 01:29:44,038 - INFO - 使用设备: cuda
2025-08-07 01:29:44,038 - INFO - 加载数据...
2025-08-07 01:31:50,779 - INFO - 创建模型...
2025-08-07 01:31:50,837 - INFO - 可训练参数数量: 867,608
2025-08-07 01:31:50,837 - INFO - 总参数数量: 867,608
2025-08-07 01:31:50,837 - INFO - 可训练参数比例: 100.00%
2025-08-07 01:31:50,838 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 01:31:50,838 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 41, 'trial_number': 40}
2025-08-07 01:31:50,839 - INFO - 📊 最少训练轮数: 2
2025-08-07 01:31:50,839 - INFO - Epoch 1/400
2025-08-07 01:50:01,430 - INFO - Train Loss: 1.6832, Train Acc: 43.32%
2025-08-07 01:50:59,525 - INFO - Val Loss: 1.4384, Val Acc: 50.53%, Macro-F1: 51.08%, Kappa: 0.4838
2025-08-07 01:50:59,526 - INFO - Epoch 1 训练时间: 1148.69 秒
2025-08-07 01:50:59,623 - INFO - 保存最佳模型，验证准确率: 50.53%, Macro-F1: 51.08%, Kappa: 0.4838
2025-08-07 01:50:59,624 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_012944/models/best_model.pth
2025-08-07 01:50:59,624 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_012944/models/model_epoch_1_acc_50.53.pth
2025-08-07 01:50:59,624 - INFO - Epoch 2/400
2025-08-07 02:09:07,635 - INFO - Train Loss: 1.4091, Train Acc: 52.57%
2025-08-07 02:10:04,852 - INFO - Val Loss: 1.4196, Val Acc: 51.62%, Macro-F1: 53.56%, Kappa: 0.4952
2025-08-07 02:10:04,853 - INFO - Epoch 2 训练时间: 1145.23 秒
2025-08-07 02:10:04,853 - INFO - 🔍 第2轮门槛检查: 准确率 51.6187%, 门槛 57.4476
2025-08-07 02:10:04,853 - INFO - ⚠️ 第2轮准确率 51.6187% 未达到门槛 57.4476，提前结束训练
2025-08-07 02:10:04,853 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_012944/results/training_history.json
2025-08-07 02:10:04,853 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_012944/results/training_summary.json
2025-08-07 02:10:04,853 - INFO - 训练完成！
2025-08-07 02:10:04,853 - INFO - 最佳验证准确率: 50.53%
2025-08-07 02:10:04,853 - INFO - 最佳Macro-F1: 51.08%
2025-08-07 02:10:04,853 - INFO - 最佳Kappa: 0.4838
2025-08-07 02:10:04,853 - INFO - 总训练时间: 2293.92 秒 (38.2 分钟)
2025-08-07 02:10:04,854 - INFO - 平均每轮训练时间: 1146.96 秒
2025-08-07 02:10:04,854 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_012944
2025-08-07 02:10:04,854 - INFO - 🔍 第2轮准确率记录: 0.5162
2025-08-07 02:10:07,508 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 02:10:07,509 - INFO -    - 当前门槛: 57.4476
2025-08-07 02:10:07,509 - INFO -    - 第2轮准确率: 51.6187
2025-08-07 02:10:07,509 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 02:10:07,509 - INFO - 训练完成 rml201801a Trial 40, 结果: {'best_val_acc': 0.5053314571607255, 'best_val_f1': 0.5107543550849784, 'best_val_kappa': 0.4838241292111918, 'total_epochs': 2, 'total_training_time': 2293.9159622192383, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5161872003335418}
2025-08-07 02:10:07,509 - INFO - rml201801a Trial 40 早停训练，使用第2轮准确率: 0.5162
2025-08-07 02:10:07,513 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 02:10:07,513 - INFO - --------------------------------------------------------------------------------
2025-08-07 02:10:07,513 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 02:10:07,513 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 02:10:07,513 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 02:10:07,513 - INFO - --------------------------------------------------------------------------------
2025-08-07 02:10:07,849 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 02:10:07,849 - INFO -    - dropout: 0.10191609533451637 (数据集特定 + 通用)
2025-08-07 02:10:07,849 - INFO -    - lambda_lifting: 0.015050946563206397
2025-08-07 02:10:07,860 - INFO - 📄 临时配置文件创建: /tmp/tmpb6kke_hw.yaml
2025-08-07 02:10:07,860 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.10191609533451637, 'batch_size': 256}
2025-08-07 02:10:07,860 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.10191609533451637
2025-08-07 02:10:07,860 - INFO - ✅ 最终训练参数: lambda_lifting=0.015050946563206397, batch_size=128
2025-08-07 02:10:07,860 - INFO - rml201801a Trial 41: 参数 {'dropout': 0.10191609533451637, 'lambda_lifting': 0.015050946563206397}
2025-08-07 02:10:07,977 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 02:10:07,977 - INFO - ====================================================================================================
2025-08-07 02:10:07,977 - INFO - 🚀 开始训练 - rml201801a Trial 41
2025-08-07 02:10:07,977 - INFO - ====================================================================================================
2025-08-07 02:10:07,977 - INFO - 📋 本次训练参数:
2025-08-07 02:10:07,977 - INFO -   dropout: 0.1019
  lambda_lifting: 0.0151
2025-08-07 02:10:07,977 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 02:10:07,977 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 02:10:07,977 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 02:10:07,977 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 02:10:07,977 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 02:10:07,977 - INFO - ====================================================================================================
2025-08-07 02:10:07,977 - INFO - 🎯 开始训练...
2025-08-07 02:10:07,977 - INFO - ====================================================================================================
2025-08-07 02:10:07,977 - INFO - 🎯 开始训练 rml201801a Trial 41 (第42次试验)
2025-08-07 02:10:07,977 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 02:10:08,025 - INFO - 开始训练，配置文件: /tmp/tmpb6kke_hw.yaml
2025-08-07 02:10:08,025 - INFO - 数据集类型: rml201801a
2025-08-07 02:10:08,025 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_021008
2025-08-07 02:10:08,036 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_021008/configs/config_backup.yaml
2025-08-07 02:10:08,036 - INFO - 使用设备: cuda
2025-08-07 02:10:08,036 - INFO - 加载数据...
2025-08-07 02:12:19,278 - INFO - 创建模型...
2025-08-07 02:12:19,336 - INFO - 可训练参数数量: 867,608
2025-08-07 02:12:19,337 - INFO - 总参数数量: 867,608
2025-08-07 02:12:19,337 - INFO - 可训练参数比例: 100.00%
2025-08-07 02:12:19,338 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 02:12:19,338 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 42, 'trial_number': 41}
2025-08-07 02:12:19,338 - INFO - 📊 最少训练轮数: 2
2025-08-07 02:12:19,338 - INFO - Epoch 1/400
2025-08-07 02:30:29,787 - INFO - Train Loss: 1.5338, Train Acc: 47.33%
2025-08-07 02:31:27,810 - INFO - Val Loss: 1.3585, Val Acc: 53.76%, Macro-F1: 54.43%, Kappa: 0.5175
2025-08-07 02:31:27,810 - INFO - Epoch 1 训练时间: 1148.47 秒
2025-08-07 02:31:27,872 - INFO - 保存最佳模型，验证准确率: 53.76%, Macro-F1: 54.43%, Kappa: 0.5175
2025-08-07 02:31:27,872 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_021008/models/best_model.pth
2025-08-07 02:31:27,872 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_021008/models/model_epoch_1_acc_53.76.pth
2025-08-07 02:31:27,872 - INFO - Epoch 2/400
2025-08-07 02:49:35,195 - INFO - Train Loss: 1.2943, Train Acc: 56.28%
2025-08-07 02:50:32,473 - INFO - Val Loss: 1.2984, Val Acc: 56.09%, Macro-F1: 57.61%, Kappa: 0.5418
2025-08-07 02:50:32,473 - INFO - Epoch 2 训练时间: 1144.60 秒
2025-08-07 02:50:32,474 - INFO - 🔍 第2轮门槛检查: 准确率 56.0871%, 门槛 57.4476
2025-08-07 02:50:32,474 - INFO - ⚠️ 第2轮准确率 56.0871% 未达到门槛 57.4476，提前结束训练
2025-08-07 02:50:32,474 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_021008/results/training_history.json
2025-08-07 02:50:32,474 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_021008/results/training_summary.json
2025-08-07 02:50:32,474 - INFO - 训练完成！
2025-08-07 02:50:32,474 - INFO - 最佳验证准确率: 53.76%
2025-08-07 02:50:32,474 - INFO - 最佳Macro-F1: 54.43%
2025-08-07 02:50:32,474 - INFO - 最佳Kappa: 0.5175
2025-08-07 02:50:32,474 - INFO - 总训练时间: 2293.07 秒 (38.2 分钟)
2025-08-07 02:50:32,474 - INFO - 平均每轮训练时间: 1146.54 秒
2025-08-07 02:50:32,474 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_021008
2025-08-07 02:50:32,474 - INFO - 🔍 第2轮准确率记录: 0.5609
2025-08-07 02:50:35,216 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 02:50:35,216 - INFO -    - 当前门槛: 57.4476
2025-08-07 02:50:35,216 - INFO -    - 第2轮准确率: 56.0871
2025-08-07 02:50:35,216 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 02:50:35,216 - INFO - 训练完成 rml201801a Trial 41, 结果: {'best_val_acc': 0.5375990202209714, 'best_val_f1': 0.5442781144460104, 'best_val_kappa': 0.5174946297957963, 'total_epochs': 2, 'total_training_time': 2293.073368549347, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5608713779445487}
2025-08-07 02:50:35,216 - INFO - rml201801a Trial 41 早停训练，使用第2轮准确率: 0.5609
2025-08-07 02:50:35,220 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 02:50:35,220 - INFO - --------------------------------------------------------------------------------
2025-08-07 02:50:35,220 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 02:50:35,220 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 02:50:35,220 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 02:50:35,220 - INFO - --------------------------------------------------------------------------------
2025-08-07 02:50:35,556 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 02:50:35,556 - INFO -    - dropout: 0.10016052716670265 (数据集特定 + 通用)
2025-08-07 02:50:35,556 - INFO -    - lambda_lifting: 0.1291881721851967
2025-08-07 02:50:35,567 - INFO - 📄 临时配置文件创建: /tmp/tmphispydkw.yaml
2025-08-07 02:50:35,567 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.10016052716670265, 'batch_size': 256}
2025-08-07 02:50:35,567 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.10016052716670265
2025-08-07 02:50:35,567 - INFO - ✅ 最终训练参数: lambda_lifting=0.1291881721851967, batch_size=128
2025-08-07 02:50:35,567 - INFO - rml201801a Trial 42: 参数 {'dropout': 0.10016052716670265, 'lambda_lifting': 0.1291881721851967}
2025-08-07 02:50:35,681 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 02:50:35,681 - INFO - ====================================================================================================
2025-08-07 02:50:35,681 - INFO - 🚀 开始训练 - rml201801a Trial 42
2025-08-07 02:50:35,681 - INFO - ====================================================================================================
2025-08-07 02:50:35,681 - INFO - 📋 本次训练参数:
2025-08-07 02:50:35,681 - INFO -   dropout: 0.1002
  lambda_lifting: 0.1292
2025-08-07 02:50:35,681 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 02:50:35,681 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 02:50:35,681 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 02:50:35,681 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 02:50:35,681 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 02:50:35,681 - INFO - ====================================================================================================
2025-08-07 02:50:35,681 - INFO - 🎯 开始训练...
2025-08-07 02:50:35,681 - INFO - ====================================================================================================
2025-08-07 02:50:35,681 - INFO - 🎯 开始训练 rml201801a Trial 42 (第43次试验)
2025-08-07 02:50:35,681 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 02:50:35,729 - INFO - 开始训练，配置文件: /tmp/tmphispydkw.yaml
2025-08-07 02:50:35,729 - INFO - 数据集类型: rml201801a
2025-08-07 02:50:35,729 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_025035
2025-08-07 02:50:35,740 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_025035/configs/config_backup.yaml
2025-08-07 02:50:35,740 - INFO - 使用设备: cuda
2025-08-07 02:50:35,740 - INFO - 加载数据...
2025-08-07 02:52:44,293 - INFO - 创建模型...
2025-08-07 02:52:44,336 - INFO - 可训练参数数量: 867,608
2025-08-07 02:52:44,336 - INFO - 总参数数量: 867,608
2025-08-07 02:52:44,336 - INFO - 可训练参数比例: 100.00%
2025-08-07 02:52:44,338 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 02:52:44,338 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 43, 'trial_number': 42}
2025-08-07 02:52:44,338 - INFO - 📊 最少训练轮数: 2
2025-08-07 02:52:44,338 - INFO - Epoch 1/400
2025-08-07 03:10:55,291 - INFO - Train Loss: 1.5927, Train Acc: 46.08%
2025-08-07 03:11:53,677 - INFO - Val Loss: 1.4089, Val Acc: 51.61%, Macro-F1: 51.39%, Kappa: 0.4951
2025-08-07 03:11:53,677 - INFO - Epoch 1 训练时间: 1149.34 秒
2025-08-07 03:11:53,770 - INFO - 保存最佳模型，验证准确率: 51.61%, Macro-F1: 51.39%, Kappa: 0.4951
2025-08-07 03:11:53,770 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_025035/models/best_model.pth
2025-08-07 03:11:53,770 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_025035/models/model_epoch_1_acc_51.61.pth
2025-08-07 03:11:53,770 - INFO - Epoch 2/400
2025-08-07 03:30:02,891 - INFO - Train Loss: 1.3408, Train Acc: 55.40%
2025-08-07 03:31:00,507 - INFO - Val Loss: 1.3143, Val Acc: 56.40%, Macro-F1: 58.42%, Kappa: 0.5451
2025-08-07 03:31:00,507 - INFO - Epoch 2 训练时间: 1146.74 秒
2025-08-07 03:31:00,507 - INFO - 🔍 第2轮门槛检查: 准确率 56.4037%, 门槛 57.4476
2025-08-07 03:31:00,507 - INFO - ⚠️ 第2轮准确率 56.4037% 未达到门槛 57.4476，提前结束训练
2025-08-07 03:31:00,508 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_025035/results/training_history.json
2025-08-07 03:31:00,508 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_025035/results/training_summary.json
2025-08-07 03:31:00,508 - INFO - 训练完成！
2025-08-07 03:31:00,508 - INFO - 最佳验证准确率: 51.61%
2025-08-07 03:31:00,508 - INFO - 最佳Macro-F1: 51.39%
2025-08-07 03:31:00,508 - INFO - 最佳Kappa: 0.4951
2025-08-07 03:31:00,508 - INFO - 总训练时间: 2296.08 秒 (38.3 分钟)
2025-08-07 03:31:00,508 - INFO - 平均每轮训练时间: 1148.04 秒
2025-08-07 03:31:00,508 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_025035
2025-08-07 03:31:00,508 - INFO - 🔍 第2轮准确率记录: 0.5640
2025-08-07 03:31:02,990 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 03:31:02,991 - INFO -    - 当前门槛: 57.4476
2025-08-07 03:31:02,991 - INFO -    - 第2轮准确率: 56.4037
2025-08-07 03:31:02,991 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 03:31:02,991 - INFO - 训练完成 rml201801a Trial 42, 结果: {'best_val_acc': 0.5161220554513237, 'best_val_f1': 0.513877980311116, 'best_val_kappa': 0.49508388394920744, 'total_epochs': 2, 'total_training_time': 2296.075948238373, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.564037419220346}
2025-08-07 03:31:02,991 - INFO - rml201801a Trial 42 早停训练，使用第2轮准确率: 0.5640
2025-08-07 03:31:02,995 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 03:31:02,995 - INFO - --------------------------------------------------------------------------------
2025-08-07 03:31:02,995 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 03:31:02,995 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 03:31:02,995 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 03:31:02,995 - INFO - --------------------------------------------------------------------------------
2025-08-07 03:31:03,319 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 03:31:03,319 - INFO -    - dropout: 0.1711852970963315 (数据集特定 + 通用)
2025-08-07 03:31:03,319 - INFO -    - lambda_lifting: 0.23400500248127906
2025-08-07 03:31:03,330 - INFO - 📄 临时配置文件创建: /tmp/tmphzugcv1v.yaml
2025-08-07 03:31:03,330 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1711852970963315, 'batch_size': 256}
2025-08-07 03:31:03,330 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1711852970963315
2025-08-07 03:31:03,330 - INFO - ✅ 最终训练参数: lambda_lifting=0.23400500248127906, batch_size=128
2025-08-07 03:31:03,330 - INFO - rml201801a Trial 43: 参数 {'dropout': 0.1711852970963315, 'lambda_lifting': 0.23400500248127906}
2025-08-07 03:31:03,447 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 03:31:03,447 - INFO - ====================================================================================================
2025-08-07 03:31:03,447 - INFO - 🚀 开始训练 - rml201801a Trial 43
2025-08-07 03:31:03,447 - INFO - ====================================================================================================
2025-08-07 03:31:03,448 - INFO - 📋 本次训练参数:
2025-08-07 03:31:03,448 - INFO -   dropout: 0.1712
  lambda_lifting: 0.2340
2025-08-07 03:31:03,448 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 03:31:03,448 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 03:31:03,448 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 03:31:03,448 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 03:31:03,448 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 03:31:03,448 - INFO - ====================================================================================================
2025-08-07 03:31:03,448 - INFO - 🎯 开始训练...
2025-08-07 03:31:03,448 - INFO - ====================================================================================================
2025-08-07 03:31:03,448 - INFO - 🎯 开始训练 rml201801a Trial 43 (第44次试验)
2025-08-07 03:31:03,448 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 03:31:03,496 - INFO - 开始训练，配置文件: /tmp/tmphzugcv1v.yaml
2025-08-07 03:31:03,496 - INFO - 数据集类型: rml201801a
2025-08-07 03:31:03,496 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_033103
2025-08-07 03:31:03,507 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_033103/configs/config_backup.yaml
2025-08-07 03:31:03,507 - INFO - 使用设备: cuda
2025-08-07 03:31:03,507 - INFO - 加载数据...
2025-08-07 03:33:23,679 - INFO - 创建模型...
2025-08-07 03:33:23,890 - INFO - 可训练参数数量: 867,608
2025-08-07 03:33:23,890 - INFO - 总参数数量: 867,608
2025-08-07 03:33:23,890 - INFO - 可训练参数比例: 100.00%
2025-08-07 03:33:23,891 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 03:33:23,891 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 44, 'trial_number': 43}
2025-08-07 03:33:23,891 - INFO - 📊 最少训练轮数: 2
2025-08-07 03:33:23,891 - INFO - Epoch 1/400
2025-08-07 03:51:35,435 - INFO - Train Loss: 1.6463, Train Acc: 44.47%
2025-08-07 03:52:33,574 - INFO - Val Loss: 1.5041, Val Acc: 48.14%, Macro-F1: 48.52%, Kappa: 0.4588
2025-08-07 03:52:33,575 - INFO - Epoch 1 训练时间: 1149.68 秒
2025-08-07 03:52:33,639 - INFO - 保存最佳模型，验证准确率: 48.14%, Macro-F1: 48.52%, Kappa: 0.4588
2025-08-07 03:52:33,639 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_033103/models/best_model.pth
2025-08-07 03:52:33,639 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_033103/models/model_epoch_1_acc_48.14.pth
2025-08-07 03:52:33,639 - INFO - Epoch 2/400
2025-08-07 04:10:40,934 - INFO - Train Loss: 1.3861, Train Acc: 53.43%
2025-08-07 04:11:38,243 - INFO - Val Loss: 1.3421, Val Acc: 55.14%, Macro-F1: 56.89%, Kappa: 0.5319
2025-08-07 04:11:38,243 - INFO - Epoch 2 训练时间: 1144.60 秒
2025-08-07 04:11:38,244 - INFO - 🔍 第2轮门槛检查: 准确率 55.1371%, 门槛 57.4476
2025-08-07 04:11:38,244 - INFO - ⚠️ 第2轮准确率 55.1371% 未达到门槛 57.4476，提前结束训练
2025-08-07 04:11:38,244 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_033103/results/training_history.json
2025-08-07 04:11:38,244 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_033103/results/training_summary.json
2025-08-07 04:11:38,244 - INFO - 训练完成！
2025-08-07 04:11:38,244 - INFO - 最佳验证准确率: 48.14%
2025-08-07 04:11:38,244 - INFO - 最佳Macro-F1: 48.52%
2025-08-07 04:11:38,244 - INFO - 最佳Kappa: 0.4588
2025-08-07 04:11:38,244 - INFO - 总训练时间: 2294.29 秒 (38.2 分钟)
2025-08-07 04:11:38,244 - INFO - 平均每轮训练时间: 1147.14 秒
2025-08-07 04:11:38,244 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_033103
2025-08-07 04:11:38,244 - INFO - 🔍 第2轮准确率记录: 0.5514
2025-08-07 04:11:41,427 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 04:11:41,427 - INFO -    - 当前门槛: 57.4476
2025-08-07 04:11:41,427 - INFO -    - 第2轮准确率: 55.1371
2025-08-07 04:11:41,427 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 04:11:41,427 - INFO - 训练完成 rml201801a Trial 43, 结果: {'best_val_acc': 0.4813946216385241, 'best_val_f1': 0.4851711266354029, 'best_val_kappa': 0.45884656170976423, 'total_epochs': 2, 'total_training_time': 2294.2880115509033, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5513706483218678}
2025-08-07 04:11:41,427 - INFO - rml201801a Trial 43 早停训练，使用第2轮准确率: 0.5514
2025-08-07 04:11:41,431 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 04:11:41,431 - INFO - --------------------------------------------------------------------------------
2025-08-07 04:11:41,431 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 04:11:41,431 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 04:11:41,431 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 04:11:41,431 - INFO - --------------------------------------------------------------------------------
2025-08-07 04:11:41,821 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 04:11:41,821 - INFO -    - dropout: 0.1365209946341168 (数据集特定 + 通用)
2025-08-07 04:11:41,821 - INFO -    - lambda_lifting: 0.0781031067996393
2025-08-07 04:11:41,832 - INFO - 📄 临时配置文件创建: /tmp/tmp69j_dgkt.yaml
2025-08-07 04:11:41,832 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1365209946341168, 'batch_size': 256}
2025-08-07 04:11:41,832 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1365209946341168
2025-08-07 04:11:41,832 - INFO - ✅ 最终训练参数: lambda_lifting=0.0781031067996393, batch_size=128
2025-08-07 04:11:41,832 - INFO - rml201801a Trial 44: 参数 {'dropout': 0.1365209946341168, 'lambda_lifting': 0.0781031067996393}
2025-08-07 04:11:42,014 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 04:11:42,014 - INFO - ====================================================================================================
2025-08-07 04:11:42,014 - INFO - 🚀 开始训练 - rml201801a Trial 44
2025-08-07 04:11:42,014 - INFO - ====================================================================================================
2025-08-07 04:11:42,014 - INFO - 📋 本次训练参数:
2025-08-07 04:11:42,014 - INFO -   dropout: 0.1365
  lambda_lifting: 0.0781
2025-08-07 04:11:42,014 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 04:11:42,014 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 04:11:42,014 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 04:11:42,014 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 04:11:42,014 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 04:11:42,014 - INFO - ====================================================================================================
2025-08-07 04:11:42,014 - INFO - 🎯 开始训练...
2025-08-07 04:11:42,014 - INFO - ====================================================================================================
2025-08-07 04:11:42,014 - INFO - 🎯 开始训练 rml201801a Trial 44 (第45次试验)
2025-08-07 04:11:42,014 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 04:11:42,063 - INFO - 开始训练，配置文件: /tmp/tmp69j_dgkt.yaml
2025-08-07 04:11:42,063 - INFO - 数据集类型: rml201801a
2025-08-07 04:11:42,063 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_041142
2025-08-07 04:11:42,073 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_041142/configs/config_backup.yaml
2025-08-07 04:11:42,073 - INFO - 使用设备: cuda
2025-08-07 04:11:42,074 - INFO - 加载数据...
2025-08-07 04:13:50,677 - INFO - 创建模型...
2025-08-07 04:13:50,738 - INFO - 可训练参数数量: 867,608
2025-08-07 04:13:50,738 - INFO - 总参数数量: 867,608
2025-08-07 04:13:50,738 - INFO - 可训练参数比例: 100.00%
2025-08-07 04:13:50,740 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 04:13:50,740 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 45, 'trial_number': 44}
2025-08-07 04:13:50,740 - INFO - 📊 最少训练轮数: 2
2025-08-07 04:13:50,740 - INFO - Epoch 1/400
2025-08-07 04:31:57,865 - INFO - Train Loss: 1.5893, Train Acc: 45.70%
2025-08-07 04:32:55,815 - INFO - Val Loss: 1.4027, Val Acc: 51.81%, Macro-F1: 52.96%, Kappa: 0.4972
2025-08-07 04:32:55,816 - INFO - Epoch 1 训练时间: 1145.08 秒
2025-08-07 04:32:55,877 - INFO - 保存最佳模型，验证准确率: 51.81%, Macro-F1: 52.96%, Kappa: 0.4972
2025-08-07 04:32:55,877 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_041142/models/best_model.pth
2025-08-07 04:32:55,877 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_041142/models/model_epoch_1_acc_51.81.pth
2025-08-07 04:32:55,877 - INFO - Epoch 2/400
2025-08-07 04:50:59,407 - INFO - Train Loss: 1.3312, Train Acc: 55.05%
2025-08-07 04:51:56,451 - INFO - Val Loss: 1.3003, Val Acc: 55.97%, Macro-F1: 56.80%, Kappa: 0.5405
2025-08-07 04:51:56,451 - INFO - Epoch 2 训练时间: 1140.57 秒
2025-08-07 04:51:56,451 - INFO - 🔍 第2轮门槛检查: 准确率 55.9673%, 门槛 57.4476
2025-08-07 04:51:56,451 - INFO - ⚠️ 第2轮准确率 55.9673% 未达到门槛 57.4476，提前结束训练
2025-08-07 04:51:56,452 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_041142/results/training_history.json
2025-08-07 04:51:56,452 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_041142/results/training_summary.json
2025-08-07 04:51:56,452 - INFO - 训练完成！
2025-08-07 04:51:56,452 - INFO - 最佳验证准确率: 51.81%
2025-08-07 04:51:56,452 - INFO - 最佳Macro-F1: 52.96%
2025-08-07 04:51:56,452 - INFO - 最佳Kappa: 0.4972
2025-08-07 04:51:56,452 - INFO - 总训练时间: 2285.65 秒 (38.1 分钟)
2025-08-07 04:51:56,452 - INFO - 平均每轮训练时间: 1142.82 秒
2025-08-07 04:51:56,452 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_041142
2025-08-07 04:51:56,452 - INFO - 🔍 第2轮准确率记录: 0.5597
2025-08-07 04:51:59,396 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 04:51:59,396 - INFO -    - 当前门槛: 57.4476
2025-08-07 04:51:59,396 - INFO -    - 第2轮准确率: 55.9673
2025-08-07 04:51:59,396 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 04:51:59,396 - INFO - 训练完成 rml201801a Trial 44, 结果: {'best_val_acc': 0.5181311236189284, 'best_val_f1': 0.5295858658931837, 'best_val_kappa': 0.497180302906708, 'total_epochs': 2, 'total_training_time': 2285.649521112442, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5596727121117365}
2025-08-07 04:51:59,396 - INFO - rml201801a Trial 44 早停训练，使用第2轮准确率: 0.5597
2025-08-07 04:51:59,400 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 04:51:59,400 - INFO - --------------------------------------------------------------------------------
2025-08-07 04:51:59,400 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 04:51:59,400 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 04:51:59,400 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 04:51:59,400 - INFO - --------------------------------------------------------------------------------
2025-08-07 04:51:59,708 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 04:51:59,708 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 25 次)
2025-08-07 04:51:59,735 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 04:51:59,736 - INFO -    - dropout: 0.14002201609343332 (数据集特定 + 通用)
2025-08-07 04:51:59,736 - INFO -    - lambda_lifting: 0.16398704057018001
2025-08-07 04:51:59,746 - INFO - 📄 临时配置文件创建: /tmp/tmpcka_di7v.yaml
2025-08-07 04:51:59,746 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.14002201609343332, 'batch_size': 256}
2025-08-07 04:51:59,746 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.14002201609343332
2025-08-07 04:51:59,746 - INFO - ✅ 最终训练参数: lambda_lifting=0.16398704057018001, batch_size=128
2025-08-07 04:51:59,747 - INFO - rml201801a Trial 45: 参数 {'dropout': 0.14002201609343332, 'lambda_lifting': 0.16398704057018001}
2025-08-07 04:51:59,834 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 04:51:59,835 - INFO - ====================================================================================================
2025-08-07 04:51:59,835 - INFO - 🚀 开始训练 - rml201801a Trial 45
2025-08-07 04:51:59,835 - INFO - ====================================================================================================
2025-08-07 04:51:59,835 - INFO - 📋 本次训练参数:
2025-08-07 04:51:59,835 - INFO -   dropout: 0.1400
  lambda_lifting: 0.1640
2025-08-07 04:51:59,835 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 04:51:59,835 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 04:51:59,835 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 04:51:59,835 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 04:51:59,835 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 04:51:59,835 - INFO - ====================================================================================================
2025-08-07 04:51:59,835 - INFO - 🎯 开始训练...
2025-08-07 04:51:59,835 - INFO - ====================================================================================================
2025-08-07 04:51:59,835 - INFO - 🎯 开始训练 rml201801a Trial 45 (第46次试验)
2025-08-07 04:51:59,835 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 04:51:59,883 - INFO - 开始训练，配置文件: /tmp/tmpcka_di7v.yaml
2025-08-07 04:51:59,883 - INFO - 数据集类型: rml201801a
2025-08-07 04:51:59,883 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_045159
2025-08-07 04:51:59,894 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_045159/configs/config_backup.yaml
2025-08-07 04:51:59,894 - INFO - 使用设备: cuda
2025-08-07 04:51:59,894 - INFO - 加载数据...
2025-08-07 04:54:09,771 - INFO - 创建模型...
2025-08-07 04:54:09,808 - INFO - 可训练参数数量: 867,608
2025-08-07 04:54:09,808 - INFO - 总参数数量: 867,608
2025-08-07 04:54:09,808 - INFO - 可训练参数比例: 100.00%
2025-08-07 04:54:09,810 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 04:54:09,810 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 46, 'trial_number': 45}
2025-08-07 04:54:09,810 - INFO - 📊 最少训练轮数: 2
2025-08-07 04:54:09,810 - INFO - Epoch 1/400
2025-08-07 05:12:16,679 - INFO - Train Loss: 1.6143, Train Acc: 45.42%
2025-08-07 05:13:14,920 - INFO - Val Loss: 1.4025, Val Acc: 51.38%, Macro-F1: 51.32%, Kappa: 0.4927
2025-08-07 05:13:14,920 - INFO - Epoch 1 训练时间: 1145.11 秒
2025-08-07 05:13:14,984 - INFO - 保存最佳模型，验证准确率: 51.38%, Macro-F1: 51.32%, Kappa: 0.4927
2025-08-07 05:13:14,985 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_045159/models/best_model.pth
2025-08-07 05:13:14,985 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_045159/models/model_epoch_1_acc_51.38.pth
2025-08-07 05:13:14,985 - INFO - Epoch 2/400
2025-08-07 05:31:19,780 - INFO - Train Loss: 1.3586, Train Acc: 54.61%
2025-08-07 05:32:17,068 - INFO - Val Loss: 1.3275, Val Acc: 56.01%, Macro-F1: 57.66%, Kappa: 0.5410
2025-08-07 05:32:17,069 - INFO - Epoch 2 训练时间: 1142.08 秒
2025-08-07 05:32:17,069 - INFO - 🔍 第2轮门槛检查: 准确率 56.0110%, 门槛 57.4476
2025-08-07 05:32:17,069 - INFO - ⚠️ 第2轮准确率 56.0110% 未达到门槛 57.4476，提前结束训练
2025-08-07 05:32:17,069 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_045159/results/training_history.json
2025-08-07 05:32:17,070 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_045159/results/training_summary.json
2025-08-07 05:32:17,070 - INFO - 训练完成！
2025-08-07 05:32:17,070 - INFO - 最佳验证准确率: 51.38%
2025-08-07 05:32:17,070 - INFO - 最佳Macro-F1: 51.32%
2025-08-07 05:32:17,070 - INFO - 最佳Kappa: 0.4927
2025-08-07 05:32:17,070 - INFO - 总训练时间: 2287.19 秒 (38.1 分钟)
2025-08-07 05:32:17,070 - INFO - 平均每轮训练时间: 1143.60 秒
2025-08-07 05:32:17,070 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_045159
2025-08-07 05:32:17,070 - INFO - 🔍 第2轮准确率记录: 0.5601
2025-08-07 05:32:19,742 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 05:32:19,742 - INFO -    - 当前门槛: 57.4476
2025-08-07 05:32:19,742 - INFO -    - 第2轮准确率: 56.0110
2025-08-07 05:32:19,742 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 05:32:19,743 - INFO - 训练完成 rml201801a Trial 45, 结果: {'best_val_acc': 0.5138002918490724, 'best_val_f1': 0.5132337016718682, 'best_val_kappa': 0.49266117410337984, 'total_epochs': 2, 'total_training_time': 2287.194304227829, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5601104857202418}
2025-08-07 05:32:19,743 - INFO - rml201801a Trial 45 早停训练，使用第2轮准确率: 0.5601
2025-08-07 05:32:19,747 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 05:32:19,747 - INFO - --------------------------------------------------------------------------------
2025-08-07 05:32:19,747 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 05:32:19,747 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 05:32:19,747 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 05:32:19,747 - INFO - --------------------------------------------------------------------------------
2025-08-07 05:32:20,068 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 05:32:20,068 - INFO -    - dropout: 0.2271043244941432 (数据集特定 + 通用)
2025-08-07 05:32:20,068 - INFO -    - lambda_lifting: 0.028148458025824365
2025-08-07 05:32:20,078 - INFO - 📄 临时配置文件创建: /tmp/tmptvjakegi.yaml
2025-08-07 05:32:20,079 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.2271043244941432, 'batch_size': 256}
2025-08-07 05:32:20,079 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.2271043244941432
2025-08-07 05:32:20,079 - INFO - ✅ 最终训练参数: lambda_lifting=0.028148458025824365, batch_size=128
2025-08-07 05:32:20,079 - INFO - rml201801a Trial 46: 参数 {'dropout': 0.2271043244941432, 'lambda_lifting': 0.028148458025824365}
2025-08-07 05:32:20,178 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 05:32:20,178 - INFO - ====================================================================================================
2025-08-07 05:32:20,178 - INFO - 🚀 开始训练 - rml201801a Trial 46
2025-08-07 05:32:20,178 - INFO - ====================================================================================================
2025-08-07 05:32:20,178 - INFO - 📋 本次训练参数:
2025-08-07 05:32:20,178 - INFO -   dropout: 0.2271
  lambda_lifting: 0.0281
2025-08-07 05:32:20,178 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 05:32:20,178 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 05:32:20,178 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 05:32:20,178 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 05:32:20,178 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 05:32:20,178 - INFO - ====================================================================================================
2025-08-07 05:32:20,178 - INFO - 🎯 开始训练...
2025-08-07 05:32:20,178 - INFO - ====================================================================================================
2025-08-07 05:32:20,178 - INFO - 🎯 开始训练 rml201801a Trial 46 (第47次试验)
2025-08-07 05:32:20,178 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 05:32:20,226 - INFO - 开始训练，配置文件: /tmp/tmptvjakegi.yaml
2025-08-07 05:32:20,226 - INFO - 数据集类型: rml201801a
2025-08-07 05:32:20,226 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_053220
2025-08-07 05:32:20,237 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_053220/configs/config_backup.yaml
2025-08-07 05:32:20,237 - INFO - 使用设备: cuda
2025-08-07 05:32:20,237 - INFO - 加载数据...
2025-08-07 05:34:32,473 - INFO - 创建模型...
2025-08-07 05:34:32,580 - INFO - 可训练参数数量: 867,608
2025-08-07 05:34:32,581 - INFO - 总参数数量: 867,608
2025-08-07 05:34:32,581 - INFO - 可训练参数比例: 100.00%
2025-08-07 05:34:32,582 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 05:34:32,582 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 47, 'trial_number': 46}
2025-08-07 05:34:32,582 - INFO - 📊 最少训练轮数: 2
2025-08-07 05:34:32,582 - INFO - Epoch 1/400
2025-08-07 05:52:38,957 - INFO - Train Loss: 1.6015, Train Acc: 45.25%
2025-08-07 05:53:37,358 - INFO - Val Loss: 1.4512, Val Acc: 50.07%, Macro-F1: 50.84%, Kappa: 0.4790
2025-08-07 05:53:37,359 - INFO - Epoch 1 训练时间: 1144.78 秒
2025-08-07 05:53:37,422 - INFO - 保存最佳模型，验证准确率: 50.07%, Macro-F1: 50.84%, Kappa: 0.4790
2025-08-07 05:53:37,422 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_053220/models/best_model.pth
2025-08-07 05:53:37,422 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_053220/models/model_epoch_1_acc_50.07.pth
2025-08-07 05:53:37,422 - INFO - Epoch 2/400
2025-08-07 06:11:41,844 - INFO - Train Loss: 1.3586, Train Acc: 54.45%
2025-08-07 06:12:39,029 - INFO - Val Loss: 1.3321, Val Acc: 55.57%, Macro-F1: 56.73%, Kappa: 0.5364
2025-08-07 06:12:39,029 - INFO - Epoch 2 训练时间: 1141.61 秒
2025-08-07 06:12:39,029 - INFO - 🔍 第2轮门槛检查: 准确率 55.5730%, 门槛 57.4476
2025-08-07 06:12:39,029 - INFO - ⚠️ 第2轮准确率 55.5730% 未达到门槛 57.4476，提前结束训练
2025-08-07 06:12:39,030 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_053220/results/training_history.json
2025-08-07 06:12:39,030 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_053220/results/training_summary.json
2025-08-07 06:12:39,030 - INFO - 训练完成！
2025-08-07 06:12:39,030 - INFO - 最佳验证准确率: 50.07%
2025-08-07 06:12:39,030 - INFO - 最佳Macro-F1: 50.84%
2025-08-07 06:12:39,030 - INFO - 最佳Kappa: 0.4790
2025-08-07 06:12:39,030 - INFO - 总训练时间: 2286.38 秒 (38.1 分钟)
2025-08-07 06:12:39,030 - INFO - 平均每轮训练时间: 1143.19 秒
2025-08-07 06:12:39,030 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_053220
2025-08-07 06:12:39,030 - INFO - 🔍 第2轮准确率记录: 0.5557
2025-08-07 06:12:42,050 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 06:12:42,050 - INFO -    - 当前门槛: 57.4476
2025-08-07 06:12:42,050 - INFO -    - 第2轮准确率: 55.5730
2025-08-07 06:12:42,050 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 06:12:42,050 - INFO - 训练完成 rml201801a Trial 46, 结果: {'best_val_acc': 0.5007348342714196, 'best_val_f1': 0.5083737970139656, 'best_val_kappa': 0.4790276531527857, 'total_epochs': 2, 'total_training_time': 2286.3836557865143, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5557301438398999}
2025-08-07 06:12:42,051 - INFO - rml201801a Trial 46 早停训练，使用第2轮准确率: 0.5557
2025-08-07 06:12:42,056 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 06:12:42,056 - INFO - --------------------------------------------------------------------------------
2025-08-07 06:12:42,056 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 06:12:42,056 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 06:12:42,056 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 06:12:42,056 - INFO - --------------------------------------------------------------------------------
2025-08-07 06:12:42,425 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 06:12:42,425 - INFO -    - dropout: 0.18277188872221478 (数据集特定 + 通用)
2025-08-07 06:12:42,425 - INFO -    - lambda_lifting: 0.10626313350547034
2025-08-07 06:12:42,436 - INFO - 📄 临时配置文件创建: /tmp/tmpboj8rzle.yaml
2025-08-07 06:12:42,436 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.18277188872221478, 'batch_size': 256}
2025-08-07 06:12:42,436 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.18277188872221478
2025-08-07 06:12:42,436 - INFO - ✅ 最终训练参数: lambda_lifting=0.10626313350547034, batch_size=128
2025-08-07 06:12:42,436 - INFO - rml201801a Trial 47: 参数 {'dropout': 0.18277188872221478, 'lambda_lifting': 0.10626313350547034}
2025-08-07 06:12:42,555 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 06:12:42,555 - INFO - ====================================================================================================
2025-08-07 06:12:42,555 - INFO - 🚀 开始训练 - rml201801a Trial 47
2025-08-07 06:12:42,555 - INFO - ====================================================================================================
2025-08-07 06:12:42,555 - INFO - 📋 本次训练参数:
2025-08-07 06:12:42,555 - INFO -   dropout: 0.1828
  lambda_lifting: 0.1063
2025-08-07 06:12:42,555 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 06:12:42,555 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 06:12:42,555 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 06:12:42,555 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 06:12:42,555 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 06:12:42,555 - INFO - ====================================================================================================
2025-08-07 06:12:42,555 - INFO - 🎯 开始训练...
2025-08-07 06:12:42,555 - INFO - ====================================================================================================
2025-08-07 06:12:42,555 - INFO - 🎯 开始训练 rml201801a Trial 47 (第48次试验)
2025-08-07 06:12:42,556 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 06:12:42,603 - INFO - 开始训练，配置文件: /tmp/tmpboj8rzle.yaml
2025-08-07 06:12:42,603 - INFO - 数据集类型: rml201801a
2025-08-07 06:12:42,603 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_061242
2025-08-07 06:12:42,614 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_061242/configs/config_backup.yaml
2025-08-07 06:12:42,614 - INFO - 使用设备: cuda
2025-08-07 06:12:42,614 - INFO - 加载数据...
2025-08-07 06:14:50,043 - INFO - 创建模型...
2025-08-07 06:14:50,106 - INFO - 可训练参数数量: 867,608
2025-08-07 06:14:50,106 - INFO - 总参数数量: 867,608
2025-08-07 06:14:50,106 - INFO - 可训练参数比例: 100.00%
2025-08-07 06:14:50,107 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 06:14:50,107 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 48, 'trial_number': 47}
2025-08-07 06:14:50,107 - INFO - 📊 最少训练轮数: 2
2025-08-07 06:14:50,107 - INFO - Epoch 1/400
2025-08-07 06:32:56,990 - INFO - Train Loss: 1.6108, Train Acc: 45.26%
2025-08-07 06:33:54,915 - INFO - Val Loss: 1.4182, Val Acc: 51.02%, Macro-F1: 51.16%, Kappa: 0.4889
2025-08-07 06:33:54,916 - INFO - Epoch 1 训练时间: 1144.81 秒
2025-08-07 06:33:54,978 - INFO - 保存最佳模型，验证准确率: 51.02%, Macro-F1: 51.16%, Kappa: 0.4889
2025-08-07 06:33:54,978 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_061242/models/best_model.pth
2025-08-07 06:33:54,978 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_061242/models/model_epoch_1_acc_51.02.pth
2025-08-07 06:33:54,978 - INFO - Epoch 2/400
2025-08-07 06:51:59,255 - INFO - Train Loss: 1.3615, Train Acc: 54.25%
2025-08-07 06:52:56,221 - INFO - Val Loss: 1.3014, Val Acc: 56.66%, Macro-F1: 58.27%, Kappa: 0.5477
2025-08-07 06:52:56,221 - INFO - Epoch 2 训练时间: 1141.24 秒
2025-08-07 06:52:56,221 - INFO - 🔍 第2轮门槛检查: 准确率 56.6568%, 门槛 57.4476
2025-08-07 06:52:56,221 - INFO - ⚠️ 第2轮准确率 56.6568% 未达到门槛 57.4476，提前结束训练
2025-08-07 06:52:56,222 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_061242/results/training_history.json
2025-08-07 06:52:56,222 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_061242/results/training_summary.json
2025-08-07 06:52:56,222 - INFO - 训练完成！
2025-08-07 06:52:56,222 - INFO - 最佳验证准确率: 51.02%
2025-08-07 06:52:56,222 - INFO - 最佳Macro-F1: 51.16%
2025-08-07 06:52:56,222 - INFO - 最佳Kappa: 0.4889
2025-08-07 06:52:56,222 - INFO - 总训练时间: 2286.05 秒 (38.1 分钟)
2025-08-07 06:52:56,222 - INFO - 平均每轮训练时间: 1143.03 秒
2025-08-07 06:52:56,222 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_061242
2025-08-07 06:52:56,222 - INFO - 🔍 第2轮准确率记录: 0.5666
2025-08-07 06:52:58,861 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 06:52:58,862 - INFO -    - 当前门槛: 57.4476
2025-08-07 06:52:58,862 - INFO -    - 第2轮准确率: 56.6568
2025-08-07 06:52:58,862 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 06:52:58,862 - INFO - 训练完成 rml201801a Trial 47, 结果: {'best_val_acc': 0.5101964769647697, 'best_val_f1': 0.511630468890018, 'best_val_kappa': 0.48890067161541184, 'total_epochs': 2, 'total_training_time': 2286.0517551898956, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5665676464456952}
2025-08-07 06:52:58,862 - INFO - rml201801a Trial 47 早停训练，使用第2轮准确率: 0.5666
2025-08-07 06:52:58,866 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 06:52:58,866 - INFO - --------------------------------------------------------------------------------
2025-08-07 06:52:58,866 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 06:52:58,866 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 06:52:58,866 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 06:52:58,866 - INFO - --------------------------------------------------------------------------------
2025-08-07 06:52:59,256 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 06:52:59,256 - INFO -    - dropout: 0.19509223398316874 (数据集特定 + 通用)
2025-08-07 06:52:59,256 - INFO -    - lambda_lifting: 0.055877746479826074
2025-08-07 06:52:59,268 - INFO - 📄 临时配置文件创建: /tmp/tmpzxy_dqq7.yaml
2025-08-07 06:52:59,268 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.19509223398316874, 'batch_size': 256}
2025-08-07 06:52:59,268 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.19509223398316874
2025-08-07 06:52:59,268 - INFO - ✅ 最终训练参数: lambda_lifting=0.055877746479826074, batch_size=128
2025-08-07 06:52:59,268 - INFO - rml201801a Trial 48: 参数 {'dropout': 0.19509223398316874, 'lambda_lifting': 0.055877746479826074}
2025-08-07 06:52:59,363 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 06:52:59,363 - INFO - ====================================================================================================
2025-08-07 06:52:59,363 - INFO - 🚀 开始训练 - rml201801a Trial 48
2025-08-07 06:52:59,363 - INFO - ====================================================================================================
2025-08-07 06:52:59,363 - INFO - 📋 本次训练参数:
2025-08-07 06:52:59,363 - INFO -   dropout: 0.1951
  lambda_lifting: 0.0559
2025-08-07 06:52:59,363 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 06:52:59,363 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 06:52:59,363 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 06:52:59,363 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 06:52:59,364 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 06:52:59,364 - INFO - ====================================================================================================
2025-08-07 06:52:59,364 - INFO - 🎯 开始训练...
2025-08-07 06:52:59,364 - INFO - ====================================================================================================
2025-08-07 06:52:59,364 - INFO - 🎯 开始训练 rml201801a Trial 48 (第49次试验)
2025-08-07 06:52:59,364 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 06:52:59,415 - INFO - 开始训练，配置文件: /tmp/tmpzxy_dqq7.yaml
2025-08-07 06:52:59,415 - INFO - 数据集类型: rml201801a
2025-08-07 06:52:59,415 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_065259
2025-08-07 06:52:59,427 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_065259/configs/config_backup.yaml
2025-08-07 06:52:59,427 - INFO - 使用设备: cuda
2025-08-07 06:52:59,428 - INFO - 加载数据...
2025-08-07 06:55:05,092 - INFO - 创建模型...
2025-08-07 06:55:05,160 - INFO - 可训练参数数量: 867,608
2025-08-07 06:55:05,160 - INFO - 总参数数量: 867,608
2025-08-07 06:55:05,160 - INFO - 可训练参数比例: 100.00%
2025-08-07 06:55:05,161 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 06:55:05,161 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 49, 'trial_number': 48}
2025-08-07 06:55:05,161 - INFO - 📊 最少训练轮数: 2
2025-08-07 06:55:05,161 - INFO - Epoch 1/400
2025-08-07 07:13:13,166 - INFO - Train Loss: 1.6164, Train Acc: 44.53%
2025-08-07 07:14:11,376 - INFO - Val Loss: 1.4707, Val Acc: 49.21%, Macro-F1: 48.58%, Kappa: 0.4700
2025-08-07 07:14:11,377 - INFO - Epoch 1 训练时间: 1146.22 秒
2025-08-07 07:14:11,439 - INFO - 保存最佳模型，验证准确率: 49.21%, Macro-F1: 48.58%, Kappa: 0.4700
2025-08-07 07:14:11,439 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_065259/models/best_model.pth
2025-08-07 07:14:11,439 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_065259/models/model_epoch_1_acc_49.21.pth
2025-08-07 07:14:11,440 - INFO - Epoch 2/400
2025-08-07 07:32:17,875 - INFO - Train Loss: 1.3526, Train Acc: 54.11%
2025-08-07 07:33:15,023 - INFO - Val Loss: 1.3647, Val Acc: 54.88%, Macro-F1: 55.38%, Kappa: 0.5292
2025-08-07 07:33:15,023 - INFO - Epoch 2 训练时间: 1143.58 秒
2025-08-07 07:33:15,023 - INFO - 🔍 第2轮门槛检查: 准确率 54.8794%, 门槛 57.4476
2025-08-07 07:33:15,023 - INFO - ⚠️ 第2轮准确率 54.8794% 未达到门槛 57.4476，提前结束训练
2025-08-07 07:33:15,024 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_065259/results/training_history.json
2025-08-07 07:33:15,024 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_065259/results/training_summary.json
2025-08-07 07:33:15,024 - INFO - 训练完成！
2025-08-07 07:33:15,024 - INFO - 最佳验证准确率: 49.21%
2025-08-07 07:33:15,024 - INFO - 最佳Macro-F1: 48.58%
2025-08-07 07:33:15,024 - INFO - 最佳Kappa: 0.4700
2025-08-07 07:33:15,024 - INFO - 总训练时间: 2289.80 秒 (38.2 分钟)
2025-08-07 07:33:15,024 - INFO - 平均每轮训练时间: 1144.90 秒
2025-08-07 07:33:15,024 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_065259
2025-08-07 07:33:15,024 - INFO - 🔍 第2轮准确率记录: 0.5488
2025-08-07 07:33:18,206 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 07:33:18,206 - INFO -    - 当前门槛: 57.4476
2025-08-07 07:33:18,206 - INFO -    - 第2轮准确率: 54.8794
2025-08-07 07:33:18,206 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 07:33:18,206 - INFO - 训练完成 rml201801a Trial 48, 结果: {'best_val_acc': 0.4920627475505524, 'best_val_f1': 0.485829396043808, 'best_val_kappa': 0.4699785191831851, 'total_epochs': 2, 'total_training_time': 2289.7993590831757, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5487935167813217}
2025-08-07 07:33:18,206 - INFO - rml201801a Trial 48 早停训练，使用第2轮准确率: 0.5488
2025-08-07 07:33:18,211 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 07:33:18,211 - INFO - --------------------------------------------------------------------------------
2025-08-07 07:33:18,211 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 07:33:18,211 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 07:33:18,211 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 07:33:18,211 - INFO - --------------------------------------------------------------------------------
2025-08-07 07:33:18,745 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 07:33:18,745 - INFO -    - dropout: 0.1310895904509244 (数据集特定 + 通用)
2025-08-07 07:33:18,745 - INFO -    - lambda_lifting: 0.11212562900677545
2025-08-07 07:33:18,757 - INFO - 📄 临时配置文件创建: /tmp/tmpaiggq30r.yaml
2025-08-07 07:33:18,758 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1310895904509244, 'batch_size': 256}
2025-08-07 07:33:18,758 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1310895904509244
2025-08-07 07:33:18,758 - INFO - ✅ 最终训练参数: lambda_lifting=0.11212562900677545, batch_size=128
2025-08-07 07:33:18,758 - INFO - rml201801a Trial 49: 参数 {'dropout': 0.1310895904509244, 'lambda_lifting': 0.11212562900677545}
2025-08-07 07:33:18,862 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 07:33:18,862 - INFO - ====================================================================================================
2025-08-07 07:33:18,862 - INFO - 🚀 开始训练 - rml201801a Trial 49
2025-08-07 07:33:18,862 - INFO - ====================================================================================================
2025-08-07 07:33:18,862 - INFO - 📋 本次训练参数:
2025-08-07 07:33:18,862 - INFO -   dropout: 0.1311
  lambda_lifting: 0.1121
2025-08-07 07:33:18,862 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 07:33:18,862 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 07:33:18,862 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 07:33:18,862 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 07:33:18,862 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 07:33:18,862 - INFO - ====================================================================================================
2025-08-07 07:33:18,862 - INFO - 🎯 开始训练...
2025-08-07 07:33:18,862 - INFO - ====================================================================================================
2025-08-07 07:33:18,862 - INFO - 🎯 开始训练 rml201801a Trial 49 (第50次试验)
2025-08-07 07:33:18,862 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 07:33:18,914 - INFO - 开始训练，配置文件: /tmp/tmpaiggq30r.yaml
2025-08-07 07:33:18,914 - INFO - 数据集类型: rml201801a
2025-08-07 07:33:18,914 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_073318
2025-08-07 07:33:18,925 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_073318/configs/config_backup.yaml
2025-08-07 07:33:18,925 - INFO - 使用设备: cuda
2025-08-07 07:33:18,925 - INFO - 加载数据...
2025-08-07 07:35:26,503 - INFO - 创建模型...
2025-08-07 07:35:26,551 - INFO - 可训练参数数量: 867,608
2025-08-07 07:35:26,551 - INFO - 总参数数量: 867,608
2025-08-07 07:35:26,551 - INFO - 可训练参数比例: 100.00%
2025-08-07 07:35:26,552 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 07:35:26,552 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 50, 'trial_number': 49}
2025-08-07 07:35:26,552 - INFO - 📊 最少训练轮数: 2
2025-08-07 07:35:26,552 - INFO - Epoch 1/400
2025-08-07 07:53:35,441 - INFO - Train Loss: 1.5968, Train Acc: 45.51%
2025-08-07 07:54:33,485 - INFO - Val Loss: 1.3812, Val Acc: 52.37%, Macro-F1: 52.25%, Kappa: 0.5030
2025-08-07 07:54:33,485 - INFO - Epoch 1 训练时间: 1146.93 秒
2025-08-07 07:54:33,546 - INFO - 保存最佳模型，验证准确率: 52.37%, Macro-F1: 52.25%, Kappa: 0.5030
2025-08-07 07:54:33,546 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_073318/models/best_model.pth
2025-08-07 07:54:33,546 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_073318/models/model_epoch_1_acc_52.37.pth
2025-08-07 07:54:33,547 - INFO - Epoch 2/400
2025-08-07 08:12:39,190 - INFO - Train Loss: 1.3313, Train Acc: 55.13%
2025-08-07 08:13:36,267 - INFO - Val Loss: 1.2886, Val Acc: 56.67%, Macro-F1: 58.21%, Kappa: 0.5479
2025-08-07 08:13:36,268 - INFO - Epoch 2 训练时间: 1142.72 秒
2025-08-07 08:13:36,268 - INFO - 🔍 第2轮门槛检查: 准确率 56.6740%, 门槛 57.4476
2025-08-07 08:13:36,268 - INFO - ⚠️ 第2轮准确率 56.6740% 未达到门槛 57.4476，提前结束训练
2025-08-07 08:13:36,268 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_073318/results/training_history.json
2025-08-07 08:13:36,268 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_073318/results/training_summary.json
2025-08-07 08:13:36,268 - INFO - 训练完成！
2025-08-07 08:13:36,268 - INFO - 最佳验证准确率: 52.37%
2025-08-07 08:13:36,268 - INFO - 最佳Macro-F1: 52.25%
2025-08-07 08:13:36,268 - INFO - 最佳Kappa: 0.5030
2025-08-07 08:13:36,268 - INFO - 总训练时间: 2289.65 秒 (38.2 分钟)
2025-08-07 08:13:36,269 - INFO - 平均每轮训练时间: 1144.83 秒
2025-08-07 08:13:36,269 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_073318
2025-08-07 08:13:36,269 - INFO - 🔍 第2轮准确率记录: 0.5667
2025-08-07 08:13:39,164 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 08:13:39,165 - INFO -    - 当前门槛: 57.4476
2025-08-07 08:13:39,165 - INFO -    - 第2轮准确率: 56.6740
2025-08-07 08:13:39,165 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 08:13:39,165 - INFO - 训练完成 rml201801a Trial 49, 结果: {'best_val_acc': 0.5236840733791953, 'best_val_f1': 0.5225476082168743, 'best_val_kappa': 0.5029746852652472, 'total_epochs': 2, 'total_training_time': 2289.6540529727936, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5667396289347508}
2025-08-07 08:13:39,165 - INFO - rml201801a Trial 49 早停训练，使用第2轮准确率: 0.5667
2025-08-07 08:13:39,169 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 08:13:39,169 - INFO - --------------------------------------------------------------------------------
2025-08-07 08:13:39,169 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 08:13:39,169 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 08:13:39,169 - INFO - #3 | 试验22 | 准确率:0.5673 | dropout=0.10826720091836849, lambda_lifting=0.0465412101136831
2025-08-07 08:13:39,169 - INFO - --------------------------------------------------------------------------------
2025-08-07 08:13:39,477 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 08:13:39,477 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 30 次)
2025-08-07 08:13:39,505 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 08:13:39,505 - INFO -    - dropout: 0.12741684389735686 (数据集特定 + 通用)
2025-08-07 08:13:39,505 - INFO -    - lambda_lifting: 0.013054893507154788
2025-08-07 08:13:39,516 - INFO - 📄 临时配置文件创建: /tmp/tmpz90cjcvb.yaml
2025-08-07 08:13:39,516 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.12741684389735686, 'batch_size': 256}
2025-08-07 08:13:39,516 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.12741684389735686
2025-08-07 08:13:39,516 - INFO - ✅ 最终训练参数: lambda_lifting=0.013054893507154788, batch_size=128
2025-08-07 08:13:39,516 - INFO - rml201801a Trial 50: 参数 {'dropout': 0.12741684389735686, 'lambda_lifting': 0.013054893507154788}
2025-08-07 08:13:39,615 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 08:13:39,615 - INFO - ====================================================================================================
2025-08-07 08:13:39,615 - INFO - 🚀 开始训练 - rml201801a Trial 50
2025-08-07 08:13:39,615 - INFO - ====================================================================================================
2025-08-07 08:13:39,615 - INFO - 📋 本次训练参数:
2025-08-07 08:13:39,615 - INFO -   dropout: 0.1274
  lambda_lifting: 0.0131
2025-08-07 08:13:39,615 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 08:13:39,615 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 08:13:39,615 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 08:13:39,615 - INFO -   #3 | Trial22 | 准确率:0.5673 | dropout=0.1083, lambda_lifting=0.0465
2025-08-07 08:13:39,615 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 08:13:39,615 - INFO - ====================================================================================================
2025-08-07 08:13:39,615 - INFO - 🎯 开始训练...
2025-08-07 08:13:39,615 - INFO - ====================================================================================================
2025-08-07 08:13:39,615 - INFO - 🎯 开始训练 rml201801a Trial 50 (第51次试验)
2025-08-07 08:13:39,615 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 08:13:39,663 - INFO - 开始训练，配置文件: /tmp/tmpz90cjcvb.yaml
2025-08-07 08:13:39,663 - INFO - 数据集类型: rml201801a
2025-08-07 08:13:39,663 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_081339
2025-08-07 08:13:39,674 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_081339/configs/config_backup.yaml
2025-08-07 08:13:39,674 - INFO - 使用设备: cuda
2025-08-07 08:13:39,674 - INFO - 加载数据...
2025-08-07 08:15:47,894 - INFO - 创建模型...
2025-08-07 08:15:48,059 - INFO - 可训练参数数量: 867,608
2025-08-07 08:15:48,059 - INFO - 总参数数量: 867,608
2025-08-07 08:15:48,059 - INFO - 可训练参数比例: 100.00%
2025-08-07 08:15:48,060 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 08:15:48,061 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 51, 'trial_number': 50}
2025-08-07 08:15:48,061 - INFO - 📊 最少训练轮数: 2
2025-08-07 08:15:48,061 - INFO - Epoch 1/400
2025-08-07 08:33:57,804 - INFO - Train Loss: 1.5407, Train Acc: 47.06%
2025-08-07 08:34:56,231 - INFO - Val Loss: 1.3698, Val Acc: 53.25%, Macro-F1: 54.06%, Kappa: 0.5121
2025-08-07 08:34:56,231 - INFO - Epoch 1 训练时间: 1148.17 秒
2025-08-07 08:34:56,294 - INFO - 保存最佳模型，验证准确率: 53.25%, Macro-F1: 54.06%, Kappa: 0.5121
2025-08-07 08:34:56,294 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_081339/models/best_model.pth
2025-08-07 08:34:56,294 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_081339/models/model_epoch_1_acc_53.25.pth
2025-08-07 08:34:56,294 - INFO - Epoch 2/400
2025-08-07 08:53:00,363 - INFO - Train Loss: 1.2998, Train Acc: 56.06%
2025-08-07 08:53:57,379 - INFO - Val Loss: 1.2784, Val Acc: 56.88%, Macro-F1: 59.05%, Kappa: 0.5500
2025-08-07 08:53:57,380 - INFO - Epoch 2 训练时间: 1141.09 秒
2025-08-07 08:53:57,380 - INFO - 🔍 第2轮门槛检查: 准确率 56.8770%, 门槛 57.4476
2025-08-07 08:53:57,380 - INFO - ⚠️ 第2轮准确率 56.8770% 未达到门槛 57.4476，提前结束训练
2025-08-07 08:53:57,380 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_081339/results/training_history.json
2025-08-07 08:53:57,380 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_081339/results/training_summary.json
2025-08-07 08:53:57,380 - INFO - 训练完成！
2025-08-07 08:53:57,381 - INFO - 最佳验证准确率: 53.25%
2025-08-07 08:53:57,381 - INFO - 最佳Macro-F1: 54.06%
2025-08-07 08:53:57,381 - INFO - 最佳Kappa: 0.5121
2025-08-07 08:53:57,381 - INFO - 总训练时间: 2289.26 秒 (38.2 分钟)
2025-08-07 08:53:57,381 - INFO - 平均每轮训练时间: 1144.63 秒
2025-08-07 08:53:57,381 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_081339
2025-08-07 08:53:57,381 - INFO - 🔍 第2轮准确率记录: 0.5688
2025-08-07 08:54:00,595 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 08:54:00,595 - INFO -    - 当前门槛: 57.4476
2025-08-07 08:54:00,596 - INFO -    - 第2轮准确率: 56.8770
2025-08-07 08:54:00,596 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 08:54:00,596 - INFO - 训练完成 rml201801a Trial 50, 结果: {'best_val_acc': 0.5324682092974775, 'best_val_f1': 0.5406304611031802, 'best_val_kappa': 0.5121407401364984, 'total_epochs': 2, 'total_training_time': 2289.255879163742, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5687695434646654}
2025-08-07 08:54:00,596 - INFO - rml201801a Trial 50 早停训练，使用第2轮准确率: 0.5688
2025-08-07 08:54:00,600 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 08:54:00,600 - INFO - --------------------------------------------------------------------------------
2025-08-07 08:54:00,600 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 08:54:00,600 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 08:54:00,600 - INFO - #3 | 试验50 | 准确率:0.5688 | dropout=0.12741684389735686, lambda_lifting=0.013054893507154788
2025-08-07 08:54:00,600 - INFO - --------------------------------------------------------------------------------
2025-08-07 08:54:01,019 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 08:54:01,020 - INFO -    - dropout: 0.1344342278629412 (数据集特定 + 通用)
2025-08-07 08:54:01,020 - INFO -    - lambda_lifting: 0.013304180223409408
2025-08-07 08:54:01,030 - INFO - 📄 临时配置文件创建: /tmp/tmp6zyn7g58.yaml
2025-08-07 08:54:01,030 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1344342278629412, 'batch_size': 256}
2025-08-07 08:54:01,030 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1344342278629412
2025-08-07 08:54:01,030 - INFO - ✅ 最终训练参数: lambda_lifting=0.013304180223409408, batch_size=128
2025-08-07 08:54:01,031 - INFO - rml201801a Trial 51: 参数 {'dropout': 0.1344342278629412, 'lambda_lifting': 0.013304180223409408}
2025-08-07 08:54:01,153 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 08:54:01,153 - INFO - ====================================================================================================
2025-08-07 08:54:01,153 - INFO - 🚀 开始训练 - rml201801a Trial 51
2025-08-07 08:54:01,153 - INFO - ====================================================================================================
2025-08-07 08:54:01,153 - INFO - 📋 本次训练参数:
2025-08-07 08:54:01,153 - INFO -   dropout: 0.1344
  lambda_lifting: 0.0133
2025-08-07 08:54:01,153 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 08:54:01,153 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 08:54:01,154 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 08:54:01,154 - INFO -   #3 | Trial50 | 准确率:0.5688 | dropout=0.1274, lambda_lifting=0.0131
2025-08-07 08:54:01,154 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 08:54:01,154 - INFO - ====================================================================================================
2025-08-07 08:54:01,154 - INFO - 🎯 开始训练...
2025-08-07 08:54:01,154 - INFO - ====================================================================================================
2025-08-07 08:54:01,154 - INFO - 🎯 开始训练 rml201801a Trial 51 (第52次试验)
2025-08-07 08:54:01,154 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 08:54:01,201 - INFO - 开始训练，配置文件: /tmp/tmp6zyn7g58.yaml
2025-08-07 08:54:01,201 - INFO - 数据集类型: rml201801a
2025-08-07 08:54:01,201 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_085401
2025-08-07 08:54:01,212 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_085401/configs/config_backup.yaml
2025-08-07 08:54:01,212 - INFO - 使用设备: cuda
2025-08-07 08:54:01,212 - INFO - 加载数据...
2025-08-07 08:56:10,891 - INFO - 创建模型...
2025-08-07 08:56:10,937 - INFO - 可训练参数数量: 867,608
2025-08-07 08:56:10,937 - INFO - 总参数数量: 867,608
2025-08-07 08:56:10,937 - INFO - 可训练参数比例: 100.00%
2025-08-07 08:56:10,938 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 08:56:10,938 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 52, 'trial_number': 51}
2025-08-07 08:56:10,938 - INFO - 📊 最少训练轮数: 2
2025-08-07 08:56:10,938 - INFO - Epoch 1/400
2025-08-07 09:14:17,577 - INFO - Train Loss: 1.5469, Train Acc: 46.83%
2025-08-07 09:15:15,791 - INFO - Val Loss: 1.3775, Val Acc: 53.17%, Macro-F1: 53.23%, Kappa: 0.5114
2025-08-07 09:15:15,791 - INFO - Epoch 1 训练时间: 1144.85 秒
2025-08-07 09:15:15,854 - INFO - 保存最佳模型，验证准确率: 53.17%, Macro-F1: 53.23%, Kappa: 0.5114
2025-08-07 09:15:15,854 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_085401/models/best_model.pth
2025-08-07 09:15:15,854 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_085401/models/model_epoch_1_acc_53.17.pth
2025-08-07 09:15:15,854 - INFO - Epoch 2/400
2025-08-07 09:33:20,271 - INFO - Train Loss: 1.3012, Train Acc: 56.00%
2025-08-07 09:34:17,317 - INFO - Val Loss: 1.2795, Val Acc: 56.90%, Macro-F1: 59.51%, Kappa: 0.5503
2025-08-07 09:34:17,317 - INFO - Epoch 2 训练时间: 1141.46 秒
2025-08-07 09:34:17,317 - INFO - 🔍 第2轮门槛检查: 准确率 56.9020%, 门槛 57.4476
2025-08-07 09:34:17,317 - INFO - ⚠️ 第2轮准确率 56.9020% 未达到门槛 57.4476，提前结束训练
2025-08-07 09:34:17,318 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_085401/results/training_history.json
2025-08-07 09:34:17,318 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_085401/results/training_summary.json
2025-08-07 09:34:17,318 - INFO - 训练完成！
2025-08-07 09:34:17,318 - INFO - 最佳验证准确率: 53.17%
2025-08-07 09:34:17,318 - INFO - 最佳Macro-F1: 53.23%
2025-08-07 09:34:17,318 - INFO - 最佳Kappa: 0.5114
2025-08-07 09:34:17,318 - INFO - 总训练时间: 2286.32 秒 (38.1 分钟)
2025-08-07 09:34:17,318 - INFO - 平均每轮训练时间: 1143.16 秒
2025-08-07 09:34:17,318 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_085401
2025-08-07 09:34:17,318 - INFO - 🔍 第2轮准确率记录: 0.5690
2025-08-07 09:34:20,437 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 09:34:20,438 - INFO -    - 当前门槛: 57.4476
2025-08-07 09:34:20,438 - INFO -    - 第2轮准确率: 56.9020
2025-08-07 09:34:20,438 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 09:34:20,438 - INFO - 训练完成 rml201801a Trial 51, 结果: {'best_val_acc': 0.5317307692307692, 'best_val_f1': 0.5323349720497641, 'best_val_kappa': 0.511371237458194, 'total_epochs': 2, 'total_training_time': 2286.315858364105, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5690196998123828}
2025-08-07 09:34:20,438 - INFO - rml201801a Trial 51 早停训练，使用第2轮准确率: 0.5690
2025-08-07 09:34:20,442 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 09:34:20,442 - INFO - --------------------------------------------------------------------------------
2025-08-07 09:34:20,442 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 09:34:20,442 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 09:34:20,442 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 09:34:20,443 - INFO - --------------------------------------------------------------------------------
2025-08-07 09:34:20,774 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 09:34:20,774 - INFO -    - dropout: 0.12832195825255127 (数据集特定 + 通用)
2025-08-07 09:34:20,774 - INFO -    - lambda_lifting: 0.010156387825911283
2025-08-07 09:34:20,785 - INFO - 📄 临时配置文件创建: /tmp/tmpzm_on5qz.yaml
2025-08-07 09:34:20,785 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.12832195825255127, 'batch_size': 256}
2025-08-07 09:34:20,785 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.12832195825255127
2025-08-07 09:34:20,785 - INFO - ✅ 最终训练参数: lambda_lifting=0.010156387825911283, batch_size=128
2025-08-07 09:34:20,785 - INFO - rml201801a Trial 52: 参数 {'dropout': 0.12832195825255127, 'lambda_lifting': 0.010156387825911283}
2025-08-07 09:34:20,883 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 09:34:20,883 - INFO - ====================================================================================================
2025-08-07 09:34:20,883 - INFO - 🚀 开始训练 - rml201801a Trial 52
2025-08-07 09:34:20,883 - INFO - ====================================================================================================
2025-08-07 09:34:20,883 - INFO - 📋 本次训练参数:
2025-08-07 09:34:20,883 - INFO -   dropout: 0.1283
  lambda_lifting: 0.0102
2025-08-07 09:34:20,883 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 09:34:20,883 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 09:34:20,883 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 09:34:20,883 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 09:34:20,883 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 09:34:20,883 - INFO - ====================================================================================================
2025-08-07 09:34:20,883 - INFO - 🎯 开始训练...
2025-08-07 09:34:20,883 - INFO - ====================================================================================================
2025-08-07 09:34:20,883 - INFO - 🎯 开始训练 rml201801a Trial 52 (第53次试验)
2025-08-07 09:34:20,883 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 09:34:20,931 - INFO - 开始训练，配置文件: /tmp/tmpzm_on5qz.yaml
2025-08-07 09:34:20,931 - INFO - 数据集类型: rml201801a
2025-08-07 09:34:20,931 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_093420
2025-08-07 09:34:20,942 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_093420/configs/config_backup.yaml
2025-08-07 09:34:20,942 - INFO - 使用设备: cuda
2025-08-07 09:34:20,942 - INFO - 加载数据...
2025-08-07 09:36:59,472 - INFO - 创建模型...
2025-08-07 09:36:59,516 - INFO - 可训练参数数量: 867,608
2025-08-07 09:36:59,516 - INFO - 总参数数量: 867,608
2025-08-07 09:36:59,516 - INFO - 可训练参数比例: 100.00%
2025-08-07 09:36:59,517 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 09:36:59,517 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 53, 'trial_number': 52}
2025-08-07 09:36:59,517 - INFO - 📊 最少训练轮数: 2
2025-08-07 09:36:59,517 - INFO - Epoch 1/400
2025-08-07 09:55:05,837 - INFO - Train Loss: 1.5418, Train Acc: 46.96%
2025-08-07 09:56:04,288 - INFO - Val Loss: 1.3651, Val Acc: 53.30%, Macro-F1: 53.15%, Kappa: 0.5127
2025-08-07 09:56:04,288 - INFO - Epoch 1 训练时间: 1144.77 秒
2025-08-07 09:56:04,353 - INFO - 保存最佳模型，验证准确率: 53.30%, Macro-F1: 53.15%, Kappa: 0.5127
2025-08-07 09:56:04,353 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_093420/models/best_model.pth
2025-08-07 09:56:04,353 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_093420/models/model_epoch_1_acc_53.30.pth
2025-08-07 09:56:04,353 - INFO - Epoch 2/400
2025-08-07 10:14:10,169 - INFO - Train Loss: 1.3011, Train Acc: 55.96%
2025-08-07 10:15:07,409 - INFO - Val Loss: 1.3069, Val Acc: 55.90%, Macro-F1: 57.12%, Kappa: 0.5399
2025-08-07 10:15:07,409 - INFO - Epoch 2 训练时间: 1143.06 秒
2025-08-07 10:15:07,409 - INFO - 🔍 第2轮门槛检查: 准确率 55.9034%, 门槛 57.4476
2025-08-07 10:15:07,409 - INFO - ⚠️ 第2轮准确率 55.9034% 未达到门槛 57.4476，提前结束训练
2025-08-07 10:15:07,410 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_093420/results/training_history.json
2025-08-07 10:15:07,410 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_093420/results/training_summary.json
2025-08-07 10:15:07,410 - INFO - 训练完成！
2025-08-07 10:15:07,410 - INFO - 最佳验证准确率: 53.30%
2025-08-07 10:15:07,410 - INFO - 最佳Macro-F1: 53.15%
2025-08-07 10:15:07,410 - INFO - 最佳Kappa: 0.5127
2025-08-07 10:15:07,410 - INFO - 总训练时间: 2287.83 秒 (38.1 分钟)
2025-08-07 10:15:07,410 - INFO - 平均每轮训练时间: 1143.91 秒
2025-08-07 10:15:07,410 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_093420
2025-08-07 10:15:07,410 - INFO - 🔍 第2轮准确率记录: 0.5590
2025-08-07 10:15:10,197 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 10:15:10,197 - INFO -    - 当前门槛: 57.4476
2025-08-07 10:15:10,197 - INFO -    - 第2轮准确率: 55.9034
2025-08-07 10:15:10,197 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 10:15:10,197 - INFO - 训练完成 rml201801a Trial 52, 结果: {'best_val_acc': 0.5329711277882009, 'best_val_f1': 0.5315094289553303, 'best_val_kappa': 0.5126655246485575, 'total_epochs': 2, 'total_training_time': 2287.8273923397064, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5590342922659997}
2025-08-07 10:15:10,197 - INFO - rml201801a Trial 52 早停训练，使用第2轮准确率: 0.5590
2025-08-07 10:15:10,202 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 10:15:10,202 - INFO - --------------------------------------------------------------------------------
2025-08-07 10:15:10,202 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 10:15:10,202 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 10:15:10,202 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 10:15:10,202 - INFO - --------------------------------------------------------------------------------
2025-08-07 10:15:10,673 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 10:15:10,674 - INFO -    - dropout: 0.16010355602949888 (数据集特定 + 通用)
2025-08-07 10:15:10,674 - INFO -    - lambda_lifting: 0.04700537880914389
2025-08-07 10:15:10,684 - INFO - 📄 临时配置文件创建: /tmp/tmp_s1houv5.yaml
2025-08-07 10:15:10,684 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.16010355602949888, 'batch_size': 256}
2025-08-07 10:15:10,684 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.16010355602949888
2025-08-07 10:15:10,684 - INFO - ✅ 最终训练参数: lambda_lifting=0.04700537880914389, batch_size=128
2025-08-07 10:15:10,685 - INFO - rml201801a Trial 53: 参数 {'dropout': 0.16010355602949888, 'lambda_lifting': 0.04700537880914389}
2025-08-07 10:15:10,800 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 10:15:10,800 - INFO - ====================================================================================================
2025-08-07 10:15:10,800 - INFO - 🚀 开始训练 - rml201801a Trial 53
2025-08-07 10:15:10,800 - INFO - ====================================================================================================
2025-08-07 10:15:10,800 - INFO - 📋 本次训练参数:
2025-08-07 10:15:10,800 - INFO -   dropout: 0.1601
  lambda_lifting: 0.0470
2025-08-07 10:15:10,800 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 10:15:10,800 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 10:15:10,800 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 10:15:10,800 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 10:15:10,800 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 10:15:10,800 - INFO - ====================================================================================================
2025-08-07 10:15:10,800 - INFO - 🎯 开始训练...
2025-08-07 10:15:10,800 - INFO - ====================================================================================================
2025-08-07 10:15:10,800 - INFO - 🎯 开始训练 rml201801a Trial 53 (第54次试验)
2025-08-07 10:15:10,800 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 10:15:10,848 - INFO - 开始训练，配置文件: /tmp/tmp_s1houv5.yaml
2025-08-07 10:15:10,848 - INFO - 数据集类型: rml201801a
2025-08-07 10:15:10,848 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_101510
2025-08-07 10:15:10,859 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_101510/configs/config_backup.yaml
2025-08-07 10:15:10,859 - INFO - 使用设备: cuda
2025-08-07 10:15:10,859 - INFO - 加载数据...
2025-08-07 10:17:17,868 - INFO - 创建模型...
2025-08-07 10:17:17,909 - INFO - 可训练参数数量: 867,608
2025-08-07 10:17:17,909 - INFO - 总参数数量: 867,608
2025-08-07 10:17:17,909 - INFO - 可训练参数比例: 100.00%
2025-08-07 10:17:17,910 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 10:17:17,910 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 54, 'trial_number': 53}
2025-08-07 10:17:17,910 - INFO - 📊 最少训练轮数: 2
2025-08-07 10:17:17,910 - INFO - Epoch 1/400
2025-08-07 10:35:26,731 - INFO - Train Loss: 1.5828, Train Acc: 45.79%
2025-08-07 10:36:25,110 - INFO - Val Loss: 1.4143, Val Acc: 51.42%, Macro-F1: 52.97%, Kappa: 0.4931
2025-08-07 10:36:25,110 - INFO - Epoch 1 训练时间: 1147.20 秒
2025-08-07 10:36:25,171 - INFO - 保存最佳模型，验证准确率: 51.42%, Macro-F1: 52.97%, Kappa: 0.4931
2025-08-07 10:36:25,171 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_101510/models/best_model.pth
2025-08-07 10:36:25,171 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_101510/models/model_epoch_1_acc_51.42.pth
2025-08-07 10:36:25,171 - INFO - Epoch 2/400
2025-08-07 10:54:30,680 - INFO - Train Loss: 1.3280, Train Acc: 55.17%
2025-08-07 10:55:27,947 - INFO - Val Loss: 1.3125, Val Acc: 55.66%, Macro-F1: 56.62%, Kappa: 0.5373
2025-08-07 10:55:27,947 - INFO - Epoch 2 训练时间: 1142.78 秒
2025-08-07 10:55:27,947 - INFO - 🔍 第2轮门槛检查: 准确率 55.6551%, 门槛 57.4476
2025-08-07 10:55:27,947 - INFO - ⚠️ 第2轮准确率 55.6551% 未达到门槛 57.4476，提前结束训练
2025-08-07 10:55:27,948 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_101510/results/training_history.json
2025-08-07 10:55:27,948 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_101510/results/training_summary.json
2025-08-07 10:55:27,948 - INFO - 训练完成！
2025-08-07 10:55:27,948 - INFO - 最佳验证准确率: 51.42%
2025-08-07 10:55:27,948 - INFO - 最佳Macro-F1: 52.97%
2025-08-07 10:55:27,948 - INFO - 最佳Kappa: 0.4931
2025-08-07 10:55:27,948 - INFO - 总训练时间: 2289.98 秒 (38.2 分钟)
2025-08-07 10:55:27,948 - INFO - 平均每轮训练时间: 1144.99 秒
2025-08-07 10:55:27,948 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_101510
2025-08-07 10:55:27,948 - INFO - 🔍 第2轮准确率记录: 0.5566
2025-08-07 10:55:30,899 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 10:55:30,899 - INFO -    - 当前门槛: 57.4476
2025-08-07 10:55:30,899 - INFO -    - 第2轮准确率: 55.6551
2025-08-07 10:55:30,899 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 10:55:30,899 - INFO - 训练完成 rml201801a Trial 53, 结果: {'best_val_acc': 0.5141781321659371, 'best_val_f1': 0.529722432981968, 'best_val_kappa': 0.4930554422601082, 'total_epochs': 2, 'total_training_time': 2289.975611448288, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5565509693558474}
2025-08-07 10:55:30,899 - INFO - rml201801a Trial 53 早停训练，使用第2轮准确率: 0.5566
2025-08-07 10:55:30,904 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 10:55:30,904 - INFO - --------------------------------------------------------------------------------
2025-08-07 10:55:30,904 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 10:55:30,904 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 10:55:30,904 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 10:55:30,904 - INFO - --------------------------------------------------------------------------------
2025-08-07 10:55:31,218 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 10:55:31,219 - INFO -    - dropout: 0.12950093763627754 (数据集特定 + 通用)
2025-08-07 10:55:31,219 - INFO -    - lambda_lifting: 0.08561555545374508
2025-08-07 10:55:31,229 - INFO - 📄 临时配置文件创建: /tmp/tmpkj1gzb6v.yaml
2025-08-07 10:55:31,229 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.12950093763627754, 'batch_size': 256}
2025-08-07 10:55:31,229 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.12950093763627754
2025-08-07 10:55:31,229 - INFO - ✅ 最终训练参数: lambda_lifting=0.08561555545374508, batch_size=128
2025-08-07 10:55:31,229 - INFO - rml201801a Trial 54: 参数 {'dropout': 0.12950093763627754, 'lambda_lifting': 0.08561555545374508}
2025-08-07 10:55:31,322 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 10:55:31,322 - INFO - ====================================================================================================
2025-08-07 10:55:31,322 - INFO - 🚀 开始训练 - rml201801a Trial 54
2025-08-07 10:55:31,322 - INFO - ====================================================================================================
2025-08-07 10:55:31,322 - INFO - 📋 本次训练参数:
2025-08-07 10:55:31,323 - INFO -   dropout: 0.1295
  lambda_lifting: 0.0856
2025-08-07 10:55:31,323 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 10:55:31,323 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 10:55:31,323 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 10:55:31,323 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 10:55:31,323 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 10:55:31,323 - INFO - ====================================================================================================
2025-08-07 10:55:31,323 - INFO - 🎯 开始训练...
2025-08-07 10:55:31,323 - INFO - ====================================================================================================
2025-08-07 10:55:31,323 - INFO - 🎯 开始训练 rml201801a Trial 54 (第55次试验)
2025-08-07 10:55:31,323 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 10:55:31,370 - INFO - 开始训练，配置文件: /tmp/tmpkj1gzb6v.yaml
2025-08-07 10:55:31,370 - INFO - 数据集类型: rml201801a
2025-08-07 10:55:31,370 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_105531
2025-08-07 10:55:31,381 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_105531/configs/config_backup.yaml
2025-08-07 10:55:31,381 - INFO - 使用设备: cuda
2025-08-07 10:55:31,381 - INFO - 加载数据...
2025-08-07 10:57:36,990 - INFO - 创建模型...
2025-08-07 10:57:37,036 - INFO - 可训练参数数量: 867,608
2025-08-07 10:57:37,036 - INFO - 总参数数量: 867,608
2025-08-07 10:57:37,036 - INFO - 可训练参数比例: 100.00%
2025-08-07 10:57:37,037 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 10:57:37,037 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 55, 'trial_number': 54}
2025-08-07 10:57:37,038 - INFO - 📊 最少训练轮数: 2
2025-08-07 10:57:37,038 - INFO - Epoch 1/400
2025-08-07 11:15:44,778 - INFO - Train Loss: 1.5837, Train Acc: 45.90%
2025-08-07 11:16:42,733 - INFO - Val Loss: 1.3831, Val Acc: 52.53%, Macro-F1: 52.97%, Kappa: 0.5047
2025-08-07 11:16:42,734 - INFO - Epoch 1 训练时间: 1145.70 秒
2025-08-07 11:16:42,795 - INFO - 保存最佳模型，验证准确率: 52.53%, Macro-F1: 52.97%, Kappa: 0.5047
2025-08-07 11:16:42,795 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_105531/models/best_model.pth
2025-08-07 11:16:42,795 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_105531/models/model_epoch_1_acc_52.53.pth
2025-08-07 11:16:42,795 - INFO - Epoch 2/400
2025-08-07 11:34:47,585 - INFO - Train Loss: 1.3269, Train Acc: 55.17%
2025-08-07 11:35:44,643 - INFO - Val Loss: 1.2807, Val Acc: 56.63%, Macro-F1: 57.50%, Kappa: 0.5474
2025-08-07 11:35:44,643 - INFO - Epoch 2 训练时间: 1141.85 秒
2025-08-07 11:35:44,643 - INFO - 🔍 第2轮门槛检查: 准确率 56.6297%, 门槛 57.4476
2025-08-07 11:35:44,643 - INFO - ⚠️ 第2轮准确率 56.6297% 未达到门槛 57.4476，提前结束训练
2025-08-07 11:35:44,643 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_105531/results/training_history.json
2025-08-07 11:35:44,644 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_105531/results/training_summary.json
2025-08-07 11:35:44,644 - INFO - 训练完成！
2025-08-07 11:35:44,644 - INFO - 最佳验证准确率: 52.53%
2025-08-07 11:35:44,644 - INFO - 最佳Macro-F1: 52.97%
2025-08-07 11:35:44,644 - INFO - 最佳Kappa: 0.5047
2025-08-07 11:35:44,644 - INFO - 总训练时间: 2287.54 秒 (38.1 分钟)
2025-08-07 11:35:44,644 - INFO - 平均每轮训练时间: 1143.77 秒
2025-08-07 11:35:44,644 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_105531
2025-08-07 11:35:44,644 - INFO - 🔍 第2轮准确率记录: 0.5663
2025-08-07 11:35:47,570 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 11:35:47,571 - INFO -    - 当前门槛: 57.4476
2025-08-07 11:35:47,571 - INFO -    - 第2轮准确率: 56.6297
2025-08-07 11:35:47,571 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 11:35:47,571 - INFO - 训练完成 rml201801a Trial 54, 结果: {'best_val_acc': 0.5253491765686887, 'best_val_f1': 0.529667327343759, 'best_val_kappa': 0.5047121842455883, 'total_epochs': 2, 'total_training_time': 2287.5439813137054, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5662966437356681}
2025-08-07 11:35:47,571 - INFO - rml201801a Trial 54 早停训练，使用第2轮准确率: 0.5663
2025-08-07 11:35:47,576 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 11:35:47,576 - INFO - --------------------------------------------------------------------------------
2025-08-07 11:35:47,576 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 11:35:47,576 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 11:35:47,576 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 11:35:47,576 - INFO - --------------------------------------------------------------------------------
2025-08-07 11:35:47,866 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 11:35:47,866 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 35 次)
2025-08-07 11:35:47,894 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 11:35:47,894 - INFO -    - dropout: 0.15245658413941912 (数据集特定 + 通用)
2025-08-07 11:35:47,894 - INFO -    - lambda_lifting: 0.06662230450916695
2025-08-07 11:35:47,905 - INFO - 📄 临时配置文件创建: /tmp/tmpk2wu39j7.yaml
2025-08-07 11:35:47,905 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.15245658413941912, 'batch_size': 256}
2025-08-07 11:35:47,905 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.15245658413941912
2025-08-07 11:35:47,905 - INFO - ✅ 最终训练参数: lambda_lifting=0.06662230450916695, batch_size=128
2025-08-07 11:35:47,905 - INFO - rml201801a Trial 55: 参数 {'dropout': 0.15245658413941912, 'lambda_lifting': 0.06662230450916695}
2025-08-07 11:35:47,997 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 11:35:47,997 - INFO - ====================================================================================================
2025-08-07 11:35:47,997 - INFO - 🚀 开始训练 - rml201801a Trial 55
2025-08-07 11:35:47,997 - INFO - ====================================================================================================
2025-08-07 11:35:47,997 - INFO - 📋 本次训练参数:
2025-08-07 11:35:47,997 - INFO -   dropout: 0.1525
  lambda_lifting: 0.0666
2025-08-07 11:35:47,997 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 11:35:47,997 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 11:35:47,997 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 11:35:47,997 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 11:35:47,997 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 11:35:47,997 - INFO - ====================================================================================================
2025-08-07 11:35:47,997 - INFO - 🎯 开始训练...
2025-08-07 11:35:47,997 - INFO - ====================================================================================================
2025-08-07 11:35:47,997 - INFO - 🎯 开始训练 rml201801a Trial 55 (第56次试验)
2025-08-07 11:35:47,997 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 11:35:48,045 - INFO - 开始训练，配置文件: /tmp/tmpk2wu39j7.yaml
2025-08-07 11:35:48,045 - INFO - 数据集类型: rml201801a
2025-08-07 11:35:48,045 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_113548
2025-08-07 11:35:48,056 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_113548/configs/config_backup.yaml
2025-08-07 11:35:48,056 - INFO - 使用设备: cuda
2025-08-07 11:35:48,056 - INFO - 加载数据...
2025-08-07 11:37:52,169 - INFO - 创建模型...
2025-08-07 11:37:52,206 - INFO - 可训练参数数量: 867,608
2025-08-07 11:37:52,207 - INFO - 总参数数量: 867,608
2025-08-07 11:37:52,207 - INFO - 可训练参数比例: 100.00%
2025-08-07 11:37:52,208 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 11:37:52,208 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 56, 'trial_number': 55}
2025-08-07 11:37:52,208 - INFO - 📊 最少训练轮数: 2
2025-08-07 11:37:52,208 - INFO - Epoch 1/400
2025-08-07 11:56:00,664 - INFO - Train Loss: 1.5770, Train Acc: 46.07%
2025-08-07 11:56:59,045 - INFO - Val Loss: 1.3813, Val Acc: 52.43%, Macro-F1: 53.87%, Kappa: 0.5036
2025-08-07 11:56:59,045 - INFO - Epoch 1 训练时间: 1146.84 秒
2025-08-07 11:56:59,109 - INFO - 保存最佳模型，验证准确率: 52.43%, Macro-F1: 53.87%, Kappa: 0.5036
2025-08-07 11:56:59,110 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_113548/models/best_model.pth
2025-08-07 11:56:59,110 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_113548/models/model_epoch_1_acc_52.43.pth
2025-08-07 11:56:59,110 - INFO - Epoch 2/400
2025-08-07 12:15:03,824 - INFO - Train Loss: 1.3286, Train Acc: 55.14%
2025-08-07 12:16:01,013 - INFO - Val Loss: 1.2712, Val Acc: 56.88%, Macro-F1: 57.52%, Kappa: 0.5500
2025-08-07 12:16:01,013 - INFO - Epoch 2 训练时间: 1141.90 秒
2025-08-07 12:16:01,013 - INFO - 🔍 第2轮门槛检查: 准确率 56.8764%, 门槛 57.4476
2025-08-07 12:16:01,013 - INFO - ⚠️ 第2轮准确率 56.8764% 未达到门槛 57.4476，提前结束训练
2025-08-07 12:16:01,013 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_113548/results/training_history.json
2025-08-07 12:16:01,014 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_113548/results/training_summary.json
2025-08-07 12:16:01,014 - INFO - 训练完成！
2025-08-07 12:16:01,014 - INFO - 最佳验证准确率: 52.43%
2025-08-07 12:16:01,014 - INFO - 最佳Macro-F1: 53.87%
2025-08-07 12:16:01,014 - INFO - 最佳Kappa: 0.5036
2025-08-07 12:16:01,014 - INFO - 总训练时间: 2288.74 秒 (38.1 分钟)
2025-08-07 12:16:01,014 - INFO - 平均每轮训练时间: 1144.37 秒
2025-08-07 12:16:01,014 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_113548
2025-08-07 12:16:01,014 - INFO - 🔍 第2轮准确率记录: 0.5688
2025-08-07 12:16:04,023 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 12:16:04,024 - INFO -    - 当前门槛: 57.4476
2025-08-07 12:16:04,024 - INFO -    - 第2轮准确率: 56.8764
2025-08-07 12:16:04,024 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 12:16:04,024 - INFO - 训练完成 rml201801a Trial 55, 结果: {'best_val_acc': 0.5243303106107985, 'best_val_f1': 0.5386962243802463, 'best_val_kappa': 0.5036490197677896, 'total_epochs': 2, 'total_training_time': 2288.7407364845276, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.568764331874088}
2025-08-07 12:16:04,024 - INFO - rml201801a Trial 55 早停训练，使用第2轮准确率: 0.5688
2025-08-07 12:16:04,030 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 12:16:04,030 - INFO - --------------------------------------------------------------------------------
2025-08-07 12:16:04,030 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 12:16:04,030 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 12:16:04,030 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 12:16:04,030 - INFO - --------------------------------------------------------------------------------
2025-08-07 12:16:04,410 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 12:16:04,411 - INFO -    - dropout: 0.24220247315200666 (数据集特定 + 通用)
2025-08-07 12:16:04,411 - INFO -    - lambda_lifting: 0.06337371021563133
2025-08-07 12:16:04,422 - INFO - 📄 临时配置文件创建: /tmp/tmp36goajoe.yaml
2025-08-07 12:16:04,422 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.24220247315200666, 'batch_size': 256}
2025-08-07 12:16:04,422 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.24220247315200666
2025-08-07 12:16:04,422 - INFO - ✅ 最终训练参数: lambda_lifting=0.06337371021563133, batch_size=128
2025-08-07 12:16:04,422 - INFO - rml201801a Trial 56: 参数 {'dropout': 0.24220247315200666, 'lambda_lifting': 0.06337371021563133}
2025-08-07 12:16:04,551 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 12:16:04,551 - INFO - ====================================================================================================
2025-08-07 12:16:04,551 - INFO - 🚀 开始训练 - rml201801a Trial 56
2025-08-07 12:16:04,551 - INFO - ====================================================================================================
2025-08-07 12:16:04,551 - INFO - 📋 本次训练参数:
2025-08-07 12:16:04,551 - INFO -   dropout: 0.2422
  lambda_lifting: 0.0634
2025-08-07 12:16:04,551 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 12:16:04,551 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 12:16:04,551 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 12:16:04,551 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 12:16:04,552 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 12:16:04,552 - INFO - ====================================================================================================
2025-08-07 12:16:04,552 - INFO - 🎯 开始训练...
2025-08-07 12:16:04,552 - INFO - ====================================================================================================
2025-08-07 12:16:04,552 - INFO - 🎯 开始训练 rml201801a Trial 56 (第57次试验)
2025-08-07 12:16:04,552 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 12:16:04,603 - INFO - 开始训练，配置文件: /tmp/tmp36goajoe.yaml
2025-08-07 12:16:04,603 - INFO - 数据集类型: rml201801a
2025-08-07 12:16:04,603 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_121604
2025-08-07 12:16:04,615 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_121604/configs/config_backup.yaml
2025-08-07 12:16:04,615 - INFO - 使用设备: cuda
2025-08-07 12:16:04,615 - INFO - 加载数据...
2025-08-07 12:18:13,405 - INFO - 创建模型...
2025-08-07 12:18:13,460 - INFO - 可训练参数数量: 867,608
2025-08-07 12:18:13,460 - INFO - 总参数数量: 867,608
2025-08-07 12:18:13,460 - INFO - 可训练参数比例: 100.00%
2025-08-07 12:18:13,461 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 12:18:13,462 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 57, 'trial_number': 56}
2025-08-07 12:18:13,462 - INFO - 📊 最少训练轮数: 2
2025-08-07 12:18:13,462 - INFO - Epoch 1/400
2025-08-07 12:36:20,807 - INFO - Train Loss: 1.6219, Train Acc: 44.88%
2025-08-07 12:37:18,625 - INFO - Val Loss: 1.4344, Val Acc: 50.93%, Macro-F1: 50.52%, Kappa: 0.4880
2025-08-07 12:37:18,626 - INFO - Epoch 1 训练时间: 1145.16 秒
2025-08-07 12:37:18,688 - INFO - 保存最佳模型，验证准确率: 50.93%, Macro-F1: 50.52%, Kappa: 0.4880
2025-08-07 12:37:18,688 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_121604/models/best_model.pth
2025-08-07 12:37:18,688 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_121604/models/model_epoch_1_acc_50.93.pth
2025-08-07 12:37:18,688 - INFO - Epoch 2/400
2025-08-07 12:55:22,765 - INFO - Train Loss: 1.3757, Train Acc: 53.98%
2025-08-07 12:56:19,925 - INFO - Val Loss: 1.3401, Val Acc: 55.33%, Macro-F1: 56.94%, Kappa: 0.5339
2025-08-07 12:56:19,926 - INFO - Epoch 2 训练时间: 1141.24 秒
2025-08-07 12:56:19,926 - INFO - 🔍 第2轮门槛检查: 准确率 55.3289%, 门槛 57.4476
2025-08-07 12:56:19,926 - INFO - ⚠️ 第2轮准确率 55.3289% 未达到门槛 57.4476，提前结束训练
2025-08-07 12:56:19,926 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_121604/results/training_history.json
2025-08-07 12:56:19,926 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_121604/results/training_summary.json
2025-08-07 12:56:19,926 - INFO - 训练完成！
2025-08-07 12:56:19,926 - INFO - 最佳验证准确率: 50.93%
2025-08-07 12:56:19,927 - INFO - 最佳Macro-F1: 50.52%
2025-08-07 12:56:19,927 - INFO - 最佳Kappa: 0.4880
2025-08-07 12:56:19,927 - INFO - 总训练时间: 2286.40 秒 (38.1 分钟)
2025-08-07 12:56:19,927 - INFO - 平均每轮训练时间: 1143.20 秒
2025-08-07 12:56:19,927 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_121604
2025-08-07 12:56:19,927 - INFO - 🔍 第2轮准确率记录: 0.5533
2025-08-07 12:56:22,815 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 12:56:22,816 - INFO -    - 当前门槛: 57.4476
2025-08-07 12:56:22,816 - INFO -    - 第2轮准确率: 55.3289
2025-08-07 12:56:22,816 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 12:56:22,816 - INFO - 训练完成 rml201801a Trial 56, 结果: {'best_val_acc': 0.509331352928914, 'best_val_f1': 0.5051625663050324, 'best_val_kappa': 0.4879979334910406, 'total_epochs': 2, 'total_training_time': 2286.402074813843, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5532885136543674}
2025-08-07 12:56:22,816 - INFO - rml201801a Trial 56 早停训练，使用第2轮准确率: 0.5533
2025-08-07 12:56:22,821 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 12:56:22,821 - INFO - --------------------------------------------------------------------------------
2025-08-07 12:56:22,821 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 12:56:22,821 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 12:56:22,821 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 12:56:22,821 - INFO - --------------------------------------------------------------------------------
2025-08-07 12:56:23,172 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 12:56:23,172 - INFO -    - dropout: 0.5080267364561007 (数据集特定 + 通用)
2025-08-07 12:56:23,172 - INFO -    - lambda_lifting: 0.033403583141651615
2025-08-07 12:56:23,182 - INFO - 📄 临时配置文件创建: /tmp/tmp5300wswe.yaml
2025-08-07 12:56:23,183 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.5080267364561007, 'batch_size': 256}
2025-08-07 12:56:23,183 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.5080267364561007
2025-08-07 12:56:23,183 - INFO - ✅ 最终训练参数: lambda_lifting=0.033403583141651615, batch_size=128
2025-08-07 12:56:23,183 - INFO - rml201801a Trial 57: 参数 {'dropout': 0.5080267364561007, 'lambda_lifting': 0.033403583141651615}
2025-08-07 12:56:23,275 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 12:56:23,275 - INFO - ====================================================================================================
2025-08-07 12:56:23,275 - INFO - 🚀 开始训练 - rml201801a Trial 57
2025-08-07 12:56:23,275 - INFO - ====================================================================================================
2025-08-07 12:56:23,275 - INFO - 📋 本次训练参数:
2025-08-07 12:56:23,275 - INFO -   dropout: 0.5080
  lambda_lifting: 0.0334
2025-08-07 12:56:23,276 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 12:56:23,276 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 12:56:23,276 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 12:56:23,276 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 12:56:23,276 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 12:56:23,276 - INFO - ====================================================================================================
2025-08-07 12:56:23,276 - INFO - 🎯 开始训练...
2025-08-07 12:56:23,276 - INFO - ====================================================================================================
2025-08-07 12:56:23,276 - INFO - 🎯 开始训练 rml201801a Trial 57 (第58次试验)
2025-08-07 12:56:23,276 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 12:56:23,324 - INFO - 开始训练，配置文件: /tmp/tmp5300wswe.yaml
2025-08-07 12:56:23,324 - INFO - 数据集类型: rml201801a
2025-08-07 12:56:23,324 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_125623
2025-08-07 12:56:23,334 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_125623/configs/config_backup.yaml
2025-08-07 12:56:23,334 - INFO - 使用设备: cuda
2025-08-07 12:56:23,335 - INFO - 加载数据...
2025-08-07 12:58:34,271 - INFO - 创建模型...
2025-08-07 12:58:34,311 - INFO - 可训练参数数量: 867,608
2025-08-07 12:58:34,311 - INFO - 总参数数量: 867,608
2025-08-07 12:58:34,312 - INFO - 可训练参数比例: 100.00%
2025-08-07 12:58:34,313 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 12:58:34,314 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 58, 'trial_number': 57}
2025-08-07 12:58:34,314 - INFO - 📊 最少训练轮数: 2
2025-08-07 12:58:34,314 - INFO - Epoch 1/400
2025-08-07 13:16:43,525 - INFO - Train Loss: 1.6832, Train Acc: 42.51%
2025-08-07 13:17:41,690 - INFO - Val Loss: 1.5435, Val Acc: 46.48%, Macro-F1: 46.42%, Kappa: 0.4415
2025-08-07 13:17:41,690 - INFO - Epoch 1 训练时间: 1147.38 秒
2025-08-07 13:17:41,756 - INFO - 保存最佳模型，验证准确率: 46.48%, Macro-F1: 46.42%, Kappa: 0.4415
2025-08-07 13:17:41,756 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_125623/models/best_model.pth
2025-08-07 13:17:41,756 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_125623/models/model_epoch_1_acc_46.48.pth
2025-08-07 13:17:41,756 - INFO - Epoch 2/400
2025-08-07 13:35:47,053 - INFO - Train Loss: 1.4439, Train Acc: 50.88%
2025-08-07 13:36:44,082 - INFO - Val Loss: 1.4360, Val Acc: 51.62%, Macro-F1: 52.04%, Kappa: 0.4952
2025-08-07 13:36:44,082 - INFO - Epoch 2 训练时间: 1142.33 秒
2025-08-07 13:36:44,082 - INFO - 🔍 第2轮门槛检查: 准确率 51.6231%, 门槛 57.4476
2025-08-07 13:36:44,082 - INFO - ⚠️ 第2轮准确率 51.6231% 未达到门槛 57.4476，提前结束训练
2025-08-07 13:36:44,083 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_125623/results/training_history.json
2025-08-07 13:36:44,083 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_125623/results/training_summary.json
2025-08-07 13:36:44,083 - INFO - 训练完成！
2025-08-07 13:36:44,083 - INFO - 最佳验证准确率: 46.48%
2025-08-07 13:36:44,083 - INFO - 最佳Macro-F1: 46.42%
2025-08-07 13:36:44,083 - INFO - 最佳Kappa: 0.4415
2025-08-07 13:36:44,083 - INFO - 总训练时间: 2289.70 秒 (38.2 分钟)
2025-08-07 13:36:44,083 - INFO - 平均每轮训练时间: 1144.85 秒
2025-08-07 13:36:44,083 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_125623
2025-08-07 13:36:44,083 - INFO - 🔍 第2轮准确率记录: 0.5162
2025-08-07 13:36:46,962 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 13:36:46,962 - INFO -    - 当前门槛: 57.4476
2025-08-07 13:36:46,962 - INFO -    - 第2轮准确率: 51.6231
2025-08-07 13:36:46,962 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 13:36:46,963 - INFO - 训练完成 rml201801a Trial 57, 结果: {'best_val_acc': 0.4647826766729206, 'best_val_f1': 0.4641506051441214, 'best_val_kappa': 0.44151235826739543, 'total_epochs': 2, 'total_training_time': 2289.7030005455017, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5162314988534501}
2025-08-07 13:36:46,963 - INFO - rml201801a Trial 57 早停训练，使用第2轮准确率: 0.5162
2025-08-07 13:36:46,967 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 13:36:46,968 - INFO - --------------------------------------------------------------------------------
2025-08-07 13:36:46,968 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 13:36:46,968 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 13:36:46,968 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 13:36:46,968 - INFO - --------------------------------------------------------------------------------
2025-08-07 13:36:47,278 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 13:36:47,278 - INFO -    - dropout: 0.15763994712200607 (数据集特定 + 通用)
2025-08-07 13:36:47,278 - INFO -    - lambda_lifting: 0.3945247298444988
2025-08-07 13:36:47,292 - INFO - 📄 临时配置文件创建: /tmp/tmpu3gvv4lo.yaml
2025-08-07 13:36:47,292 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.15763994712200607, 'batch_size': 256}
2025-08-07 13:36:47,292 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.15763994712200607
2025-08-07 13:36:47,292 - INFO - ✅ 最终训练参数: lambda_lifting=0.3945247298444988, batch_size=128
2025-08-07 13:36:47,292 - INFO - rml201801a Trial 58: 参数 {'dropout': 0.15763994712200607, 'lambda_lifting': 0.3945247298444988}
2025-08-07 13:36:47,400 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 13:36:47,400 - INFO - ====================================================================================================
2025-08-07 13:36:47,400 - INFO - 🚀 开始训练 - rml201801a Trial 58
2025-08-07 13:36:47,400 - INFO - ====================================================================================================
2025-08-07 13:36:47,400 - INFO - 📋 本次训练参数:
2025-08-07 13:36:47,400 - INFO -   dropout: 0.1576
  lambda_lifting: 0.3945
2025-08-07 13:36:47,400 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 13:36:47,400 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 13:36:47,400 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 13:36:47,400 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 13:36:47,400 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 13:36:47,400 - INFO - ====================================================================================================
2025-08-07 13:36:47,400 - INFO - 🎯 开始训练...
2025-08-07 13:36:47,401 - INFO - ====================================================================================================
2025-08-07 13:36:47,401 - INFO - 🎯 开始训练 rml201801a Trial 58 (第59次试验)
2025-08-07 13:36:47,401 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 13:36:47,467 - INFO - 开始训练，配置文件: /tmp/tmpu3gvv4lo.yaml
2025-08-07 13:36:47,467 - INFO - 数据集类型: rml201801a
2025-08-07 13:36:47,467 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_133647
2025-08-07 13:36:47,482 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_133647/configs/config_backup.yaml
2025-08-07 13:36:47,482 - INFO - 使用设备: cuda
2025-08-07 13:36:47,482 - INFO - 加载数据...
2025-08-07 13:38:52,895 - INFO - 创建模型...
2025-08-07 13:38:52,952 - INFO - 可训练参数数量: 867,608
2025-08-07 13:38:52,953 - INFO - 总参数数量: 867,608
2025-08-07 13:38:52,953 - INFO - 可训练参数比例: 100.00%
2025-08-07 13:38:52,954 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 13:38:52,954 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 59, 'trial_number': 58}
2025-08-07 13:38:52,954 - INFO - 📊 最少训练轮数: 2
2025-08-07 13:38:52,954 - INFO - Epoch 1/400
2025-08-07 13:57:01,950 - INFO - Train Loss: 1.6839, Train Acc: 43.36%
2025-08-07 13:57:59,927 - INFO - Val Loss: 1.4400, Val Acc: 50.43%, Macro-F1: 50.61%, Kappa: 0.4827
2025-08-07 13:57:59,927 - INFO - Epoch 1 训练时间: 1146.97 秒
2025-08-07 13:57:59,990 - INFO - 保存最佳模型，验证准确率: 50.43%, Macro-F1: 50.61%, Kappa: 0.4827
2025-08-07 13:57:59,990 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_133647/models/best_model.pth
2025-08-07 13:57:59,990 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_133647/models/model_epoch_1_acc_50.43.pth
2025-08-07 13:57:59,990 - INFO - Epoch 2/400
2025-08-07 14:16:06,315 - INFO - Train Loss: 1.4066, Train Acc: 52.58%
2025-08-07 14:17:03,362 - INFO - Val Loss: 1.4698, Val Acc: 50.50%, Macro-F1: 51.69%, Kappa: 0.4835
2025-08-07 14:17:03,363 - INFO - Epoch 2 训练时间: 1143.37 秒
2025-08-07 14:17:03,363 - INFO - 🔍 第2轮门槛检查: 准确率 50.5016%, 门槛 57.4476
2025-08-07 14:17:03,363 - INFO - ⚠️ 第2轮准确率 50.5016% 未达到门槛 57.4476，提前结束训练
2025-08-07 14:17:03,363 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_133647/results/training_history.json
2025-08-07 14:17:03,363 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_133647/results/training_summary.json
2025-08-07 14:17:03,364 - INFO - 训练完成！
2025-08-07 14:17:03,364 - INFO - 最佳验证准确率: 50.43%
2025-08-07 14:17:03,364 - INFO - 最佳Macro-F1: 50.61%
2025-08-07 14:17:03,364 - INFO - 最佳Kappa: 0.4827
2025-08-07 14:17:03,364 - INFO - 总训练时间: 2290.35 秒 (38.2 分钟)
2025-08-07 14:17:03,364 - INFO - 平均每轮训练时间: 1145.17 秒
2025-08-07 14:17:03,364 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_133647
2025-08-07 14:17:03,364 - INFO - 🔍 第2轮准确率记录: 0.5050
2025-08-07 14:17:05,806 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 14:17:05,807 - INFO -    - 当前门槛: 57.4476
2025-08-07 14:17:05,807 - INFO -    - 第2轮准确率: 50.5016
2025-08-07 14:17:05,807 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 14:17:05,807 - INFO - 训练完成 rml201801a Trial 58, 结果: {'best_val_acc': 0.5042787158640817, 'best_val_f1': 0.5060974655006388, 'best_val_kappa': 0.4827256165538244, 'total_epochs': 2, 'total_training_time': 2290.346328496933, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5050161559307901}
2025-08-07 14:17:05,807 - INFO - rml201801a Trial 58 早停训练，使用第2轮准确率: 0.5050
2025-08-07 14:17:05,812 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 14:17:05,812 - INFO - --------------------------------------------------------------------------------
2025-08-07 14:17:05,812 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 14:17:05,812 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 14:17:05,812 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 14:17:05,812 - INFO - --------------------------------------------------------------------------------
2025-08-07 14:17:06,149 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 14:17:06,149 - INFO -    - dropout: 0.6424573818865047 (数据集特定 + 通用)
2025-08-07 14:17:06,149 - INFO -    - lambda_lifting: 0.4421293090794793
2025-08-07 14:17:06,163 - INFO - 📄 临时配置文件创建: /tmp/tmp9i9b_jbi.yaml
2025-08-07 14:17:06,163 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.6424573818865047, 'batch_size': 256}
2025-08-07 14:17:06,163 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.6424573818865047
2025-08-07 14:17:06,163 - INFO - ✅ 最终训练参数: lambda_lifting=0.4421293090794793, batch_size=128
2025-08-07 14:17:06,163 - INFO - rml201801a Trial 59: 参数 {'dropout': 0.6424573818865047, 'lambda_lifting': 0.4421293090794793}
2025-08-07 14:17:06,275 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 14:17:06,275 - INFO - ====================================================================================================
2025-08-07 14:17:06,275 - INFO - 🚀 开始训练 - rml201801a Trial 59
2025-08-07 14:17:06,275 - INFO - ====================================================================================================
2025-08-07 14:17:06,275 - INFO - 📋 本次训练参数:
2025-08-07 14:17:06,275 - INFO -   dropout: 0.6425
  lambda_lifting: 0.4421
2025-08-07 14:17:06,275 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 14:17:06,275 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 14:17:06,275 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 14:17:06,275 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 14:17:06,276 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 14:17:06,276 - INFO - ====================================================================================================
2025-08-07 14:17:06,276 - INFO - 🎯 开始训练...
2025-08-07 14:17:06,276 - INFO - ====================================================================================================
2025-08-07 14:17:06,276 - INFO - 🎯 开始训练 rml201801a Trial 59 (第60次试验)
2025-08-07 14:17:06,276 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 14:17:06,342 - INFO - 开始训练，配置文件: /tmp/tmp9i9b_jbi.yaml
2025-08-07 14:17:06,342 - INFO - 数据集类型: rml201801a
2025-08-07 14:17:06,342 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_141706
2025-08-07 14:17:06,357 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_141706/configs/config_backup.yaml
2025-08-07 14:17:06,357 - INFO - 使用设备: cuda
2025-08-07 14:17:06,357 - INFO - 加载数据...
2025-08-07 14:19:11,197 - INFO - 创建模型...
2025-08-07 14:19:11,261 - INFO - 可训练参数数量: 867,608
2025-08-07 14:19:11,261 - INFO - 总参数数量: 867,608
2025-08-07 14:19:11,261 - INFO - 可训练参数比例: 100.00%
2025-08-07 14:19:11,262 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 14:19:11,262 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 60, 'trial_number': 59}
2025-08-07 14:19:11,262 - INFO - 📊 最少训练轮数: 2
2025-08-07 14:19:11,262 - INFO - Epoch 1/400
2025-08-07 14:37:20,270 - INFO - Train Loss: 1.8113, Train Acc: 38.84%
2025-08-07 14:38:18,287 - INFO - Val Loss: 1.7846, Val Acc: 41.57%, Macro-F1: 39.30%, Kappa: 0.3903
2025-08-07 14:38:18,288 - INFO - Epoch 1 训练时间: 1147.03 秒
2025-08-07 14:38:18,363 - INFO - 保存最佳模型，验证准确率: 41.57%, Macro-F1: 39.30%, Kappa: 0.3903
2025-08-07 14:38:18,363 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_141706/models/best_model.pth
2025-08-07 14:38:18,363 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_141706/models/model_epoch_1_acc_41.57.pth
2025-08-07 14:38:18,363 - INFO - Epoch 2/400
2025-08-07 14:56:23,373 - INFO - Train Loss: 1.5170, Train Acc: 48.35%
2025-08-07 14:57:20,356 - INFO - Val Loss: 1.5732, Val Acc: 46.51%, Macro-F1: 46.90%, Kappa: 0.4418
2025-08-07 14:57:20,356 - INFO - Epoch 2 训练时间: 1141.99 秒
2025-08-07 14:57:20,356 - INFO - 🔍 第2轮门槛检查: 准确率 46.5085%, 门槛 57.4476
2025-08-07 14:57:20,356 - INFO - ⚠️ 第2轮准确率 46.5085% 未达到门槛 57.4476，提前结束训练
2025-08-07 14:57:20,357 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_141706/results/training_history.json
2025-08-07 14:57:20,357 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_141706/results/training_summary.json
2025-08-07 14:57:20,357 - INFO - 训练完成！
2025-08-07 14:57:20,357 - INFO - 最佳验证准确率: 41.57%
2025-08-07 14:57:20,357 - INFO - 最佳Macro-F1: 39.30%
2025-08-07 14:57:20,357 - INFO - 最佳Kappa: 0.3903
2025-08-07 14:57:20,357 - INFO - 总训练时间: 2289.02 秒 (38.2 分钟)
2025-08-07 14:57:20,357 - INFO - 平均每轮训练时间: 1144.51 秒
2025-08-07 14:57:20,357 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_141706
2025-08-07 14:57:20,357 - INFO - 🔍 第2轮准确率记录: 0.4651
2025-08-07 14:57:22,727 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 14:57:22,727 - INFO -    - 当前门槛: 57.4476
2025-08-07 14:57:22,727 - INFO -    - 第2轮准确率: 46.5085
2025-08-07 14:57:22,727 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 14:57:22,728 - INFO - 训练完成 rml201801a Trial 59, 结果: {'best_val_acc': 0.4157363977485929, 'best_val_f1': 0.39302566431522257, 'best_val_kappa': 0.3903336324333143, 'total_epochs': 2, 'total_training_time': 2289.018721342087, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.46508494892641233}
2025-08-07 14:57:22,728 - INFO - rml201801a Trial 59 早停训练，使用第2轮准确率: 0.4651
2025-08-07 14:57:22,733 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 14:57:22,733 - INFO - --------------------------------------------------------------------------------
2025-08-07 14:57:22,733 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 14:57:22,733 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 14:57:22,733 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 14:57:22,733 - INFO - --------------------------------------------------------------------------------
2025-08-07 14:57:23,019 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 14:57:23,019 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 40 次)
2025-08-07 14:57:23,047 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 14:57:23,047 - INFO -    - dropout: 0.20410027800044608 (数据集特定 + 通用)
2025-08-07 14:57:23,047 - INFO -    - lambda_lifting: 0.5839307884178822
2025-08-07 14:57:23,058 - INFO - 📄 临时配置文件创建: /tmp/tmpszj4wnkd.yaml
2025-08-07 14:57:23,058 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.20410027800044608, 'batch_size': 256}
2025-08-07 14:57:23,058 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.20410027800044608
2025-08-07 14:57:23,058 - INFO - ✅ 最终训练参数: lambda_lifting=0.5839307884178822, batch_size=128
2025-08-07 14:57:23,058 - INFO - rml201801a Trial 60: 参数 {'dropout': 0.20410027800044608, 'lambda_lifting': 0.5839307884178822}
2025-08-07 14:57:23,157 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 14:57:23,157 - INFO - ====================================================================================================
2025-08-07 14:57:23,157 - INFO - 🚀 开始训练 - rml201801a Trial 60
2025-08-07 14:57:23,157 - INFO - ====================================================================================================
2025-08-07 14:57:23,157 - INFO - 📋 本次训练参数:
2025-08-07 14:57:23,157 - INFO -   dropout: 0.2041
  lambda_lifting: 0.5839
2025-08-07 14:57:23,157 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 14:57:23,157 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 14:57:23,157 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 14:57:23,157 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 14:57:23,157 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 14:57:23,157 - INFO - ====================================================================================================
2025-08-07 14:57:23,157 - INFO - 🎯 开始训练...
2025-08-07 14:57:23,157 - INFO - ====================================================================================================
2025-08-07 14:57:23,157 - INFO - 🎯 开始训练 rml201801a Trial 60 (第61次试验)
2025-08-07 14:57:23,157 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 14:57:23,205 - INFO - 开始训练，配置文件: /tmp/tmpszj4wnkd.yaml
2025-08-07 14:57:23,205 - INFO - 数据集类型: rml201801a
2025-08-07 14:57:23,205 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_145723
2025-08-07 14:57:23,216 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_145723/configs/config_backup.yaml
2025-08-07 14:57:23,216 - INFO - 使用设备: cuda
2025-08-07 14:57:23,216 - INFO - 加载数据...
2025-08-07 14:59:31,392 - INFO - 创建模型...
2025-08-07 14:59:31,453 - INFO - 可训练参数数量: 867,608
2025-08-07 14:59:31,453 - INFO - 总参数数量: 867,608
2025-08-07 14:59:31,453 - INFO - 可训练参数比例: 100.00%
2025-08-07 14:59:31,454 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 14:59:31,454 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 61, 'trial_number': 60}
2025-08-07 14:59:31,454 - INFO - 📊 最少训练轮数: 2
2025-08-07 14:59:31,454 - INFO - Epoch 1/400
2025-08-07 15:17:40,936 - INFO - Train Loss: 1.7050, Train Acc: 43.18%
2025-08-07 15:18:38,872 - INFO - Val Loss: 1.4512, Val Acc: 49.87%, Macro-F1: 49.59%, Kappa: 0.4769
2025-08-07 15:18:38,873 - INFO - Epoch 1 训练时间: 1147.42 秒
2025-08-07 15:18:38,934 - INFO - 保存最佳模型，验证准确率: 49.87%, Macro-F1: 49.59%, Kappa: 0.4769
2025-08-07 15:18:38,934 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_145723/models/best_model.pth
2025-08-07 15:18:38,934 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_145723/models/model_epoch_1_acc_49.87.pth
2025-08-07 15:18:38,934 - INFO - Epoch 2/400
2025-08-07 15:36:45,146 - INFO - Train Loss: 1.4080, Train Acc: 52.55%
2025-08-07 15:37:42,208 - INFO - Val Loss: 1.3715, Val Acc: 53.16%, Macro-F1: 54.65%, Kappa: 0.5112
2025-08-07 15:37:42,209 - INFO - Epoch 2 训练时间: 1143.27 秒
2025-08-07 15:37:42,209 - INFO - 🔍 第2轮门槛检查: 准确率 53.1554%, 门槛 57.4476
2025-08-07 15:37:42,209 - INFO - ⚠️ 第2轮准确率 53.1554% 未达到门槛 57.4476，提前结束训练
2025-08-07 15:37:42,209 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_145723/results/training_history.json
2025-08-07 15:37:42,209 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_145723/results/training_summary.json
2025-08-07 15:37:42,209 - INFO - 训练完成！
2025-08-07 15:37:42,209 - INFO - 最佳验证准确率: 49.87%
2025-08-07 15:37:42,209 - INFO - 最佳Macro-F1: 49.59%
2025-08-07 15:37:42,209 - INFO - 最佳Kappa: 0.4769
2025-08-07 15:37:42,210 - INFO - 总训练时间: 2290.69 秒 (38.2 分钟)
2025-08-07 15:37:42,210 - INFO - 平均每轮训练时间: 1145.35 秒
2025-08-07 15:37:42,210 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_145723
2025-08-07 15:37:42,210 - INFO - 🔍 第2轮准确率记录: 0.5316
2025-08-07 15:37:44,758 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 15:37:44,759 - INFO -    - 当前门槛: 57.4476
2025-08-07 15:37:44,759 - INFO -    - 第2轮准确率: 53.1554
2025-08-07 15:37:44,759 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 15:37:44,759 - INFO - 训练完成 rml201801a Trial 60, 结果: {'best_val_acc': 0.49873358348968105, 'best_val_f1': 0.49594973430660955, 'best_val_kappa': 0.4769393914674933, 'total_epochs': 2, 'total_training_time': 2290.6929161548615, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5315535751511361}
2025-08-07 15:37:44,759 - INFO - rml201801a Trial 60 早停训练，使用第2轮准确率: 0.5316
2025-08-07 15:37:44,764 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 15:37:44,764 - INFO - --------------------------------------------------------------------------------
2025-08-07 15:37:44,764 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 15:37:44,764 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 15:37:44,764 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 15:37:44,764 - INFO - --------------------------------------------------------------------------------
2025-08-07 15:37:45,081 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 15:37:45,081 - INFO -    - dropout: 0.12368540961529925 (数据集特定 + 通用)
2025-08-07 15:37:45,081 - INFO -    - lambda_lifting: 0.11572935460085382
2025-08-07 15:37:45,092 - INFO - 📄 临时配置文件创建: /tmp/tmp20oc8vku.yaml
2025-08-07 15:37:45,092 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.12368540961529925, 'batch_size': 256}
2025-08-07 15:37:45,092 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.12368540961529925
2025-08-07 15:37:45,092 - INFO - ✅ 最终训练参数: lambda_lifting=0.11572935460085382, batch_size=128
2025-08-07 15:37:45,092 - INFO - rml201801a Trial 61: 参数 {'dropout': 0.12368540961529925, 'lambda_lifting': 0.11572935460085382}
2025-08-07 15:37:45,196 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 15:37:45,196 - INFO - ====================================================================================================
2025-08-07 15:37:45,196 - INFO - 🚀 开始训练 - rml201801a Trial 61
2025-08-07 15:37:45,196 - INFO - ====================================================================================================
2025-08-07 15:37:45,196 - INFO - 📋 本次训练参数:
2025-08-07 15:37:45,196 - INFO -   dropout: 0.1237
  lambda_lifting: 0.1157
2025-08-07 15:37:45,196 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 15:37:45,196 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 15:37:45,196 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 15:37:45,196 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 15:37:45,196 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 15:37:45,196 - INFO - ====================================================================================================
2025-08-07 15:37:45,196 - INFO - 🎯 开始训练...
2025-08-07 15:37:45,196 - INFO - ====================================================================================================
2025-08-07 15:37:45,197 - INFO - 🎯 开始训练 rml201801a Trial 61 (第62次试验)
2025-08-07 15:37:45,197 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 15:37:45,244 - INFO - 开始训练，配置文件: /tmp/tmp20oc8vku.yaml
2025-08-07 15:37:45,244 - INFO - 数据集类型: rml201801a
2025-08-07 15:37:45,244 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_153745
2025-08-07 15:37:45,255 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_153745/configs/config_backup.yaml
2025-08-07 15:37:45,255 - INFO - 使用设备: cuda
2025-08-07 15:37:45,255 - INFO - 加载数据...
2025-08-07 15:39:49,672 - INFO - 创建模型...
2025-08-07 15:39:49,736 - INFO - 可训练参数数量: 867,608
2025-08-07 15:39:49,736 - INFO - 总参数数量: 867,608
2025-08-07 15:39:49,736 - INFO - 可训练参数比例: 100.00%
2025-08-07 15:39:49,737 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 15:39:49,737 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 62, 'trial_number': 61}
2025-08-07 15:39:49,737 - INFO - 📊 最少训练轮数: 2
2025-08-07 15:39:49,737 - INFO - Epoch 1/400
2025-08-07 15:57:58,985 - INFO - Train Loss: 1.5906, Train Acc: 45.81%
2025-08-07 15:58:56,970 - INFO - Val Loss: 1.4023, Val Acc: 52.18%, Macro-F1: 52.51%, Kappa: 0.5010
2025-08-07 15:58:56,971 - INFO - Epoch 1 训练时间: 1147.23 秒
2025-08-07 15:58:57,031 - INFO - 保存最佳模型，验证准确率: 52.18%, Macro-F1: 52.51%, Kappa: 0.5010
2025-08-07 15:58:57,031 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_153745/models/best_model.pth
2025-08-07 15:58:57,031 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_153745/models/model_epoch_1_acc_52.18.pth
2025-08-07 15:58:57,032 - INFO - Epoch 2/400
2025-08-07 16:17:03,522 - INFO - Train Loss: 1.3348, Train Acc: 55.25%
2025-08-07 16:18:00,587 - INFO - Val Loss: 1.3025, Val Acc: 56.21%, Macro-F1: 57.10%, Kappa: 0.5431
2025-08-07 16:18:00,587 - INFO - Epoch 2 训练时间: 1143.56 秒
2025-08-07 16:18:00,587 - INFO - 🔍 第2轮门槛检查: 准确率 56.2109%, 门槛 57.4476
2025-08-07 16:18:00,587 - INFO - ⚠️ 第2轮准确率 56.2109% 未达到门槛 57.4476，提前结束训练
2025-08-07 16:18:00,588 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_153745/results/training_history.json
2025-08-07 16:18:00,588 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_153745/results/training_summary.json
2025-08-07 16:18:00,588 - INFO - 训练完成！
2025-08-07 16:18:00,588 - INFO - 最佳验证准确率: 52.18%
2025-08-07 16:18:00,588 - INFO - 最佳Macro-F1: 52.51%
2025-08-07 16:18:00,588 - INFO - 最佳Kappa: 0.5010
2025-08-07 16:18:00,588 - INFO - 总训练时间: 2290.79 秒 (38.2 分钟)
2025-08-07 16:18:00,588 - INFO - 平均每轮训练时间: 1145.39 秒
2025-08-07 16:18:00,588 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_153745
2025-08-07 16:18:00,588 - INFO - 🔍 第2轮准确率记录: 0.5621
2025-08-07 16:18:02,863 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 16:18:02,863 - INFO -    - 当前门槛: 57.4476
2025-08-07 16:18:02,863 - INFO -    - 第2轮准确率: 56.2109
2025-08-07 16:18:02,863 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 16:18:02,863 - INFO - 训练完成 rml201801a Trial 61, 结果: {'best_val_acc': 0.5217583906608297, 'best_val_f1': 0.5251397764226639, 'best_val_kappa': 0.5009652772113005, 'total_epochs': 2, 'total_training_time': 2290.788955926895, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5621091307066917}
2025-08-07 16:18:02,863 - INFO - rml201801a Trial 61 早停训练，使用第2轮准确率: 0.5621
2025-08-07 16:18:02,869 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 16:18:02,869 - INFO - --------------------------------------------------------------------------------
2025-08-07 16:18:02,869 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 16:18:02,869 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 16:18:02,869 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 16:18:02,869 - INFO - --------------------------------------------------------------------------------
2025-08-07 16:18:03,273 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 16:18:03,274 - INFO -    - dropout: 0.15441035755564353 (数据集特定 + 通用)
2025-08-07 16:18:03,274 - INFO -    - lambda_lifting: 0.01049470085955723
2025-08-07 16:18:03,284 - INFO - 📄 临时配置文件创建: /tmp/tmp1_a8psv8.yaml
2025-08-07 16:18:03,284 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.15441035755564353, 'batch_size': 256}
2025-08-07 16:18:03,284 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.15441035755564353
2025-08-07 16:18:03,284 - INFO - ✅ 最终训练参数: lambda_lifting=0.01049470085955723, batch_size=128
2025-08-07 16:18:03,285 - INFO - rml201801a Trial 62: 参数 {'dropout': 0.15441035755564353, 'lambda_lifting': 0.01049470085955723}
2025-08-07 16:18:03,383 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 16:18:03,383 - INFO - ====================================================================================================
2025-08-07 16:18:03,383 - INFO - 🚀 开始训练 - rml201801a Trial 62
2025-08-07 16:18:03,384 - INFO - ====================================================================================================
2025-08-07 16:18:03,384 - INFO - 📋 本次训练参数:
2025-08-07 16:18:03,384 - INFO -   dropout: 0.1544
  lambda_lifting: 0.0105
2025-08-07 16:18:03,384 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 16:18:03,384 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 16:18:03,384 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 16:18:03,384 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 16:18:03,384 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 16:18:03,384 - INFO - ====================================================================================================
2025-08-07 16:18:03,384 - INFO - 🎯 开始训练...
2025-08-07 16:18:03,384 - INFO - ====================================================================================================
2025-08-07 16:18:03,384 - INFO - 🎯 开始训练 rml201801a Trial 62 (第63次试验)
2025-08-07 16:18:03,384 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 16:18:03,431 - INFO - 开始训练，配置文件: /tmp/tmp1_a8psv8.yaml
2025-08-07 16:18:03,431 - INFO - 数据集类型: rml201801a
2025-08-07 16:18:03,431 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_161803
2025-08-07 16:18:03,442 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_161803/configs/config_backup.yaml
2025-08-07 16:18:03,442 - INFO - 使用设备: cuda
2025-08-07 16:18:03,442 - INFO - 加载数据...
2025-08-07 16:20:13,682 - INFO - 创建模型...
2025-08-07 16:20:13,738 - INFO - 可训练参数数量: 867,608
2025-08-07 16:20:13,738 - INFO - 总参数数量: 867,608
2025-08-07 16:20:13,738 - INFO - 可训练参数比例: 100.00%
2025-08-07 16:20:13,739 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 16:20:13,739 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 63, 'trial_number': 62}
2025-08-07 16:20:13,739 - INFO - 📊 最少训练轮数: 2
2025-08-07 16:20:13,739 - INFO - Epoch 1/400
2025-08-07 16:38:21,795 - INFO - Train Loss: 1.5483, Train Acc: 46.69%
2025-08-07 16:39:19,597 - INFO - Val Loss: 1.3977, Val Acc: 52.17%, Macro-F1: 53.29%, Kappa: 0.5009
2025-08-07 16:39:19,597 - INFO - Epoch 1 训练时间: 1145.86 秒
2025-08-07 16:39:19,658 - INFO - 保存最佳模型，验证准确率: 52.17%, Macro-F1: 53.29%, Kappa: 0.5009
2025-08-07 16:39:19,659 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_161803/models/best_model.pth
2025-08-07 16:39:19,659 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_161803/models/model_epoch_1_acc_52.17.pth
2025-08-07 16:39:19,659 - INFO - Epoch 2/400
2025-08-07 16:57:25,346 - INFO - Train Loss: 1.3062, Train Acc: 55.77%
2025-08-07 16:58:22,382 - INFO - Val Loss: 1.3179, Val Acc: 55.50%, Macro-F1: 56.53%, Kappa: 0.5356
2025-08-07 16:58:22,382 - INFO - Epoch 2 训练时间: 1142.72 秒
2025-08-07 16:58:22,382 - INFO - 🔍 第2轮门槛检查: 准确率 55.4954%, 门槛 57.4476
2025-08-07 16:58:22,382 - INFO - ⚠️ 第2轮准确率 55.4954% 未达到门槛 57.4476，提前结束训练
2025-08-07 16:58:22,382 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_161803/results/training_history.json
2025-08-07 16:58:22,383 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_161803/results/training_summary.json
2025-08-07 16:58:22,383 - INFO - 训练完成！
2025-08-07 16:58:22,383 - INFO - 最佳验证准确率: 52.17%
2025-08-07 16:58:22,383 - INFO - 最佳Macro-F1: 53.29%
2025-08-07 16:58:22,383 - INFO - 最佳Kappa: 0.5009
2025-08-07 16:58:22,383 - INFO - 总训练时间: 2288.58 秒 (38.1 分钟)
2025-08-07 16:58:22,383 - INFO - 平均每轮训练时间: 1144.29 秒
2025-08-07 16:58:22,383 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_161803
2025-08-07 16:58:22,383 - INFO - 🔍 第2轮准确率记录: 0.5550
2025-08-07 16:58:25,011 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 16:58:25,011 - INFO -    - 当前门槛: 57.4476
2025-08-07 16:58:25,011 - INFO -    - 第2轮准确率: 55.4954
2025-08-07 16:58:25,011 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 16:58:25,011 - INFO - 训练完成 rml201801a Trial 62, 结果: {'best_val_acc': 0.5217401500938087, 'best_val_f1': 0.5328679927350386, 'best_val_kappa': 0.5009462435761481, 'total_epochs': 2, 'total_training_time': 2288.581072807312, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5549536168438608}
2025-08-07 16:58:25,011 - INFO - rml201801a Trial 62 早停训练，使用第2轮准确率: 0.5550
2025-08-07 16:58:25,017 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 16:58:25,017 - INFO - --------------------------------------------------------------------------------
2025-08-07 16:58:25,017 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 16:58:25,017 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 16:58:25,017 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 16:58:25,017 - INFO - --------------------------------------------------------------------------------
2025-08-07 16:58:25,343 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 16:58:25,343 - INFO -    - dropout: 0.12615707431622902 (数据集特定 + 通用)
2025-08-07 16:58:25,344 - INFO -    - lambda_lifting: 0.07462804400066944
2025-08-07 16:58:25,354 - INFO - 📄 临时配置文件创建: /tmp/tmpxzb979w3.yaml
2025-08-07 16:58:25,354 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.12615707431622902, 'batch_size': 256}
2025-08-07 16:58:25,354 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.12615707431622902
2025-08-07 16:58:25,354 - INFO - ✅ 最终训练参数: lambda_lifting=0.07462804400066944, batch_size=128
2025-08-07 16:58:25,354 - INFO - rml201801a Trial 63: 参数 {'dropout': 0.12615707431622902, 'lambda_lifting': 0.07462804400066944}
2025-08-07 16:58:25,464 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 16:58:25,464 - INFO - ====================================================================================================
2025-08-07 16:58:25,464 - INFO - 🚀 开始训练 - rml201801a Trial 63
2025-08-07 16:58:25,464 - INFO - ====================================================================================================
2025-08-07 16:58:25,464 - INFO - 📋 本次训练参数:
2025-08-07 16:58:25,464 - INFO -   dropout: 0.1262
  lambda_lifting: 0.0746
2025-08-07 16:58:25,464 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 16:58:25,465 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 16:58:25,465 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 16:58:25,465 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 16:58:25,465 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 16:58:25,465 - INFO - ====================================================================================================
2025-08-07 16:58:25,465 - INFO - 🎯 开始训练...
2025-08-07 16:58:25,465 - INFO - ====================================================================================================
2025-08-07 16:58:25,465 - INFO - 🎯 开始训练 rml201801a Trial 63 (第64次试验)
2025-08-07 16:58:25,465 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 16:58:25,512 - INFO - 开始训练，配置文件: /tmp/tmpxzb979w3.yaml
2025-08-07 16:58:25,512 - INFO - 数据集类型: rml201801a
2025-08-07 16:58:25,512 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_165825
2025-08-07 16:58:25,523 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_165825/configs/config_backup.yaml
2025-08-07 16:58:25,523 - INFO - 使用设备: cuda
2025-08-07 16:58:25,523 - INFO - 加载数据...
2025-08-07 17:00:30,304 - INFO - 创建模型...
2025-08-07 17:00:30,356 - INFO - 可训练参数数量: 867,608
2025-08-07 17:00:30,357 - INFO - 总参数数量: 867,608
2025-08-07 17:00:30,357 - INFO - 可训练参数比例: 100.00%
2025-08-07 17:00:30,358 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 17:00:30,358 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 64, 'trial_number': 63}
2025-08-07 17:00:30,358 - INFO - 📊 最少训练轮数: 2
2025-08-07 17:00:30,358 - INFO - Epoch 1/400
2025-08-07 17:18:39,149 - INFO - Train Loss: 1.5832, Train Acc: 45.64%
2025-08-07 17:19:37,188 - INFO - Val Loss: 1.4007, Val Acc: 51.96%, Macro-F1: 52.39%, Kappa: 0.4987
2025-08-07 17:19:37,188 - INFO - Epoch 1 训练时间: 1146.83 秒
2025-08-07 17:19:37,251 - INFO - 保存最佳模型，验证准确率: 51.96%, Macro-F1: 52.39%, Kappa: 0.4987
2025-08-07 17:19:37,251 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_165825/models/best_model.pth
2025-08-07 17:19:37,251 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_165825/models/model_epoch_1_acc_51.96.pth
2025-08-07 17:19:37,251 - INFO - Epoch 2/400
2025-08-07 17:37:43,451 - INFO - Train Loss: 1.3219, Train Acc: 55.39%
2025-08-07 17:38:40,519 - INFO - Val Loss: 1.2871, Val Acc: 56.82%, Macro-F1: 58.66%, Kappa: 0.5495
2025-08-07 17:38:40,519 - INFO - Epoch 2 训练时间: 1143.27 秒
2025-08-07 17:38:40,520 - INFO - 🔍 第2轮门槛检查: 准确率 56.8246%, 门槛 57.4476
2025-08-07 17:38:40,520 - INFO - ⚠️ 第2轮准确率 56.8246% 未达到门槛 57.4476，提前结束训练
2025-08-07 17:38:40,520 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_165825/results/training_history.json
2025-08-07 17:38:40,520 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_165825/results/training_summary.json
2025-08-07 17:38:40,520 - INFO - 训练完成！
2025-08-07 17:38:40,520 - INFO - 最佳验证准确率: 51.96%
2025-08-07 17:38:40,520 - INFO - 最佳Macro-F1: 52.39%
2025-08-07 17:38:40,520 - INFO - 最佳Kappa: 0.4987
2025-08-07 17:38:40,520 - INFO - 总训练时间: 2290.10 秒 (38.2 分钟)
2025-08-07 17:38:40,520 - INFO - 平均每轮训练时间: 1145.05 秒
2025-08-07 17:38:40,520 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_165825
2025-08-07 17:38:40,520 - INFO - 🔍 第2轮准确率记录: 0.5682
2025-08-07 17:38:43,198 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 17:38:43,198 - INFO -    - 当前门槛: 57.4476
2025-08-07 17:38:43,198 - INFO -    - 第2轮准确率: 56.8246
2025-08-07 17:38:43,199 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 17:38:43,199 - INFO - 训练完成 rml201801a Trial 63, 结果: {'best_val_acc': 0.5196190327287888, 'best_val_f1': 0.5238757236764241, 'best_val_kappa': 0.4987329037169971, 'total_epochs': 2, 'total_training_time': 2290.099354028702, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5682457786116323}
2025-08-07 17:38:43,199 - INFO - rml201801a Trial 63 早停训练，使用第2轮准确率: 0.5682
2025-08-07 17:38:43,204 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 17:38:43,204 - INFO - --------------------------------------------------------------------------------
2025-08-07 17:38:43,204 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 17:38:43,204 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 17:38:43,204 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 17:38:43,205 - INFO - --------------------------------------------------------------------------------
2025-08-07 17:38:43,535 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 17:38:43,535 - INFO -    - dropout: 0.1752562829289379 (数据集特定 + 通用)
2025-08-07 17:38:43,535 - INFO -    - lambda_lifting: 0.07251150780454019
2025-08-07 17:38:43,546 - INFO - 📄 临时配置文件创建: /tmp/tmpi2lahdwa.yaml
2025-08-07 17:38:43,546 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.1752562829289379, 'batch_size': 256}
2025-08-07 17:38:43,546 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.1752562829289379
2025-08-07 17:38:43,546 - INFO - ✅ 最终训练参数: lambda_lifting=0.07251150780454019, batch_size=128
2025-08-07 17:38:43,546 - INFO - rml201801a Trial 64: 参数 {'dropout': 0.1752562829289379, 'lambda_lifting': 0.07251150780454019}
2025-08-07 17:38:43,641 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 17:38:43,642 - INFO - ====================================================================================================
2025-08-07 17:38:43,642 - INFO - 🚀 开始训练 - rml201801a Trial 64
2025-08-07 17:38:43,642 - INFO - ====================================================================================================
2025-08-07 17:38:43,642 - INFO - 📋 本次训练参数:
2025-08-07 17:38:43,642 - INFO -   dropout: 0.1753
  lambda_lifting: 0.0725
2025-08-07 17:38:43,642 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 17:38:43,642 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 17:38:43,642 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 17:38:43,642 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 17:38:43,642 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 17:38:43,642 - INFO - ====================================================================================================
2025-08-07 17:38:43,642 - INFO - 🎯 开始训练...
2025-08-07 17:38:43,642 - INFO - ====================================================================================================
2025-08-07 17:38:43,642 - INFO - 🎯 开始训练 rml201801a Trial 64 (第65次试验)
2025-08-07 17:38:43,642 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 17:38:43,690 - INFO - 开始训练，配置文件: /tmp/tmpi2lahdwa.yaml
2025-08-07 17:38:43,690 - INFO - 数据集类型: rml201801a
2025-08-07 17:38:43,690 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_173843
2025-08-07 17:38:43,700 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_173843/configs/config_backup.yaml
2025-08-07 17:38:43,701 - INFO - 使用设备: cuda
2025-08-07 17:38:43,701 - INFO - 加载数据...
2025-08-07 17:40:48,396 - INFO - 创建模型...
2025-08-07 17:40:48,459 - INFO - 可训练参数数量: 867,608
2025-08-07 17:40:48,459 - INFO - 总参数数量: 867,608
2025-08-07 17:40:48,459 - INFO - 可训练参数比例: 100.00%
2025-08-07 17:40:48,460 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 17:40:48,460 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 65, 'trial_number': 64}
2025-08-07 17:40:48,460 - INFO - 📊 最少训练轮数: 2
2025-08-07 17:40:48,460 - INFO - Epoch 1/400
2025-08-07 17:58:57,690 - INFO - Train Loss: 1.6119, Train Acc: 45.21%
2025-08-07 17:59:55,772 - INFO - Val Loss: 1.4223, Val Acc: 51.16%, Macro-F1: 51.32%, Kappa: 0.4903
2025-08-07 17:59:55,773 - INFO - Epoch 1 训练时间: 1147.31 秒
2025-08-07 17:59:55,838 - INFO - 保存最佳模型，验证准确率: 51.16%, Macro-F1: 51.32%, Kappa: 0.4903
2025-08-07 17:59:55,838 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_173843/models/best_model.pth
2025-08-07 17:59:55,838 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_173843/models/model_epoch_1_acc_51.16.pth
2025-08-07 17:59:55,838 - INFO - Epoch 2/400
2025-08-07 18:18:01,254 - INFO - Train Loss: 1.3561, Train Acc: 54.71%
2025-08-07 18:18:58,601 - INFO - Val Loss: 1.3384, Val Acc: 55.47%, Macro-F1: 56.97%, Kappa: 0.5354
2025-08-07 18:18:58,601 - INFO - Epoch 2 训练时间: 1142.76 秒
2025-08-07 18:18:58,601 - INFO - 🔍 第2轮门槛检查: 准确率 55.4737%, 门槛 57.4476
2025-08-07 18:18:58,601 - INFO - ⚠️ 第2轮准确率 55.4737% 未达到门槛 57.4476，提前结束训练
2025-08-07 18:18:58,602 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_173843/results/training_history.json
2025-08-07 18:18:58,602 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_173843/results/training_summary.json
2025-08-07 18:18:58,602 - INFO - 训练完成！
2025-08-07 18:18:58,602 - INFO - 最佳验证准确率: 51.16%
2025-08-07 18:18:58,602 - INFO - 最佳Macro-F1: 51.32%
2025-08-07 18:18:58,602 - INFO - 最佳Kappa: 0.4903
2025-08-07 18:18:58,602 - INFO - 总训练时间: 2290.08 秒 (38.2 分钟)
2025-08-07 18:18:58,602 - INFO - 平均每轮训练时间: 1145.04 秒
2025-08-07 18:18:58,602 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_173843
2025-08-07 18:18:58,602 - INFO - 🔍 第2轮准确率记录: 0.5547
2025-08-07 18:19:01,278 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 18:19:01,279 - INFO -    - 当前门槛: 57.4476
2025-08-07 18:19:01,279 - INFO -    - 第2轮准确率: 55.4737
2025-08-07 18:19:01,279 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 18:19:01,279 - INFO - 训练完成 rml201801a Trial 64, 结果: {'best_val_acc': 0.5115827600583698, 'best_val_f1': 0.5131969203707824, 'best_val_kappa': 0.4903472278869946, 'total_epochs': 2, 'total_training_time': 2290.0757925510406, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5547373358348968}
2025-08-07 18:19:01,279 - INFO - rml201801a Trial 64 早停训练，使用第2轮准确率: 0.5547
2025-08-07 18:19:01,284 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 18:19:01,285 - INFO - --------------------------------------------------------------------------------
2025-08-07 18:19:01,285 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 18:19:01,285 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 18:19:01,285 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 18:19:01,285 - INFO - --------------------------------------------------------------------------------
2025-08-07 18:19:01,613 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 18:19:01,613 - INFO - 🔄 使用新门槛 57.4476 进行额外 5 次试验 (已额外进行 45 次)
2025-08-07 18:19:01,641 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 18:19:01,642 - INFO -    - dropout: 0.11870833252396801 (数据集特定 + 通用)
2025-08-07 18:19:01,642 - INFO -    - lambda_lifting: 0.049540262211959674
2025-08-07 18:19:01,652 - INFO - 📄 临时配置文件创建: /tmp/tmpb6i_6pa7.yaml
2025-08-07 18:19:01,652 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.11870833252396801, 'batch_size': 256}
2025-08-07 18:19:01,652 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.11870833252396801
2025-08-07 18:19:01,652 - INFO - ✅ 最终训练参数: lambda_lifting=0.049540262211959674, batch_size=128
2025-08-07 18:19:01,653 - INFO - rml201801a Trial 65: 参数 {'dropout': 0.11870833252396801, 'lambda_lifting': 0.049540262211959674}
2025-08-07 18:19:01,767 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 18:19:01,767 - INFO - ====================================================================================================
2025-08-07 18:19:01,767 - INFO - 🚀 开始训练 - rml201801a Trial 65
2025-08-07 18:19:01,767 - INFO - ====================================================================================================
2025-08-07 18:19:01,767 - INFO - 📋 本次训练参数:
2025-08-07 18:19:01,767 - INFO -   dropout: 0.1187
  lambda_lifting: 0.0495
2025-08-07 18:19:01,767 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 18:19:01,767 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 18:19:01,767 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 18:19:01,767 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 18:19:01,767 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 18:19:01,767 - INFO - ====================================================================================================
2025-08-07 18:19:01,767 - INFO - 🎯 开始训练...
2025-08-07 18:19:01,767 - INFO - ====================================================================================================
2025-08-07 18:19:01,767 - INFO - 🎯 开始训练 rml201801a Trial 65 (第66次试验)
2025-08-07 18:19:01,768 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 18:19:01,815 - INFO - 开始训练，配置文件: /tmp/tmpb6i_6pa7.yaml
2025-08-07 18:19:01,815 - INFO - 数据集类型: rml201801a
2025-08-07 18:19:01,815 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_181901
2025-08-07 18:19:01,826 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_181901/configs/config_backup.yaml
2025-08-07 18:19:01,826 - INFO - 使用设备: cuda
2025-08-07 18:19:01,826 - INFO - 加载数据...
2025-08-07 18:21:16,372 - INFO - 创建模型...
2025-08-07 18:21:16,533 - INFO - 可训练参数数量: 867,608
2025-08-07 18:21:16,533 - INFO - 总参数数量: 867,608
2025-08-07 18:21:16,533 - INFO - 可训练参数比例: 100.00%
2025-08-07 18:21:16,535 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 18:21:16,535 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 66, 'trial_number': 65}
2025-08-07 18:21:16,535 - INFO - 📊 最少训练轮数: 2
2025-08-07 18:21:16,535 - INFO - Epoch 1/400
2025-08-07 18:39:24,104 - INFO - Train Loss: 1.5642, Train Acc: 46.45%
2025-08-07 18:40:22,152 - INFO - Val Loss: 1.3807, Val Acc: 52.56%, Macro-F1: 53.53%, Kappa: 0.5049
2025-08-07 18:40:22,153 - INFO - Epoch 1 训练时间: 1145.62 秒
2025-08-07 18:40:22,214 - INFO - 保存最佳模型，验证准确率: 52.56%, Macro-F1: 53.53%, Kappa: 0.5049
2025-08-07 18:40:22,214 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_181901/models/best_model.pth
2025-08-07 18:40:22,214 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_181901/models/model_epoch_1_acc_52.56.pth
2025-08-07 18:40:22,214 - INFO - Epoch 2/400
2025-08-07 18:58:26,220 - INFO - Train Loss: 1.3166, Train Acc: 55.56%
2025-08-07 18:59:23,331 - INFO - Val Loss: 1.3104, Val Acc: 55.92%, Macro-F1: 57.13%, Kappa: 0.5400
2025-08-07 18:59:23,332 - INFO - Epoch 2 训练时间: 1141.12 秒
2025-08-07 18:59:23,332 - INFO - 🔍 第2轮门槛检查: 准确率 55.9185%, 门槛 57.4476
2025-08-07 18:59:23,332 - INFO - ⚠️ 第2轮准确率 55.9185% 未达到门槛 57.4476，提前结束训练
2025-08-07 18:59:23,332 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_181901/results/training_history.json
2025-08-07 18:59:23,332 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_181901/results/training_summary.json
2025-08-07 18:59:23,333 - INFO - 训练完成！
2025-08-07 18:59:23,333 - INFO - 最佳验证准确率: 52.56%
2025-08-07 18:59:23,333 - INFO - 最佳Macro-F1: 53.53%
2025-08-07 18:59:23,333 - INFO - 最佳Kappa: 0.5049
2025-08-07 18:59:23,333 - INFO - 总训练时间: 2286.74 秒 (38.1 分钟)
2025-08-07 18:59:23,333 - INFO - 平均每轮训练时间: 1143.37 秒
2025-08-07 18:59:23,333 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_181901
2025-08-07 18:59:23,333 - INFO - 🔍 第2轮准确率记录: 0.5592
2025-08-07 18:59:26,014 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 18:59:26,014 - INFO -    - 当前门槛: 57.4476
2025-08-07 18:59:26,014 - INFO -    - 第2轮准确率: 55.9185
2025-08-07 18:59:26,014 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 18:59:26,014 - INFO - 训练完成 rml201801a Trial 65, 结果: {'best_val_acc': 0.5255550343964979, 'best_val_f1': 0.5352875354855047, 'best_val_kappa': 0.5049269924137368, 'total_epochs': 2, 'total_training_time': 2286.735491991043, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5591854283927454}
2025-08-07 18:59:26,015 - INFO - rml201801a Trial 65 早停训练，使用第2轮准确率: 0.5592
2025-08-07 18:59:26,020 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 18:59:26,020 - INFO - --------------------------------------------------------------------------------
2025-08-07 18:59:26,020 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 18:59:26,020 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 18:59:26,020 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 18:59:26,020 - INFO - --------------------------------------------------------------------------------
2025-08-07 18:59:26,394 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 18:59:26,394 - INFO -    - dropout: 0.14523725492720777 (数据集特定 + 通用)
2025-08-07 18:59:26,394 - INFO -    - lambda_lifting: 0.090667641265434
2025-08-07 18:59:26,405 - INFO - 📄 临时配置文件创建: /tmp/tmpcts6hrh4.yaml
2025-08-07 18:59:26,405 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.14523725492720777, 'batch_size': 256}
2025-08-07 18:59:26,405 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.14523725492720777
2025-08-07 18:59:26,405 - INFO - ✅ 最终训练参数: lambda_lifting=0.090667641265434, batch_size=128
2025-08-07 18:59:26,405 - INFO - rml201801a Trial 66: 参数 {'dropout': 0.14523725492720777, 'lambda_lifting': 0.090667641265434}
2025-08-07 18:59:26,493 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 18:59:26,493 - INFO - ====================================================================================================
2025-08-07 18:59:26,493 - INFO - 🚀 开始训练 - rml201801a Trial 66
2025-08-07 18:59:26,493 - INFO - ====================================================================================================
2025-08-07 18:59:26,493 - INFO - 📋 本次训练参数:
2025-08-07 18:59:26,493 - INFO -   dropout: 0.1452
  lambda_lifting: 0.0907
2025-08-07 18:59:26,493 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 18:59:26,493 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 18:59:26,493 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 18:59:26,493 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 18:59:26,493 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 18:59:26,493 - INFO - ====================================================================================================
2025-08-07 18:59:26,493 - INFO - 🎯 开始训练...
2025-08-07 18:59:26,493 - INFO - ====================================================================================================
2025-08-07 18:59:26,494 - INFO - 🎯 开始训练 rml201801a Trial 66 (第67次试验)
2025-08-07 18:59:26,494 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 18:59:26,541 - INFO - 开始训练，配置文件: /tmp/tmpcts6hrh4.yaml
2025-08-07 18:59:26,541 - INFO - 数据集类型: rml201801a
2025-08-07 18:59:26,541 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_185926
2025-08-07 18:59:26,552 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_185926/configs/config_backup.yaml
2025-08-07 18:59:26,552 - INFO - 使用设备: cuda
2025-08-07 18:59:26,552 - INFO - 加载数据...
2025-08-07 19:01:32,901 - INFO - 创建模型...
2025-08-07 19:01:32,964 - INFO - 可训练参数数量: 867,608
2025-08-07 19:01:32,964 - INFO - 总参数数量: 867,608
2025-08-07 19:01:32,964 - INFO - 可训练参数比例: 100.00%
2025-08-07 19:01:32,965 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 19:01:32,965 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 67, 'trial_number': 66}
2025-08-07 19:01:32,965 - INFO - 📊 最少训练轮数: 2
2025-08-07 19:01:32,965 - INFO - Epoch 1/400
2025-08-07 19:19:40,487 - INFO - Train Loss: 1.5968, Train Acc: 45.41%
2025-08-07 19:20:38,474 - INFO - Val Loss: 1.4156, Val Acc: 51.15%, Macro-F1: 52.25%, Kappa: 0.4903
2025-08-07 19:20:38,474 - INFO - Epoch 1 训练时间: 1145.51 秒
2025-08-07 19:20:38,536 - INFO - 保存最佳模型，验证准确率: 51.15%, Macro-F1: 52.25%, Kappa: 0.4903
2025-08-07 19:20:38,537 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_185926/models/best_model.pth
2025-08-07 19:20:38,537 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_185926/models/model_epoch_1_acc_51.15.pth
2025-08-07 19:20:38,537 - INFO - Epoch 2/400
2025-08-07 19:38:42,741 - INFO - Train Loss: 1.3434, Train Acc: 54.76%
2025-08-07 19:39:39,742 - INFO - Val Loss: 1.2962, Val Acc: 56.51%, Macro-F1: 57.99%, Kappa: 0.5462
2025-08-07 19:39:39,743 - INFO - Epoch 2 训练时间: 1141.21 秒
2025-08-07 19:39:39,743 - INFO - 🔍 第2轮门槛检查: 准确率 56.5116%, 门槛 57.4476
2025-08-07 19:39:39,743 - INFO - ⚠️ 第2轮准确率 56.5116% 未达到门槛 57.4476，提前结束训练
2025-08-07 19:39:39,743 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_185926/results/training_history.json
2025-08-07 19:39:39,743 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_185926/results/training_summary.json
2025-08-07 19:39:39,743 - INFO - 训练完成！
2025-08-07 19:39:39,744 - INFO - 最佳验证准确率: 51.15%
2025-08-07 19:39:39,744 - INFO - 最佳Macro-F1: 52.25%
2025-08-07 19:39:39,744 - INFO - 最佳Kappa: 0.4903
2025-08-07 19:39:39,744 - INFO - 总训练时间: 2286.72 秒 (38.1 分钟)
2025-08-07 19:39:39,744 - INFO - 平均每轮训练时间: 1143.36 秒
2025-08-07 19:39:39,744 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_185926
2025-08-07 19:39:39,744 - INFO - 🔍 第2轮准确率记录: 0.5651
2025-08-07 19:39:42,451 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 19:39:42,452 - INFO -    - 当前门槛: 57.4476
2025-08-07 19:39:42,452 - INFO -    - 第2轮准确率: 56.5116
2025-08-07 19:39:42,452 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 19:39:42,452 - INFO - 训练完成 rml201801a Trial 66, 结果: {'best_val_acc': 0.5115228267667292, 'best_val_f1': 0.5225208592177285, 'best_val_kappa': 0.49028468880006537, 'total_epochs': 2, 'total_training_time': 2286.715566635132, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.565116218469877}
2025-08-07 19:39:42,452 - INFO - rml201801a Trial 66 早停训练，使用第2轮准确率: 0.5651
2025-08-07 19:39:42,458 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 19:39:42,458 - INFO - --------------------------------------------------------------------------------
2025-08-07 19:39:42,458 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 19:39:42,458 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 19:39:42,458 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 19:39:42,458 - INFO - --------------------------------------------------------------------------------
2025-08-07 19:39:43,101 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 19:39:43,101 - INFO -    - dropout: 0.21624056892189708 (数据集特定 + 通用)
2025-08-07 19:39:43,102 - INFO -    - lambda_lifting: 0.03046533544900941
2025-08-07 19:39:43,112 - INFO - 📄 临时配置文件创建: /tmp/tmpp_62tjd7.yaml
2025-08-07 19:39:43,112 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.21624056892189708, 'batch_size': 256}
2025-08-07 19:39:43,112 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.21624056892189708
2025-08-07 19:39:43,112 - INFO - ✅ 最终训练参数: lambda_lifting=0.03046533544900941, batch_size=128
2025-08-07 19:39:43,112 - INFO - rml201801a Trial 67: 参数 {'dropout': 0.21624056892189708, 'lambda_lifting': 0.03046533544900941}
2025-08-07 19:39:43,225 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 19:39:43,226 - INFO - ====================================================================================================
2025-08-07 19:39:43,226 - INFO - 🚀 开始训练 - rml201801a Trial 67
2025-08-07 19:39:43,226 - INFO - ====================================================================================================
2025-08-07 19:39:43,226 - INFO - 📋 本次训练参数:
2025-08-07 19:39:43,226 - INFO -   dropout: 0.2162
  lambda_lifting: 0.0305
2025-08-07 19:39:43,226 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 19:39:43,226 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 19:39:43,226 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 19:39:43,226 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 19:39:43,226 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 19:39:43,226 - INFO - ====================================================================================================
2025-08-07 19:39:43,226 - INFO - 🎯 开始训练...
2025-08-07 19:39:43,226 - INFO - ====================================================================================================
2025-08-07 19:39:43,226 - INFO - 🎯 开始训练 rml201801a Trial 67 (第68次试验)
2025-08-07 19:39:43,226 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 19:39:43,274 - INFO - 开始训练，配置文件: /tmp/tmpp_62tjd7.yaml
2025-08-07 19:39:43,274 - INFO - 数据集类型: rml201801a
2025-08-07 19:39:43,274 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_193943
2025-08-07 19:39:43,285 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_193943/configs/config_backup.yaml
2025-08-07 19:39:43,285 - INFO - 使用设备: cuda
2025-08-07 19:39:43,285 - INFO - 加载数据...
2025-08-07 19:41:53,688 - INFO - 创建模型...
2025-08-07 19:41:53,736 - INFO - 可训练参数数量: 867,608
2025-08-07 19:41:53,736 - INFO - 总参数数量: 867,608
2025-08-07 19:41:53,736 - INFO - 可训练参数比例: 100.00%
2025-08-07 19:41:53,737 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 19:41:53,737 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 68, 'trial_number': 67}
2025-08-07 19:41:53,737 - INFO - 📊 最少训练轮数: 2
2025-08-07 19:41:53,737 - INFO - Epoch 1/400
2025-08-07 20:00:02,346 - INFO - Train Loss: 1.6059, Train Acc: 45.02%
2025-08-07 20:01:00,434 - INFO - Val Loss: 1.4581, Val Acc: 49.25%, Macro-F1: 49.06%, Kappa: 0.4704
2025-08-07 20:01:00,434 - INFO - Epoch 1 训练时间: 1146.70 秒
2025-08-07 20:01:00,518 - INFO - 保存最佳模型，验证准确率: 49.25%, Macro-F1: 49.06%, Kappa: 0.4704
2025-08-07 20:01:00,519 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_193943/models/best_model.pth
2025-08-07 20:01:00,519 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_193943/models/model_epoch_1_acc_49.25.pth
2025-08-07 20:01:00,519 - INFO - Epoch 2/400
2025-08-07 20:19:06,448 - INFO - Train Loss: 1.3548, Train Acc: 54.32%
2025-08-07 20:20:03,583 - INFO - Val Loss: 1.3713, Val Acc: 53.80%, Macro-F1: 55.12%, Kappa: 0.5179
2025-08-07 20:20:03,583 - INFO - Epoch 2 训练时间: 1143.06 秒
2025-08-07 20:20:03,583 - INFO - 🔍 第2轮门槛检查: 准确率 53.7998%, 门槛 57.4476
2025-08-07 20:20:03,583 - INFO - ⚠️ 第2轮准确率 53.7998% 未达到门槛 57.4476，提前结束训练
2025-08-07 20:20:03,583 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_193943/results/training_history.json
2025-08-07 20:20:03,584 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_193943/results/training_summary.json
2025-08-07 20:20:03,584 - INFO - 训练完成！
2025-08-07 20:20:03,584 - INFO - 最佳验证准确率: 49.25%
2025-08-07 20:20:03,584 - INFO - 最佳Macro-F1: 49.06%
2025-08-07 20:20:03,584 - INFO - 最佳Kappa: 0.4704
2025-08-07 20:20:03,584 - INFO - 总训练时间: 2289.76 秒 (38.2 分钟)
2025-08-07 20:20:03,584 - INFO - 平均每轮训练时间: 1144.88 秒
2025-08-07 20:20:03,584 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_193943
2025-08-07 20:20:03,584 - INFO - 🔍 第2轮准确率记录: 0.5380
2025-08-07 20:20:06,501 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 20:20:06,501 - INFO -    - 当前门槛: 57.4476
2025-08-07 20:20:06,502 - INFO -    - 第2轮准确率: 53.7998
2025-08-07 20:20:06,502 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 20:20:06,502 - INFO - 训练完成 rml201801a Trial 67, 结果: {'best_val_acc': 0.4924822805920367, 'best_val_f1': 0.4905636919772081, 'best_val_kappa': 0.4704162927916905, 'total_epochs': 2, 'total_training_time': 2289.7614760398865, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5379977069001459}
2025-08-07 20:20:06,502 - INFO - rml201801a Trial 67 早停训练，使用第2轮准确率: 0.5380
2025-08-07 20:20:06,509 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 20:20:06,509 - INFO - --------------------------------------------------------------------------------
2025-08-07 20:20:06,509 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 20:20:06,510 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 20:20:06,510 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 20:20:06,510 - INFO - --------------------------------------------------------------------------------
2025-08-07 20:20:06,866 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 20:20:06,866 - INFO -    - dropout: 0.12062370209382149 (数据集特定 + 通用)
2025-08-07 20:20:06,866 - INFO -    - lambda_lifting: 0.07043385682151337
2025-08-07 20:20:06,879 - INFO - 📄 临时配置文件创建: /tmp/tmp_qtqa9eq.yaml
2025-08-07 20:20:06,879 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.12062370209382149, 'batch_size': 256}
2025-08-07 20:20:06,880 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.12062370209382149
2025-08-07 20:20:06,880 - INFO - ✅ 最终训练参数: lambda_lifting=0.07043385682151337, batch_size=128
2025-08-07 20:20:06,880 - INFO - rml201801a Trial 68: 参数 {'dropout': 0.12062370209382149, 'lambda_lifting': 0.07043385682151337}
2025-08-07 20:20:06,990 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 20:20:06,990 - INFO - ====================================================================================================
2025-08-07 20:20:06,990 - INFO - 🚀 开始训练 - rml201801a Trial 68
2025-08-07 20:20:06,990 - INFO - ====================================================================================================
2025-08-07 20:20:06,990 - INFO - 📋 本次训练参数:
2025-08-07 20:20:06,991 - INFO -   dropout: 0.1206
  lambda_lifting: 0.0704
2025-08-07 20:20:06,991 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 20:20:06,991 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 20:20:06,991 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 20:20:06,991 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 20:20:06,991 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 20:20:06,991 - INFO - ====================================================================================================
2025-08-07 20:20:06,991 - INFO - 🎯 开始训练...
2025-08-07 20:20:06,991 - INFO - ====================================================================================================
2025-08-07 20:20:06,991 - INFO - 🎯 开始训练 rml201801a Trial 68 (第69次试验)
2025-08-07 20:20:06,991 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 20:20:07,053 - INFO - 开始训练，配置文件: /tmp/tmp_qtqa9eq.yaml
2025-08-07 20:20:07,053 - INFO - 数据集类型: rml201801a
2025-08-07 20:20:07,053 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_202007
2025-08-07 20:20:07,064 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_202007/configs/config_backup.yaml
2025-08-07 20:20:07,064 - INFO - 使用设备: cuda
2025-08-07 20:20:07,064 - INFO - 加载数据...
2025-08-07 20:22:17,684 - INFO - 创建模型...
2025-08-07 20:22:17,736 - INFO - 可训练参数数量: 867,608
2025-08-07 20:22:17,736 - INFO - 总参数数量: 867,608
2025-08-07 20:22:17,736 - INFO - 可训练参数比例: 100.00%
2025-08-07 20:22:17,737 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 20:22:17,737 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 69, 'trial_number': 68}
2025-08-07 20:22:17,737 - INFO - 📊 最少训练轮数: 2
2025-08-07 20:22:17,737 - INFO - Epoch 1/400
2025-08-07 20:40:26,635 - INFO - Train Loss: 1.5740, Train Acc: 46.13%
2025-08-07 20:41:24,599 - INFO - Val Loss: 1.3638, Val Acc: 53.08%, Macro-F1: 53.61%, Kappa: 0.5104
2025-08-07 20:41:24,600 - INFO - Epoch 1 训练时间: 1146.86 秒
2025-08-07 20:41:24,662 - INFO - 保存最佳模型，验证准确率: 53.08%, Macro-F1: 53.61%, Kappa: 0.5104
2025-08-07 20:41:24,662 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_202007/models/best_model.pth
2025-08-07 20:41:24,662 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_202007/models/model_epoch_1_acc_53.08.pth
2025-08-07 20:41:24,662 - INFO - Epoch 2/400
2025-08-07 20:59:30,829 - INFO - Train Loss: 1.3180, Train Acc: 55.51%
2025-08-07 21:00:28,095 - INFO - Val Loss: 1.2877, Val Acc: 56.65%, Macro-F1: 57.95%, Kappa: 0.5477
2025-08-07 21:00:28,095 - INFO - Epoch 2 训练时间: 1143.43 秒
2025-08-07 21:00:28,096 - INFO - 🔍 第2轮门槛检查: 准确率 56.6542%, 门槛 57.4476
2025-08-07 21:00:28,096 - INFO - ⚠️ 第2轮准确率 56.6542% 未达到门槛 57.4476，提前结束训练
2025-08-07 21:00:28,096 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_202007/results/training_history.json
2025-08-07 21:00:28,096 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_202007/results/training_summary.json
2025-08-07 21:00:28,096 - INFO - 训练完成！
2025-08-07 21:00:28,096 - INFO - 最佳验证准确率: 53.08%
2025-08-07 21:00:28,097 - INFO - 最佳Macro-F1: 53.61%
2025-08-07 21:00:28,097 - INFO - 最佳Kappa: 0.5104
2025-08-07 21:00:28,097 - INFO - 总训练时间: 2290.30 秒 (38.2 分钟)
2025-08-07 21:00:28,097 - INFO - 平均每轮训练时间: 1145.15 秒
2025-08-07 21:00:28,097 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_202007
2025-08-07 21:00:28,097 - INFO - 🔍 第2轮准确率记录: 0.5665
2025-08-07 21:00:30,669 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 21:00:30,669 - INFO -    - 当前门槛: 57.4476
2025-08-07 21:00:30,669 - INFO -    - 第2轮准确率: 56.6542
2025-08-07 21:00:30,669 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 21:00:30,670 - INFO - 训练完成 rml201801a Trial 68, 结果: {'best_val_acc': 0.5308187408797165, 'best_val_f1': 0.5360696335811995, 'best_val_kappa': 0.5104195557005737, 'total_epochs': 2, 'total_training_time': 2290.296011209488, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.566541588492808}
2025-08-07 21:00:30,670 - INFO - rml201801a Trial 68 早停训练，使用第2轮准确率: 0.5665
2025-08-07 21:00:30,676 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 21:00:30,676 - INFO - --------------------------------------------------------------------------------
2025-08-07 21:00:30,676 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 21:00:30,676 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 21:00:30,677 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 21:00:30,677 - INFO - --------------------------------------------------------------------------------
2025-08-07 21:00:31,090 - INFO - 🔧 应用优化参数到 rml201801a 配置:
2025-08-07 21:00:31,090 - INFO -    - dropout: 0.18592293245513836 (数据集特定 + 通用)
2025-08-07 21:00:31,090 - INFO -    - lambda_lifting: 0.05544697552727458
2025-08-07 21:00:31,104 - INFO - 📄 临时配置文件创建: /tmp/tmpzl2x39d9.yaml
2025-08-07 21:00:31,104 - INFO - ✅ 最终数据集特定参数: {'wavelet_dim': 64, 'rnn_dim': 128, 'num_layers': 3, 'num_levels': 2, 'dropout': 0.18592293245513836, 'batch_size': 256}
2025-08-07 21:00:31,104 - INFO - ✅ 最终通用模型参数: num_layers=4, num_levels=4, dropout=0.18592293245513836
2025-08-07 21:00:31,104 - INFO - ✅ 最终训练参数: lambda_lifting=0.05544697552727458, batch_size=128
2025-08-07 21:00:31,105 - INFO - rml201801a Trial 69: 参数 {'dropout': 0.18592293245513836, 'lambda_lifting': 0.05544697552727458}
2025-08-07 21:00:31,216 - INFO - 显存状态 - 总计: 23.6GB, 已用: 0.0GB, 缓存: 1.8GB, 可用: 21.9GB
2025-08-07 21:00:31,216 - INFO - ====================================================================================================
2025-08-07 21:00:31,216 - INFO - 🚀 开始训练 - rml201801a Trial 69
2025-08-07 21:00:31,216 - INFO - ====================================================================================================
2025-08-07 21:00:31,216 - INFO - 📋 本次训练参数:
2025-08-07 21:00:31,216 - INFO -   dropout: 0.1859
  lambda_lifting: 0.0554
2025-08-07 21:00:31,216 - INFO - 
🏆 rml201801a 当前TOP 3最佳参数组合:
2025-08-07 21:00:31,217 - INFO -   #1 | Trial12 | 准确率:0.5745 | dropout=0.1017, lambda_lifting=0.0136
2025-08-07 21:00:31,217 - INFO -   #2 | Trial33 | 准确率:0.5696 | dropout=0.1045, lambda_lifting=0.0361
2025-08-07 21:00:31,217 - INFO -   #3 | Trial51 | 准确率:0.5690 | dropout=0.1344, lambda_lifting=0.0133
2025-08-07 21:00:31,217 - INFO - 
🔧 预估模型参数量: 169,472
2025-08-07 21:00:31,217 - INFO - ====================================================================================================
2025-08-07 21:00:31,217 - INFO - 🎯 开始训练...
2025-08-07 21:00:31,217 - INFO - ====================================================================================================
2025-08-07 21:00:31,217 - INFO - 🎯 开始训练 rml201801a Trial 69 (第70次试验)
2025-08-07 21:00:31,217 - INFO - 🚪 当前第2轮门槛: 57.4476
2025-08-07 21:00:31,283 - INFO - 开始训练，配置文件: /tmp/tmpzl2x39d9.yaml
2025-08-07 21:00:31,283 - INFO - 数据集类型: rml201801a
2025-08-07 21:00:31,283 - INFO - 实验目录: ./saved_models/wnn_mrnn/rml201801a_20250807_210031
2025-08-07 21:00:31,298 - INFO - 配置文件备份保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_210031/configs/config_backup.yaml
2025-08-07 21:00:31,298 - INFO - 使用设备: cuda
2025-08-07 21:00:31,299 - INFO - 加载数据...
2025-08-07 21:02:41,281 - INFO - 创建模型...
2025-08-07 21:02:41,336 - INFO - 可训练参数数量: 867,608
2025-08-07 21:02:41,336 - INFO - 总参数数量: 867,608
2025-08-07 21:02:41,336 - INFO - 可训练参数比例: 100.00%
2025-08-07 21:02:41,337 - INFO - 🎯 启用第二轮门槛检查，门槛: 57.4476
2025-08-07 21:02:41,337 - INFO - 🔍 试验信息: {'dataset': 'rml201801a', 'trial_count': 70, 'trial_number': 69}
2025-08-07 21:02:41,337 - INFO - 📊 最少训练轮数: 2
2025-08-07 21:02:41,337 - INFO - Epoch 1/400
2025-08-07 21:20:48,974 - INFO - Train Loss: 1.6164, Train Acc: 44.69%
2025-08-07 21:21:47,075 - INFO - Val Loss: 1.4865, Val Acc: 48.55%, Macro-F1: 48.13%, Kappa: 0.4631
2025-08-07 21:21:47,075 - INFO - Epoch 1 训练时间: 1145.74 秒
2025-08-07 21:21:47,137 - INFO - 保存最佳模型，验证准确率: 48.55%, Macro-F1: 48.13%, Kappa: 0.4631
2025-08-07 21:21:47,137 - INFO - 模型保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_210031/models/best_model.pth
2025-08-07 21:21:47,137 - INFO - 模型副本保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_210031/models/model_epoch_1_acc_48.55.pth
2025-08-07 21:21:47,137 - INFO - Epoch 2/400
2025-08-07 21:39:53,043 - INFO - Train Loss: 1.3569, Train Acc: 54.48%
2025-08-07 21:40:50,123 - INFO - Val Loss: 1.3495, Val Acc: 55.21%, Macro-F1: 57.38%, Kappa: 0.5326
2025-08-07 21:40:50,123 - INFO - Epoch 2 训练时间: 1142.99 秒
2025-08-07 21:40:50,123 - INFO - 🔍 第2轮门槛检查: 准确率 55.2087%, 门槛 57.4476
2025-08-07 21:40:50,123 - INFO - ⚠️ 第2轮准确率 55.2087% 未达到门槛 57.4476，提前结束训练
2025-08-07 21:40:50,124 - INFO - 训练历史保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_210031/results/training_history.json
2025-08-07 21:40:50,124 - INFO - 训练摘要保存到: ./saved_models/wnn_mrnn/rml201801a_20250807_210031/results/training_summary.json
2025-08-07 21:40:50,124 - INFO - 训练完成！
2025-08-07 21:40:50,124 - INFO - 最佳验证准确率: 48.55%
2025-08-07 21:40:50,124 - INFO - 最佳Macro-F1: 48.13%
2025-08-07 21:40:50,124 - INFO - 最佳Kappa: 0.4631
2025-08-07 21:40:50,124 - INFO - 总训练时间: 2288.72 秒 (38.1 分钟)
2025-08-07 21:40:50,124 - INFO - 平均每轮训练时间: 1144.36 秒
2025-08-07 21:40:50,124 - INFO - 实验结果保存在: ./saved_models/wnn_mrnn/rml201801a_20250807_210031
2025-08-07 21:40:50,124 - INFO - 🔍 第2轮准确率记录: 0.5521
2025-08-07 21:40:52,833 - INFO - 🔍 rml201801a 第2轮门槛检查:
2025-08-07 21:40:52,833 - INFO -    - 当前门槛: 57.4476
2025-08-07 21:40:52,833 - INFO -    - 第2轮准确率: 55.2087
2025-08-07 21:40:52,833 - INFO - 📊 rml201801a 第2轮准确率未超过门槛，门槛保持: 57.4476
2025-08-07 21:40:52,833 - INFO - 训练完成 rml201801a Trial 69, 结果: {'best_val_acc': 0.4854831144465291, 'best_val_f1': 0.4813388959883582, 'best_val_kappa': 0.4631128150746391, 'total_epochs': 2, 'total_training_time': 2288.724184989929, 'trainable_parameters': 867608, 'early_stopped': True, 'second_epoch_acc': 0.5520872420262664}
2025-08-07 21:40:52,833 - INFO - rml201801a Trial 69 早停训练，使用第2轮准确率: 0.5521
2025-08-07 21:40:52,839 - INFO - 
📊 rml201801a 当前TOP 3 参数组合排名:
2025-08-07 21:40:52,839 - INFO - --------------------------------------------------------------------------------
2025-08-07 21:40:52,839 - INFO - #1 | 试验12 | 准确率:0.5745 | dropout=0.10172189671003123, lambda_lifting=0.013577754816239018
2025-08-07 21:40:52,839 - INFO - #2 | 试验33 | 准确率:0.5696 | dropout=0.10449021418159912, lambda_lifting=0.03607260164657145
2025-08-07 21:40:52,839 - INFO - #3 | 试验51 | 准确率:0.5690 | dropout=0.1344342278629412, lambda_lifting=0.013304180223409408
2025-08-07 21:40:52,839 - INFO - --------------------------------------------------------------------------------
2025-08-07 21:40:53,153 - INFO - 📈 当前已有 0 次完整训练完成
2025-08-07 21:40:53,153 - WARNING - ⚠️ 达到最大试验次数限制 (70)，仍无完整训练完成
2025-08-07 21:40:53,156 - INFO - ✅ rml201801a 优化完成!
2025-08-07 21:40:53,156 - INFO -    最佳准确率: 0.5745
2025-08-07 21:40:53,156 - INFO -    最佳参数: {'dropout': 0.10172189671003123, 'lambda_lifting': 0.013577754816239018}
2025-08-07 21:40:53,159 - INFO - 汇总结果已保存到:
2025-08-07 21:40:53,159 - INFO -   YAML格式: optimization_results_wnn_mrnn_hyperopt_separate/optimization_summary.yaml
2025-08-07 21:40:53,159 - INFO -   JSON格式: optimization_results_wnn_mrnn_hyperopt_separate/optimization_summary.json
2025-08-07 21:40:53,159 - INFO - 
🏆 分别优化最终结果汇总
2025-08-07 21:40:53,159 - INFO - ====================================================================================================
2025-08-07 21:40:53,159 - INFO - 
📊 rml201801a:
2025-08-07 21:40:53,159 - INFO -    最佳准确率: 0.5745
2025-08-07 21:40:53,159 - INFO -    完成试验数: 13
