{"optimization_mode": "separate", "datasets": ["rml201801a"], "n_trials_per_dataset": 20, "total_trials": 20, "study_name": "wnn_mrnn_hyperopt", "optimization_config": {"study_name": "wnn_mrnn_hyperopt", "config_path": "config.yaml", "datasets": ["rml201801a"], "optimization_mode": "separate", "training_epochs": 400, "early_stop_patience": 30, "min_epochs": 2, "n_trials_per_dataset": 20, "initial_second_epoch_thresholds": {"rml": 61.0, "rml201801a": 60, "hisar": 55.0, "torchsig1024": 53.5, "torchsig2048": 55.5, "torchsig4096": 59.5}, "optimize_params": ["dropout", "lambda_lifting"], "skip_on_oom": true, "max_oom_retries": 1, "save_frequency": 1, "enable_database": true, "database_url": "sqlite:///wnn_mrnn_optimization.db"}, "completion_time": "2025-08-07T21:40:53.156464", "best_results_by_dataset": {"rml201801a": {"trial_number": 12, "timestamp": "2025-08-06T06:55:02.583463", "best_params": {"dropout": 0.10172189671003123, "lambda_lifting": 0.013577754816239018}, "best_value": 0.5744762351469669, "dataset": "rml201801a", "total_trials_so_far": 13}}}