[{"trial_number": 0, "timestamp": "2025-08-05T22:27:53.047487", "parameters": {"dropout": 0.29100315496856294, "lambda_lifting": 0.3510096866438333}, "value": 0.5337163852407755, "dataset": "rml201801a", "result": {"best_val_acc": 0.5046904315196998, "best_val_f1": 0.4993416014804006, "best_val_kappa": 0.4831552328901215, "total_epochs": 2, "total_training_time": 2291.774444103241, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5337163852407755}}, {"trial_number": 1, "timestamp": "2025-08-05T23:11:50.099315", "parameters": {"dropout": 0.6192028344071885, "lambda_lifting": 0.4013116754754876}, "value": 0.4809855117781947, "dataset": "rml201801a", "result": {"best_val_acc": 0.4135605586825099, "best_val_f1": 0.4003107947966909, "best_val_kappa": 0.38806319166870595, "total_epochs": 2, "total_training_time": 2289.4453353881836, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.4809855117781947}}, {"trial_number": 2, "timestamp": "2025-08-05T23:56:02.657592", "parameters": {"dropout": 0.33975852577567, "lambda_lifting": 0.4449883445254538}, "value": 0.5194574734208881, "dataset": "rml201801a", "result": {"best_val_acc": 0.48918334375651445, "best_val_f1": 0.47345148097554185, "best_val_kappa": 0.46697392391984116, "total_epochs": 2, "total_training_time": 2293.5682106018066, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5194574734208881}}, {"trial_number": 3, "timestamp": "2025-08-06T00:38:13.363360", "parameters": {"dropout": 0.22965918483841627, "lambda_lifting": 0.5553878352193506}, "value": 0.5202704815509693, "dataset": "rml201801a", "result": {"best_val_acc": 0.5118094642484886, "best_val_f1": 0.5048786225304307, "best_val_kappa": 0.4905837887810317, "total_epochs": 2, "total_training_time": 2291.971033811569, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5202704815509693}}, {"trial_number": 4, "timestamp": "2025-08-06T01:24:12.756830", "parameters": {"dropout": 0.17041612078998825, "lambda_lifting": 0.10770851894149434}, "value": 0.5614212007504691, "dataset": "rml201801a", "result": {"best_val_acc": 0.5073874296435271, "best_val_f1": 0.5059082118938868, "best_val_kappa": 0.4859694918019414, "total_epochs": 2, "total_training_time": 2292.7732117176056, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5614212007504691}}, {"trial_number": 5, "timestamp": "2025-08-06T02:05:02.422950", "parameters": {"dropout": 0.4524924390885482, "lambda_lifting": 0.22565302006075094}, "value": 0.5088857619345424, "dataset": "rml201801a", "result": {"best_val_acc": 0.46945747342088806, "best_val_f1": 0.46654929531483985, "best_val_kappa": 0.44639040704788324, "total_epochs": 2, "total_training_time": 2292.8128066062927, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5088857619345424}}, {"trial_number": 6, "timestamp": "2025-08-06T02:45:30.601598", "parameters": {"dropout": 0.545344740309106, "lambda_lifting": 0.20034056983204745}, "value": 0.4947727746508235, "dataset": "rml201801a", "result": {"best_val_acc": 0.45176151761517613, "best_val_f1": 0.43259851126122967, "best_val_kappa": 0.4279250618593142, "total_epochs": 2, "total_training_time": 2292.847448348999, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.4947727746508235}}, {"trial_number": 7, "timestamp": "2025-08-06T03:28:20.586249", "parameters": {"dropout": 0.5900964442675751, "lambda_lifting": 0.35253770774999277}, "value": 0.4813555347091933, "dataset": "rml201801a", "result": {"best_val_acc": 0.4254377736085053, "best_val_f1": 0.41684883737731254, "best_val_kappa": 0.40045680724365773, "total_epochs": 2, "total_training_time": 2291.80627989769, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.4813555347091933}}, {"trial_number": 8, "timestamp": "2025-08-06T04:11:21.155986", "parameters": {"dropout": 0.6055348952589817, "lambda_lifting": 0.27566253362281073}, "value": 0.4463544923910778, "dataset": "rml201801a", "result": {"best_val_acc": 0.4436522826766729, "best_val_f1": 0.44654705321346944, "best_val_kappa": 0.4194632514887022, "total_epochs": 2, "total_training_time": 2291.832747936249, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.4463544923910778}}, {"trial_number": 9, "timestamp": "2025-08-06T04:53:43.868359", "parameters": {"dropout": 0.24588965130477117, "lambda_lifting": 0.38777172859103726}, "value": 0.5147748592870545, "dataset": "rml201801a", "result": {"best_val_acc": 0.517930477381697, "best_val_f1": 0.5250588379838629, "best_val_kappa": 0.4969709329200316, "total_epochs": 2, "total_training_time": 2291.10076713562, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5147748592870545}}, {"trial_number": 10, "timestamp": "2025-08-06T05:34:10.439351", "parameters": {"dropout": 0.14440589784927163, "lambda_lifting": 0.015854139739503323}, "value": 0.5658692933083177, "dataset": "rml201801a", "result": {"best_val_acc": 0.5278559516364395, "best_val_f1": 0.533979629108302, "best_val_kappa": 0.507327949533676, "total_epochs": 2, "total_training_time": 2289.558680534363, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5658692933083177}}, {"trial_number": 11, "timestamp": "2025-08-06T06:14:36.474901", "parameters": {"dropout": 0.11085351225993631, "lambda_lifting": 0.012639165448392015}, "value": 0.5661168438607462, "dataset": "rml201801a", "result": {"best_val_acc": 0.5256436314363144, "best_val_f1": 0.5282432911003794, "best_val_kappa": 0.5050194414987628, "total_epochs": 2, "total_training_time": 2287.386987686157, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5661168438607462}}, {"trial_number": 12, "timestamp": "2025-08-06T06:55:02.583463", "parameters": {"dropout": 0.10172189671003123, "lambda_lifting": 0.013577754816239018}, "value": 0.5744762351469669, "dataset": "rml201801a", "result": {"best_val_acc": 0.5307770481550969, "best_val_f1": 0.546436191966398, "best_val_kappa": 0.5103760502487968, "total_epochs": 2, "total_training_time": 2289.3132309913635, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5744762351469669}}, {"trial_number": 13, "timestamp": "2025-08-06T07:41:34.259597", "parameters": {"dropout": 0.13077394844648713, "lambda_lifting": 0.01365782966030925}, "value": 0.5619449656035022, "dataset": "rml201801a", "result": {"best_val_acc": 0.5281113195747342, "best_val_f1": 0.5263002943953125, "best_val_kappa": 0.5075944204258096, "total_epochs": 2, "total_training_time": 2292.1193845272064, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5619449656035022}}, {"trial_number": 14, "timestamp": "2025-08-06T08:25:45.305404", "parameters": {"dropout": 0.11584021634636985, "lambda_lifting": 0.11819306668931777}, "value": 0.565462789243277, "dataset": "rml201801a", "result": {"best_val_acc": 0.5151787575568063, "best_val_f1": 0.5170110065641894, "best_val_kappa": 0.49409957310275443, "total_epochs": 2, "total_training_time": 2292.48965549469, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.565462789243277}}, {"trial_number": 15, "timestamp": "2025-08-06T09:06:18.839891", "parameters": {"dropout": 0.42117508708370305, "lambda_lifting": 0.11084364581588171}, "value": 0.5138862830936002, "dataset": "rml201801a", "result": {"best_val_acc": 0.4798858661663539, "best_val_f1": 0.46898519517123916, "best_val_kappa": 0.4572722081735867, "total_epochs": 2, "total_training_time": 2292.81174659729, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5138862830936002}}, {"trial_number": 16, "timestamp": "2025-08-06T09:46:42.577936", "parameters": {"dropout": 0.22203274903391282, "lambda_lifting": 0.06737019642538318}, "value": 0.5503830519074422, "dataset": "rml201801a", "result": {"best_val_acc": 0.508440170940171, "best_val_f1": 0.5055979396376106, "best_val_kappa": 0.48706800445930876, "total_epochs": 2, "total_training_time": 2293.593867301941, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5503830519074422}}, {"trial_number": 17, "timestamp": "2025-08-06T10:27:08.533399", "parameters": {"dropout": 0.34485872018444197, "lambda_lifting": 0.2051766603962944}, "value": 0.5316369606003752, "dataset": "rml201801a", "result": {"best_val_acc": 0.4917682926829269, "best_val_f1": 0.4853560114685214, "best_val_kappa": 0.46967126193001063, "total_epochs": 2, "total_training_time": 2293.6270904541016, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5316369606003752}}, {"trial_number": 18, "timestamp": "2025-08-06T11:07:37.842977", "parameters": {"dropout": 0.4982493119467225, "lambda_lifting": 0.17329349740503833}, "value": 0.5124192203460496, "dataset": "rml201801a", "result": {"best_val_acc": 0.4484208880550344, "best_val_f1": 0.43903401617283366, "best_val_kappa": 0.4244391875356881, "total_epochs": 2, "total_training_time": 2291.1800334453583, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5124192203460496}}, {"trial_number": 19, "timestamp": "2025-08-06T11:48:49.623318", "parameters": {"dropout": 0.6924209643082504, "lambda_lifting": 0.06809196611452875}, "value": 0.4254247446320617, "dataset": "rml201801a", "result": {"best_val_acc": 0.38749478840942253, "best_val_f1": 0.3632678963224105, "best_val_kappa": 0.3608641270359192, "total_epochs": 2, "total_training_time": 2289.538379430771, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.4254247446320617}}, {"trial_number": 20, "timestamp": "2025-08-06T12:36:01.740052", "parameters": {"dropout": 0.18616317405804933, "lambda_lifting": 0.5357172492448996}, "value": 0.5444913487596414, "dataset": "rml201801a", "result": {"best_val_acc": 0.49768865957890346, "best_val_f1": 0.48920145098908, "best_val_kappa": 0.4758490360823341, "total_epochs": 2, "total_training_time": 2292.5402960777283, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5444913487596414}}, {"trial_number": 21, "timestamp": "2025-08-06T13:21:15.633981", "parameters": {"dropout": 0.10255948279039259, "lambda_lifting": 0.024320486227991597}, "value": 0.5666927246195539, "dataset": "rml201801a", "result": {"best_val_acc": 0.5330727538044612, "best_val_f1": 0.5455526520315161, "best_val_kappa": 0.5127715691872639, "total_epochs": 2, "total_training_time": 2290.1887419223785, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5666927246195539}}, {"trial_number": 22, "timestamp": "2025-08-06T14:02:47.105206", "parameters": {"dropout": 0.10826720091836849, "lambda_lifting": 0.0465412101136831}, "value": 0.5673233270794246, "dataset": "rml201801a", "result": {"best_val_acc": 0.5324447571398792, "best_val_f1": 0.5451517255205316, "best_val_kappa": 0.5121162683198739, "total_epochs": 2, "total_training_time": 2289.4169969558716, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5673233270794246}}, {"trial_number": 23, "timestamp": "2025-08-06T14:43:05.842202", "parameters": {"dropout": 0.1940292726535822, "lambda_lifting": 0.07449127104192901}, "value": 0.5622368146758391, "dataset": "rml201801a", "result": {"best_val_acc": 0.4951401917865333, "best_val_f1": 0.49162763679565225, "best_val_kappa": 0.4731897653424695, "total_epochs": 2, "total_training_time": 2287.018234014511, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5622368146758391}}, {"trial_number": 24, "timestamp": "2025-08-06T15:23:27.550743", "parameters": {"dropout": 0.1014707019418255, "lambda_lifting": 0.14844740618744584}, "value": 0.5664425682718366, "dataset": "rml201801a", "result": {"best_val_acc": 0.5090082343131124, "best_val_f1": 0.5055217579808883, "best_val_kappa": 0.4876607662397694, "total_epochs": 2, "total_training_time": 2286.5270788669586, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5664425682718366}}, {"trial_number": 25, "timestamp": "2025-08-06T16:03:44.069423", "parameters": {"dropout": 0.2984542243356675, "lambda_lifting": 0.26270239003648516}, "value": 0.5154706066291432, "dataset": "rml201801a", "result": {"best_val_acc": 0.49391025641025643, "best_val_f1": 0.4873624932447431, "best_val_kappa": 0.47190635451505014, "total_epochs": 2, "total_training_time": 2283.744827747345, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5154706066291432}}, {"trial_number": 26, "timestamp": "2025-08-06T16:44:04.805044", "parameters": {"dropout": 0.15801474603995552, "lambda_lifting": 0.05652996957322522}, "value": 0.563109756097561, "dataset": "rml201801a", "result": {"best_val_acc": 0.5019230769230769, "best_val_f1": 0.5116520069721507, "best_val_kappa": 0.48026755852842806, "total_epochs": 2, "total_training_time": 2288.3174653053284, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.563109756097561}}, {"trial_number": 27, "timestamp": "2025-08-06T17:24:22.240122", "parameters": {"dropout": 0.28545044929670754, "lambda_lifting": 0.14825810294567573}, "value": 0.50723889931207, "dataset": "rml201801a", "result": {"best_val_acc": 0.5056519699812383, "best_val_f1": 0.5135039354535977, "best_val_kappa": 0.48415857737172685, "total_epochs": 2, "total_training_time": 2287.2889637947083, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.50723889931207}}, {"trial_number": 28, "timestamp": "2025-08-06T18:04:44.712404", "parameters": {"dropout": 0.19976922424547017, "lambda_lifting": 0.09623976230807928}, "value": 0.5593365645194913, "dataset": "rml201801a", "result": {"best_val_acc": 0.5171852199291224, "best_val_f1": 0.5211411635798199, "best_val_kappa": 0.496193272969519, "total_epochs": 2, "total_training_time": 2289.3693342208862, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5593365645194913}}, {"trial_number": 29, "timestamp": "2025-08-06T18:45:07.826487", "parameters": {"dropout": 0.30568188285243714, "lambda_lifting": 0.05078182737946532}, "value": 0.5388393787784032, "dataset": "rml201801a", "result": {"best_val_acc": 0.5043568897227434, "best_val_f1": 0.5041249141459261, "best_val_kappa": 0.48280718927590616, "total_epochs": 2, "total_training_time": 2289.637164592743, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5388393787784032}}, {"trial_number": 30, "timestamp": "2025-08-06T19:25:35.145244", "parameters": {"dropout": 0.2574793972678259, "lambda_lifting": 0.14777091408958076}, "value": 0.5100818219720659, "dataset": "rml201801a", "result": {"best_val_acc": 0.5034526787575568, "best_val_f1": 0.5022581343827551, "best_val_kappa": 0.48186366479049403, "total_epochs": 2, "total_training_time": 2294.5029373168945, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5100818219720659}}, {"trial_number": 31, "timestamp": "2025-08-06T20:06:00.688589", "parameters": {"dropout": 0.11845459404772883, "lambda_lifting": 0.04261267189830338}, "value": 0.565322076297686, "dataset": "rml201801a", "result": {"best_val_acc": 0.5400641025641025, "best_val_f1": 0.5376226229090078, "best_val_kappa": 0.520066889632107, "total_epochs": 2, "total_training_time": 2294.118821144104, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.565322076297686}}, {"trial_number": 32, "timestamp": "2025-08-06T20:46:25.241004", "parameters": {"dropout": 0.14423171992910572, "lambda_lifting": 0.13941811039324267}, "value": 0.532960704607046, "dataset": "rml201801a", "result": {"best_val_acc": 0.4948301021471753, "best_val_f1": 0.4992256699582825, "best_val_kappa": 0.4728661935448786, "total_epochs": 2, "total_training_time": 2294.1373114585876, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.532960704607046}}, {"trial_number": 33, "timestamp": "2025-08-06T21:26:53.580880", "parameters": {"dropout": 0.10449021418159912, "lambda_lifting": 0.03607260164657145}, "value": 0.5695747342088806, "dataset": "rml201801a", "result": {"best_val_acc": 0.5365045861997081, "best_val_f1": 0.5480130362996781, "best_val_kappa": 0.5163526116866519, "total_epochs": 2, "total_training_time": 2293.4181411266327, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5695747342088806}}, {"trial_number": 34, "timestamp": "2025-08-06T22:07:17.432146", "parameters": {"dropout": 0.16338082959479072, "lambda_lifting": 0.03299363994159852}, "value": 0.5554018136335209, "dataset": "rml201801a", "result": {"best_val_acc": 0.5069939545549302, "best_val_f1": 0.5110084917858562, "best_val_kappa": 0.4855589091007967, "total_epochs": 2, "total_training_time": 2293.6898822784424, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5554018136335209}}, {"trial_number": 35, "timestamp": "2025-08-06T22:47:45.348299", "parameters": {"dropout": 0.21117186948749847, "lambda_lifting": 0.08518217999094388}, "value": 0.5462189910360642, "dataset": "rml201801a", "result": {"best_val_acc": 0.5069340212632896, "best_val_f1": 0.5043566924200564, "best_val_kappa": 0.48549637001386736, "total_epochs": 2, "total_training_time": 2293.208431005478, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5462189910360642}}, {"trial_number": 36, "timestamp": "2025-08-06T23:28:11.878095", "parameters": {"dropout": 0.1022720418725496, "lambda_lifting": 0.4687391996852591}, "value": 0.5393266624973942, "dataset": "rml201801a", "result": {"best_val_acc": 0.5024728997289973, "best_val_f1": 0.5089081285945772, "best_val_kappa": 0.48084128667373627, "total_epochs": 2, "total_training_time": 2294.404551744461, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5393266624973942}}, {"trial_number": 37, "timestamp": "2025-08-07T00:08:50.092354", "parameters": {"dropout": 0.38096181206222446, "lambda_lifting": 0.03906209628052036}, "value": 0.538362518240567, "dataset": "rml201801a", "result": {"best_val_acc": 0.48721075672295183, "best_val_f1": 0.49190957151530074, "best_val_kappa": 0.46491557223264546, "total_epochs": 2, "total_training_time": 2294.253432035446, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.538362518240567}}, {"trial_number": 38, "timestamp": "2025-08-07T00:49:20.659313", "parameters": {"dropout": 0.1719078396497284, "lambda_lifting": 0.10123719011661961}, "value": 0.5619423598082135, "dataset": "rml201801a", "result": {"best_val_acc": 0.5205545132374401, "best_val_f1": 0.5368329994200205, "best_val_kappa": 0.4997090572912418, "total_epochs": 2, "total_training_time": 2294.051414489746, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5619423598082135}}, {"trial_number": 39, "timestamp": "2025-08-07T01:29:43.532499", "parameters": {"dropout": 0.14474624850597775, "lambda_lifting": 0.18777392496901543}, "value": 0.5363795080258494, "dataset": "rml201801a", "result": {"best_val_acc": 0.5105039608088389, "best_val_f1": 0.5128466237776682, "best_val_kappa": 0.4892215243222666, "total_epochs": 2, "total_training_time": 2293.9995579719543, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5363795080258494}}, {"trial_number": 40, "timestamp": "2025-08-07T02:10:07.509550", "parameters": {"dropout": 0.25389021178032817, "lambda_lifting": 0.32863227212284596}, "value": 0.5161872003335418, "dataset": "rml201801a", "result": {"best_val_acc": 0.5053314571607255, "best_val_f1": 0.5107543550849784, "best_val_kappa": 0.4838241292111918, "total_epochs": 2, "total_training_time": 2293.9159622192383, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5161872003335418}}, {"trial_number": 41, "timestamp": "2025-08-07T02:50:35.216745", "parameters": {"dropout": 0.10191609533451637, "lambda_lifting": 0.015050946563206397}, "value": 0.5608713779445487, "dataset": "rml201801a", "result": {"best_val_acc": 0.5375990202209714, "best_val_f1": 0.5442781144460104, "best_val_kappa": 0.5174946297957963, "total_epochs": 2, "total_training_time": 2293.073368549347, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5608713779445487}}, {"trial_number": 42, "timestamp": "2025-08-07T03:31:02.991524", "parameters": {"dropout": 0.10016052716670265, "lambda_lifting": 0.1291881721851967}, "value": 0.564037419220346, "dataset": "rml201801a", "result": {"best_val_acc": 0.5161220554513237, "best_val_f1": 0.513877980311116, "best_val_kappa": 0.49508388394920744, "total_epochs": 2, "total_training_time": 2296.075948238373, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.564037419220346}}, {"trial_number": 43, "timestamp": "2025-08-07T04:11:41.427929", "parameters": {"dropout": 0.1711852970963315, "lambda_lifting": 0.23400500248127906}, "value": 0.5513706483218678, "dataset": "rml201801a", "result": {"best_val_acc": 0.4813946216385241, "best_val_f1": 0.4851711266354029, "best_val_kappa": 0.45884656170976423, "total_epochs": 2, "total_training_time": 2294.2880115509033, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5513706483218678}}, {"trial_number": 44, "timestamp": "2025-08-07T04:51:59.396750", "parameters": {"dropout": 0.1365209946341168, "lambda_lifting": 0.0781031067996393}, "value": 0.5596727121117365, "dataset": "rml201801a", "result": {"best_val_acc": 0.5181311236189284, "best_val_f1": 0.5295858658931837, "best_val_kappa": 0.497180302906708, "total_epochs": 2, "total_training_time": 2285.649521112442, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5596727121117365}}, {"trial_number": 45, "timestamp": "2025-08-07T05:32:19.743178", "parameters": {"dropout": 0.14002201609343332, "lambda_lifting": 0.16398704057018001}, "value": 0.5601104857202418, "dataset": "rml201801a", "result": {"best_val_acc": 0.5138002918490724, "best_val_f1": 0.5132337016718682, "best_val_kappa": 0.49266117410337984, "total_epochs": 2, "total_training_time": 2287.194304227829, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5601104857202418}}, {"trial_number": 46, "timestamp": "2025-08-07T06:12:42.051078", "parameters": {"dropout": 0.2271043244941432, "lambda_lifting": 0.028148458025824365}, "value": 0.5557301438398999, "dataset": "rml201801a", "result": {"best_val_acc": 0.5007348342714196, "best_val_f1": 0.5083737970139656, "best_val_kappa": 0.4790276531527857, "total_epochs": 2, "total_training_time": 2286.3836557865143, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5557301438398999}}, {"trial_number": 47, "timestamp": "2025-08-07T06:52:58.862567", "parameters": {"dropout": 0.18277188872221478, "lambda_lifting": 0.10626313350547034}, "value": 0.5665676464456952, "dataset": "rml201801a", "result": {"best_val_acc": 0.5101964769647697, "best_val_f1": 0.511630468890018, "best_val_kappa": 0.48890067161541184, "total_epochs": 2, "total_training_time": 2286.0517551898956, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5665676464456952}}, {"trial_number": 48, "timestamp": "2025-08-07T07:33:18.206762", "parameters": {"dropout": 0.19509223398316874, "lambda_lifting": 0.055877746479826074}, "value": 0.5487935167813217, "dataset": "rml201801a", "result": {"best_val_acc": 0.4920627475505524, "best_val_f1": 0.485829396043808, "best_val_kappa": 0.4699785191831851, "total_epochs": 2, "total_training_time": 2289.7993590831757, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5487935167813217}}, {"trial_number": 49, "timestamp": "2025-08-07T08:13:39.165587", "parameters": {"dropout": 0.1310895904509244, "lambda_lifting": 0.11212562900677545}, "value": 0.5667396289347508, "dataset": "rml201801a", "result": {"best_val_acc": 0.5236840733791953, "best_val_f1": 0.5225476082168743, "best_val_kappa": 0.5029746852652472, "total_epochs": 2, "total_training_time": 2289.6540529727936, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5667396289347508}}, {"trial_number": 50, "timestamp": "2025-08-07T08:54:00.596277", "parameters": {"dropout": 0.12741684389735686, "lambda_lifting": 0.013054893507154788}, "value": 0.5687695434646654, "dataset": "rml201801a", "result": {"best_val_acc": 0.5324682092974775, "best_val_f1": 0.5406304611031802, "best_val_kappa": 0.5121407401364984, "total_epochs": 2, "total_training_time": 2289.255879163742, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5687695434646654}}, {"trial_number": 51, "timestamp": "2025-08-07T09:34:20.438486", "parameters": {"dropout": 0.1344342278629412, "lambda_lifting": 0.013304180223409408}, "value": 0.5690196998123828, "dataset": "rml201801a", "result": {"best_val_acc": 0.5317307692307692, "best_val_f1": 0.5323349720497641, "best_val_kappa": 0.511371237458194, "total_epochs": 2, "total_training_time": 2286.315858364105, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5690196998123828}}, {"trial_number": 52, "timestamp": "2025-08-07T10:15:10.197960", "parameters": {"dropout": 0.12832195825255127, "lambda_lifting": 0.010156387825911283}, "value": 0.5590342922659997, "dataset": "rml201801a", "result": {"best_val_acc": 0.5329711277882009, "best_val_f1": 0.5315094289553303, "best_val_kappa": 0.5126655246485575, "total_epochs": 2, "total_training_time": 2287.8273923397064, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5590342922659997}}, {"trial_number": 53, "timestamp": "2025-08-07T10:55:30.899608", "parameters": {"dropout": 0.16010355602949888, "lambda_lifting": 0.04700537880914389}, "value": 0.5565509693558474, "dataset": "rml201801a", "result": {"best_val_acc": 0.5141781321659371, "best_val_f1": 0.529722432981968, "best_val_kappa": 0.4930554422601082, "total_epochs": 2, "total_training_time": 2289.975611448288, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5565509693558474}}, {"trial_number": 54, "timestamp": "2025-08-07T11:35:47.571520", "parameters": {"dropout": 0.12950093763627754, "lambda_lifting": 0.08561555545374508}, "value": 0.5662966437356681, "dataset": "rml201801a", "result": {"best_val_acc": 0.5253491765686887, "best_val_f1": 0.529667327343759, "best_val_kappa": 0.5047121842455883, "total_epochs": 2, "total_training_time": 2287.5439813137054, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5662966437356681}}, {"trial_number": 55, "timestamp": "2025-08-07T12:16:04.024513", "parameters": {"dropout": 0.15245658413941912, "lambda_lifting": 0.06662230450916695}, "value": 0.568764331874088, "dataset": "rml201801a", "result": {"best_val_acc": 0.5243303106107985, "best_val_f1": 0.5386962243802463, "best_val_kappa": 0.5036490197677896, "total_epochs": 2, "total_training_time": 2288.7407364845276, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.568764331874088}}, {"trial_number": 56, "timestamp": "2025-08-07T12:56:22.816568", "parameters": {"dropout": 0.24220247315200666, "lambda_lifting": 0.06337371021563133}, "value": 0.5532885136543674, "dataset": "rml201801a", "result": {"best_val_acc": 0.509331352928914, "best_val_f1": 0.5051625663050324, "best_val_kappa": 0.4879979334910406, "total_epochs": 2, "total_training_time": 2286.402074813843, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5532885136543674}}, {"trial_number": 57, "timestamp": "2025-08-07T13:36:46.963163", "parameters": {"dropout": 0.5080267364561007, "lambda_lifting": 0.033403583141651615}, "value": 0.5162314988534501, "dataset": "rml201801a", "result": {"best_val_acc": 0.4647826766729206, "best_val_f1": 0.4641506051441214, "best_val_kappa": 0.44151235826739543, "total_epochs": 2, "total_training_time": 2289.7030005455017, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5162314988534501}}, {"trial_number": 58, "timestamp": "2025-08-07T14:17:05.807676", "parameters": {"dropout": 0.15763994712200607, "lambda_lifting": 0.3945247298444988}, "value": 0.5050161559307901, "dataset": "rml201801a", "result": {"best_val_acc": 0.5042787158640817, "best_val_f1": 0.5060974655006388, "best_val_kappa": 0.4827256165538244, "total_epochs": 2, "total_training_time": 2290.346328496933, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5050161559307901}}, {"trial_number": 59, "timestamp": "2025-08-07T14:57:22.728130", "parameters": {"dropout": 0.6424573818865047, "lambda_lifting": 0.4421293090794793}, "value": 0.46508494892641233, "dataset": "rml201801a", "result": {"best_val_acc": 0.4157363977485929, "best_val_f1": 0.39302566431522257, "best_val_kappa": 0.3903336324333143, "total_epochs": 2, "total_training_time": 2289.018721342087, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.46508494892641233}}, {"trial_number": 60, "timestamp": "2025-08-07T15:37:44.759380", "parameters": {"dropout": 0.20410027800044608, "lambda_lifting": 0.5839307884178822}, "value": 0.5315535751511361, "dataset": "rml201801a", "result": {"best_val_acc": 0.49873358348968105, "best_val_f1": 0.49594973430660955, "best_val_kappa": 0.4769393914674933, "total_epochs": 2, "total_training_time": 2290.6929161548615, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5315535751511361}}, {"trial_number": 61, "timestamp": "2025-08-07T16:18:02.864029", "parameters": {"dropout": 0.12368540961529925, "lambda_lifting": 0.11572935460085382}, "value": 0.5621091307066917, "dataset": "rml201801a", "result": {"best_val_acc": 0.5217583906608297, "best_val_f1": 0.5251397764226639, "best_val_kappa": 0.5009652772113005, "total_epochs": 2, "total_training_time": 2290.788955926895, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5621091307066917}}, {"trial_number": 62, "timestamp": "2025-08-07T16:58:25.011975", "parameters": {"dropout": 0.15441035755564353, "lambda_lifting": 0.01049470085955723}, "value": 0.5549536168438608, "dataset": "rml201801a", "result": {"best_val_acc": 0.5217401500938087, "best_val_f1": 0.5328679927350386, "best_val_kappa": 0.5009462435761481, "total_epochs": 2, "total_training_time": 2288.581072807312, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5549536168438608}}, {"trial_number": 63, "timestamp": "2025-08-07T17:38:43.199296", "parameters": {"dropout": 0.12615707431622902, "lambda_lifting": 0.07462804400066944}, "value": 0.5682457786116323, "dataset": "rml201801a", "result": {"best_val_acc": 0.5196190327287888, "best_val_f1": 0.5238757236764241, "best_val_kappa": 0.4987329037169971, "total_epochs": 2, "total_training_time": 2290.099354028702, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5682457786116323}}, {"trial_number": 64, "timestamp": "2025-08-07T18:19:01.279443", "parameters": {"dropout": 0.1752562829289379, "lambda_lifting": 0.07251150780454019}, "value": 0.5547373358348968, "dataset": "rml201801a", "result": {"best_val_acc": 0.5115827600583698, "best_val_f1": 0.5131969203707824, "best_val_kappa": 0.4903472278869946, "total_epochs": 2, "total_training_time": 2290.0757925510406, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5547373358348968}}, {"trial_number": 65, "timestamp": "2025-08-07T18:59:26.015091", "parameters": {"dropout": 0.11870833252396801, "lambda_lifting": 0.049540262211959674}, "value": 0.5591854283927454, "dataset": "rml201801a", "result": {"best_val_acc": 0.5255550343964979, "best_val_f1": 0.5352875354855047, "best_val_kappa": 0.5049269924137368, "total_epochs": 2, "total_training_time": 2286.735491991043, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5591854283927454}}, {"trial_number": 66, "timestamp": "2025-08-07T19:39:42.452478", "parameters": {"dropout": 0.14523725492720777, "lambda_lifting": 0.090667641265434}, "value": 0.565116218469877, "dataset": "rml201801a", "result": {"best_val_acc": 0.5115228267667292, "best_val_f1": 0.5225208592177285, "best_val_kappa": 0.49028468880006537, "total_epochs": 2, "total_training_time": 2286.715566635132, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.565116218469877}}, {"trial_number": 67, "timestamp": "2025-08-07T20:20:06.502382", "parameters": {"dropout": 0.21624056892189708, "lambda_lifting": 0.03046533544900941}, "value": 0.5379977069001459, "dataset": "rml201801a", "result": {"best_val_acc": 0.4924822805920367, "best_val_f1": 0.4905636919772081, "best_val_kappa": 0.4704162927916905, "total_epochs": 2, "total_training_time": 2289.7614760398865, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5379977069001459}}, {"trial_number": 68, "timestamp": "2025-08-07T21:00:30.670275", "parameters": {"dropout": 0.12062370209382149, "lambda_lifting": 0.07043385682151337}, "value": 0.566541588492808, "dataset": "rml201801a", "result": {"best_val_acc": 0.5308187408797165, "best_val_f1": 0.5360696335811995, "best_val_kappa": 0.5104195557005737, "total_epochs": 2, "total_training_time": 2290.296011209488, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.566541588492808}}, {"trial_number": 69, "timestamp": "2025-08-07T21:40:52.833799", "parameters": {"dropout": 0.18592293245513836, "lambda_lifting": 0.05544697552727458}, "value": 0.5520872420262664, "dataset": "rml201801a", "result": {"best_val_acc": 0.4854831144465291, "best_val_f1": 0.4813388959883582, "best_val_kappa": 0.4631128150746391, "total_epochs": 2, "total_training_time": 2288.724184989929, "trainable_parameters": 867608, "early_stopped": true, "second_epoch_acc": 0.5520872420262664}}]